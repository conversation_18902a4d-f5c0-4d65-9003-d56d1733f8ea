// 获取应用实例
var util = require('../../../utils/util')
var api = require('../../../config/api.js')
var app = getApp()
import { PLATE_COLOR } from '../../../utils/dict'

Page({
  data: {
    index_type: 0, //车辆类型的索引
    index_colour: 0, //车辆颜色的索引
    vehicleType: [],
    vehicleColour: [],
    defaultVehicle: false,
    carList: [],
    isKeyboard: false,
    isNumberKB: true,
    tapNum: false,
    showNewPower: true, //是否显示‘新’
    // keyboardNumber: "1234567890ABCDEFGHJKLMNPQRSTUVWXYZ港澳学",
    keyboardNumber: '1234567890QWERTYUP港澳ASDFGHJKL学ZXCVBNM',
    keyboard1: '渝川京沪粤津冀晋蒙辽吉黑苏浙皖闽赣鲁豫鄂湘桂琼贵云藏陕甘青宁新',
    inputPlates: {
      index0: '桂',
      index1: '',
      index2: '',
      index3: '',
      index4: '',
      index5: '',
      index6: '',
      index7: '',
    },
    inputOnFocusIndex: '',
    carNum: '',
    plateNo: '', //用于删除提示
    isAuth: false,
    contentTitle: '我的车辆',
  },
  onLoad(options) {
    this.setData({
      isAuth: options.isAuth,
      contentTitle: options.isAuth ? '车辆认证' : '我的车辆',
    })
  },
  onShow() {
    if (app.globalData.isLoginBack_mycar) {
      app.globalData.isLoginBack_mycar = false
    } else {
      if (app.globalData.userInfo.loginStatus != 'TEMP_LOGIN') {
        this.getCarList()
      }
      this.getVehicleColour()
      this.getVehicleType()
    }
    this.popup = this.selectComponent('#dialog') //获取
  },
  getVehicleColour() {
    var that = this
    util.showLoading('正在加载…')
    util.request(api.getVehicleColour, {}, 'GET').then(function (res) {
      if (res.code == '0') {
        const list = res.result.map(item => {
          item.title = PLATE_COLOR[item.vehicleColourType] ? PLATE_COLOR[item.vehicleColourType] : item.title
          return item
        })
        that.setData({
          vehicleColour: list,
        })
        wx.hideLoading()
      } else {
        wx.hideLoading()
        util.showToast(res.message)
      }
    })
  },
  bindPickerChange_colour: function (e) {
    this.setData({
      index_colour: e.detail.value,
    })
  },
  getVehicleType() {
    var that = this
    util.showLoading('正在加载…')
    util.request(api.getVehicleType, {}, 'GET').then(function (res) {
      if (res.code == '0') {
        that.setData({
          vehicleType: res.result,
        })
        wx.hideLoading()
      } else {
        wx.hideLoading()
        util.showToast(res.message)
      }
    })
  },
  bindPickerChange_type: function (e) {
    this.setData({
      index_type: e.detail.value,
    })
  },
  getCarList() {
    var that = this
    util.showLoading('正在加载…')
    util.request(api.getCarList, {}, 'POST').then(function (res) {
      if (res.code == '0') {
        that.setData({
          carList: res.result,
        })
        wx.hideLoading()
      } else {
        wx.hideLoading()
        util.showToast(res.message)
      }
    })
  },
  isVehicleNumber(vehicleNumber) {
    var xxreg =
      /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}(([0-9]{5}[DABCEFGHJK]$)|([DABCEFGHJK][A-HJ-NP-Z0-9][0-9]{4}$))/ // 2021年新能源车牌不止有DF
    var creg =
      /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}[A-HJ-NP-Z0-9]{4}[A-HJ-NP-Z0-9挂学警港澳]{1}$/
    if (vehicleNumber.length == 7) {
      return creg.test(vehicleNumber)
    } else if (vehicleNumber.length == 8) {
      return xxreg.test(vehicleNumber)
    } else {
      return false
    }
  },
  vehicleAuthentication(e) {
    //去认证
    var dataset = e.currentTarget.dataset
    var id = dataset.id
    var carnum = dataset.carnum
    var type = dataset.type
    var that = this
    util.showLoading('正在加载…')
    if (type == '0') {
      //当状态是未认证的时候，要去查是否可以进行认证
      util.request(api.isAuthenticationable + '/' + id, {}, 'GET').then(function (res) {
        if (res.code == '0') {
          wx.hideLoading()
          var reviewable = res.result.reviewable //0-不可认证，已被他人认证成功或认证中 1-可认证的，未被他人认证成功或认证中
          if (reviewable == 0) {
            wx.showModal({
              title: '提示',
              content: res.result.explain,
              success(res) {},
            })
            //隐藏掉去认证的按钮
            var index = dataset.index
            var keyname = 'carList[' + index + '].hide'
            var param = {}
            param[keyname] = true
            that.setData(param)
          } else {
            //可认证
            that.goNextPage(id, carnum, type)
          }
        } else {
          util.showToast(res.message)
        }
      })
    } else {
      that.goNextPage(id, carnum, type)
    }
  },
  goNextPage(id, carnum, type) {
    if (type == '0') {
      wx.navigateTo({
        url: '/pages/my/myCar/add/vehicleCertification/index?id=' + id + '&carNum=' + carnum + '&operate=add',
      })
    } else {
      wx.navigateTo({
        url: '/pages/my/myCar/add/vehicleCertification/details?id=' + id + '&carNum=' + carnum + '&type=' + type,
      })
    }
  },
  setAcquiesceVehicle(vehicleId) {
    var that = this
    util
      .request(
        api.setAcquiesceVehicle,
        {
          vehicleId: vehicleId,
        },
        'POST'
      )
      .then(function (res) {
        if (res.code == '0') {
          util.showToast('默认车辆设置成功')
          that.getCarList()
        } else {
          util.showToast(res.message)
        }
      })
  },
  addCar() {
    var that = this
    var data = that.data
    var carNum = data.carNum
    if (that.isVehicleNumber(carNum)) {
      //校验车牌号
      if (app.globalData.userInfo.loginStatus == 'TEMP_LOGIN') {
        //登陆后才可以添加车辆
        that.setData({
          isKeyboard: false,
        })
        wx.navigateTo({
          url: '/pages/common/loginTip/loginTip?tiptype=0',
        })
        return
      }
      that.jumpProcess()
    } else {
      wx.hideLoading()
      util.showToast('输入的车牌号不正确！')
    }
  },
  jumpProcess: function () {
    util.showLoading('正在加载…')
    var that = this
    var data = that.data
    var carNum = data.carNum
    // var vehicleType = data.vehicleType[data.index_type]['vehicleType']; 12.5去掉车辆类型
    var vehicleColourType = data.vehicleColour[data.index_colour]['vehicleColourType']
    util
      .request(
        api.addCar,
        {
          plateNo: carNum,
          defaultVehicle: data.defaultVehicle,
          vehicleColourType: vehicleColourType,
          vehicleType: '', //12.5去掉车辆类型
        },
        'POST'
      )
      .then(function (res) {
        if (res.code == '0') {
          wx.hideLoading()
          util.showToast('添加成功！')
          var clock = setTimeout(function () {
            that.getCarList()
            that.setData({
              flag: true,
              inputPlates: {
                index0: '桂',
                index1: '',
                index2: '',
                index3: '',
                index4: '',
                index5: '',
                index6: '',
                index7: '',
              },
            })
            clearInterval(clock)
          }, 500)
        } else {
          wx.hideLoading()
          util.showToast(res.message)
        }
      })
  },
  inputClick: function (t) {
    //点击车牌输入框
    var that = this
    var id = t.target.dataset.id
    if (id == 0) {
      that.setData({
        inputOnFocusIndex: id,
        isNumberKB: true,
        isKeyboard: true,
        tapNum: false,
      })
    } else if (id == 1) {
      that.setData({
        inputOnFocusIndex: id,
        isNumberKB: false,
        isKeyboard: true,
        tapNum: false,
      })
    } else if (id == 7) {
      that.setData({
        inputOnFocusIndex: id,
        isNumberKB: false,
        isKeyboard: true,
        tapNum: true,
        showNewPower: false,
      })
    } else if (id > 1) {
      that.setData({
        inputOnFocusIndex: id,
        isNumberKB: false,
        isKeyboard: true,
        tapNum: true,
      })
    }
  },
  tapKeyboard: function (t) {
    //键盘点击事件
    t.target.dataset.index
    var a = t.target.dataset.val
    switch (parseInt(this.data.inputOnFocusIndex)) {
      case 0:
        this.setData({
          'inputPlates.index0': a,
          inputOnFocusIndex: '1',
        })
        break
      case 1:
        this.setData({
          'inputPlates.index1': a,
          inputOnFocusIndex: '2',
        })
        break
      case 2:
        this.setData({
          'inputPlates.index2': a,
          inputOnFocusIndex: '3',
        })
        break
      case 3:
        this.setData({
          'inputPlates.index3': a,
          inputOnFocusIndex: '4',
        })
        break
      case 4:
        this.setData({
          'inputPlates.index4': a,
          inputOnFocusIndex: '5',
        })
        break
      case 5:
        this.setData({
          'inputPlates.index5': a,
          inputOnFocusIndex: '6',
        })
        break
      case 6:
        this.setData({
          'inputPlates.index6': a,
          inputOnFocusIndex: '7',
        })
        break
      case 7:
        this.setData({
          'inputPlates.index7': a,
          inputOnFocusIndex: '7',
          showNewPower: false,
        })
    }
    var n =
      this.data.inputPlates.index0 +
      this.data.inputPlates.index1 +
      this.data.inputPlates.index2 +
      this.data.inputPlates.index3 +
      this.data.inputPlates.index4 +
      this.data.inputPlates.index5 +
      this.data.inputPlates.index6 +
      this.data.inputPlates.index7
    console.log('车牌号:', n)
    this.data.carNum = n
    this.checkedKeyboard()
  },
  //点击键盘上的完成按钮
  tapSpecBtna: function (t) {
    var that = this
    that.setData({
      isKeyboard: false,
      inputOnFocusIndex: '7',
    })
  },
  //键盘删除按钮点击事件
  tapSpecBtn: function (t) {
    var data = this.data
    console.log(t)
    console.log(t.target.dataset.index)
    switch (parseInt(data.inputOnFocusIndex)) {
      case 0:
        this.setData({
          'inputPlates.index0': '',
          inputOnFocusIndex: '0',
        })
        break
      case 1:
        this.setData({
          'inputPlates.index1': '',
          inputOnFocusIndex: '0',
        })
        break
      case 2:
        this.setData({
          'inputPlates.index2': '',
          inputOnFocusIndex: '1',
        })
        break
      case 3:
        this.setData({
          'inputPlates.index3': '',
          inputOnFocusIndex: '2',
        })
        break
      case 4:
        this.setData({
          'inputPlates.index4': '',
          inputOnFocusIndex: '3',
        })
        break
      case 5:
        this.setData({
          'inputPlates.index5': '',
          inputOnFocusIndex: '4',
        })
        break
      case 6:
        this.setData({
          'inputPlates.index6': '',
          inputOnFocusIndex: '5',
        })
        break
      case 7:
        this.setData({
          'inputPlates.index7': '',
          inputOnFocusIndex: '6',
        })
    }
    this.checkedKeyboard()
    var inputPlates = data.inputPlates
    var n =
      inputPlates.index0 +
      inputPlates.index1 +
      inputPlates.index2 +
      inputPlates.index3 +
      inputPlates.index4 +
      inputPlates.index5 +
      inputPlates.index6 +
      inputPlates.index7
    this.setData({
      'carNum': n,
    })
  },
  checkedKeyboard: function () {
    //键盘切换
    var t = this
    var inputOnFocusIndex = this.data.inputOnFocusIndex
    if (inputOnFocusIndex == 0) {
      t.setData({
        tapNum: false, //是否可以点击
        isNumberKB: true, //省和
      })
    }
    if (inputOnFocusIndex == 1) {
      t.setData({
        tapNum: false,
        isNumberKB: false,
      })
    }
    if (inputOnFocusIndex > 1) {
      t.setData({
        tapNum: true,
        isNumberKB: false,
      })
    }
  },
  changeRange2(e) {
    var _this = this
    var vehicleId = e.currentTarget.dataset.id
    var name = e.currentTarget.dataset.name
    _this.setData({
      vehicleId: vehicleId,
      plateNo: name,
    })
    _this.popup.changeRange() //调用子组件内的函数
  },
  selectItem(e) {
    var that = this
    var vehicleId = that.data.vehicleId
    if (e.detail == 0) {
      //设置为默认车辆
      that.setAcquiesceVehicle(vehicleId)
    } else {
      //删除车辆
      wx.showModal({
        title: '删除提醒',
        content: '您确定要删除' + that.data.plateNo + '吗? 该车无欠费信息即可删除',
        success(res) {
          if (res.confirm) {
            console.log('用户点击确定')
            that.delVehicle(vehicleId)
          } else if (res.cancel) {
            console.log('用户点击取消')
          }
        },
      })
    }
  },
  delVehicle(vehicleId) {
    var that = this
    util
      .request(
        api.delVehicle,
        {
          vehicleId: vehicleId,
        },
        'POST'
      )
      .then(function (res) {
        if (res.code == '0') {
          util.showToast('删除成功！')
          that.getCarList()
          that.setData({
            flag: true,
            inputPlates: {
              index0: '桂',
              index1: '',
              index2: '',
              index3: '',
              index4: '',
              index5: '',
              index6: '',
              index7: '',
            },
          })
        } else {
          util.showToast(res.message)
        }
      })
  },
  goAddCarPage() {
    wx.navigateTo({
      url: '/pages/my/myCar/add/index',
    })
  },
  switchChange: function (e) {
    this.setData({
      defaultVehicle: e.detail.value,
    })
  },
  goPageMethod() {
    //登陆后跳转到这个方法
    app.globalData.isLoginBack_mycar = true
    this.jumpProcess()
    wx.navigateBack()
  },
})
