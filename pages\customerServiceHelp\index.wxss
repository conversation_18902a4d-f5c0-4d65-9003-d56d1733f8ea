@import "../common/index.wxss";
@import "../common/common.wxss";

page {
  background: #F8F8F8;
  height: 100hv;
}
.container{
  position: relative;
  height: 100vh;
  background: linear-gradient(180deg, #E1EBFE 0%, #FFFFFF 100%);
}
.header{
  position: relative;
  width: 100%;
  height: 158rpx;
  line-height: 158rpx;
  text-indent: 60rpx;
  font-size: 48rpx;
  font-weight: bold;
  color: #353535;
}
.header .icon{
  position: absolute;
  top: 27rpx;
  right: 96rpx;
  width: 125rpx;
  height: 142rpx;
}
.list {
  width: calc("100%-100rpx");
  background: #FFFFFF;
  box-shadow: 0rpx 10rpx 20rpx 0rpx rgba(228, 234, 248, 0.5);
  border-radius: 20rpx;
  margin-left: 50rpx;
  margin-right: 50rpx;
  margin-bottom: calc(40rpx + constant(safe-area-inset-bottom));
  /*兼容 IOS<11.2*/
  margin-bottom: calc(40rpx + env(safe-area-inset-bottom));
  /*兼容 IOS>11.2*/
}
.navigator{
  padding: 30rpx 0;
  margin: 0 30rpx;
  height: 109rpx;
  box-sizing: border-box;
  border-bottom: 1rpx solid #DFDFDF;
}
.navigator:last-child{
  border-width: 0;
}
.navigator-text {
  font-size: 26rpx;
  font-weight: bold;
  color: #353535;
  font-size: 34rpx;
  color: #353535;
}
.footer{
  position: absolute;
  bottom: 94rpx;
  display: flex;
  justify-content: center;
  width: 100%;
  line-height: 40rpx;
}

.footer .icon{
  margin-right: 15rpx;
  width: 40rpx;
  height: 40rpx;
}