<!--pages/supplementaryPayment/details/index.wxml-->
<view class="saveOutView">
  <cu-custom bgColor="white-bg" contentTitle="我的月卡" isBack="true"></cu-custom>
  <view class="details-box p">
    <view class="info-bt rowView">
      <view class="icon-sjx-left-tilte"></view>
      <view class="info-bt-text">包月信息</view>
    </view>
    <view class="t pl">{{info.parkName}}</view>
    <view class="n pl">{{info.plateNo}}</view>
    <view class="n pl">{{info.startTime}}至{{info.endTime}}</view>
    <view class="stateBox a{{info.bagState}}">{{info.bagStateText}}</view>
    <view class="rowView">
      <view class="btn w f" bindtap="renewImmediately" id="{{info.id}}" wx:if="{{info.renewable==1}}">立即续费</view>
      <view class="btn w f" bindtap="reProcessing" id="{{info.id}}" wx:if="{{info.rebagable==1}}">再次办理</view>
      <view class="btn z {{info.rebagable!=1&&info.renewable!=1?'f':''}}" wx:if="{{info.bagState==1&&bagMyself==1}}" bindtap="goRefund" >取消包月</view>
    </view>
  </view>
  <scroll-view class="scrollViewSa" scroll-y="true">
    <view class="details-box p">
      <view class="info-bt rowView">
        <view class="icon-sjx-left-tilte"></view>
        <view class="info-bt-text">包月记录</view>
      </view>
      <view wx:if="{{bagMyself==0}}">
        <image src="../../../image/qfjf-null.png" class="nullImg"></image>
        <view class="nullTip" style="margin: 0 10rpx;">非办理用户</view>
        <view class="nullTip" style="margin-top: 10rpx;">无记录查看权限</view>
      </view>
      <view wx:else>
        <block wx:for="{{info.orders}}" wx:key="index">
          <view class="rowView {{index>0?'line':''}}">
            <view class="ft black">{{item.title}}</view>
            <view class="ft {{item.orderType==1?'green':'red'}} flex1">{{item.amountDesc}}</view>
          </view>
          <view wx:if="{{item.orderType==1}}">
            <view class="rowView">
              <view class="ft">包月规则：</view>
              <view class="ft ">{{item.ruleName}}</view>
            </view>
            <view class="rowView">
              <view class="ft">包月时间：</view>
              <view class="ft ">{{item.validTime}}</view>
            </view>
            <view class="rowView">
              <view class="ft">支付方式：</view>
              <view class="ft ">{{item.payTypeText}}</view>
            </view>
            <view class="rowView">
              <view class="ft">支付时间：</view>
              <view class="ft ">{{item.orderTime}}</view>
            </view>
          </view>
          <view wx:else>
            <view class="rowView">
              <view class="ft">退款时间：</view>
              <view class="ft ">{{item.orderTime}}</view>
            </view>
          </view>
        </block>
      </view>
    </view>
  </scroll-view>
</view>
<monthlySubscriptionRules isShow="{{isShowRules}}" rules="{{rules}}" bind:selectRule="selectRule"></monthlySubscriptionRules>