page {
  background-color: #F6F7FB;
}

.container {
  width: 100%;
}

.header {
  margin-top: 10rpx;
  width: 100%;
  height: 80rpx;
  background-color: #ffffff;
  display: flex;
  align-items: center;
}

.header .order-type {
  margin-left: 40rpx;
  width: 136rpx;
  height: 48rpx;
  font-size: 34rpx;
  font-weight: bold;
  color: #7E7E7E;
  line-height: 48rpx;
}

.header .order-type::after {
  content: '';
  display: block;
  margin-left: 38rpx;
  width: 60rpx;
  height: 8rpx;
  background: transparent;
  border-radius: 4rpx 4rpx 4rpx 4rpx;
}

.header .order-type.active {
  color: #000000;
}

.header .order-type.active::after {
  background: #41E0AC;
}

.header .invoice-state {
  margin-left: 34rpx;
  margin-right: 8rpx;
  height: 40rpx;
  font-size: 28rpx;
  color: #7E7E7E;
}

.header .invoice-state.active {
  color: #41E0AC;
}

.footer {
  padding: 0 33rpx;
  width: 100%;
  height: 211rpx;
  background-color: #F6F6F6;
  box-sizing: border-box;
  font-size: 26rpx;
  font-weight: 400;
}

.footer .total {
  width: 100%;
  min-height: 50rpx;
  line-height: 38rpx;
  border-bottom: 1rpx solid #7E7E7E;
  font-size: 26rpx;
  color: #7E7E7E;
}

.footer .total text {
  color: #DA5937;
}

.footer .control {
  padding: 37rpx 0;
  width: 100%;
  display: flex;
  flex-direction: row;
  align-items: center;
}

.footer .control .checkbox {
  margin-right: 40rpx;
  width: 152rpx;
  height: 37rpx;
  font-size: 26rpx;
  color: #353535;
  display: flex;
  align-items: center;
}

.footer .control .checkbox image {
  margin-top: 2rpx;
  margin-right: 10rpx;
  width: 32rpx;
  height: 32rpx;
}

.footer .control .btn {
  width: 156rpx;
  height: 64rpx;


  background: linear-gradient(90deg, #458BFF 0%, #08D7AE 100%);
  box-shadow: 0rpx 3rpx 10rpx 1rpx rgba(37, 107, 245, 0.2);
  border-radius: 40rpx;
  text-align: center;
  font-size: 30rpx;
  color: #FFFFFF;
  line-height: 64rpx;
}

.order {
  padding: 30rpx 20rpx 20rpx 20rpx;
  margin-bottom: 10rpx;
  width: 100%;
  min-height: 180rpx;
  background-color: #ffffff;
  box-sizing: border-box;
  display: flex;
  flex-direction: row;
}

.left {
  margin: auto 40rpx auto 45rpx;
  width: 32rpx;
  height: 100%;
  display: flex;
  align-items: center;
}

.left image {
  width: 32rpx;
  height: 32rpx;
}

.middle {
  flex: 1;
}

.middle .name {
  margin-bottom: 10rpx;
  width: 100%;
  min-height: 48rpx;
  font-size: 34rpx;
  font-weight: bold;
  color: #353535;
  line-height: 48rpx;
}

.middle .address {
  margin-bottom: 10rpx;
  width: 100%;
  height: 40rpx;
  font-weight: 400;
  font-size: 28rpx;
  color: #353535;
  line-height: 40rpx;
}

.middle .extra {
  margin-bottom: 10rpx;
  width: 100%;
  height: 37rpx;
  font-size: 26rpx;
  font-weight: 400;
  color: #7E7E7E;
  line-height: 37rpx;
}

.middle .extra text:last-child {
  margin-left: 20rpx;
}

.right {
  width: 172rpx;
  border-left: 1px solid #DFDFDF;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: 34rpx;
  font-weight: bold;
  color: #353535;
}

.right .red {
  font-size: 26rpx;
  color: #DA5937;
}