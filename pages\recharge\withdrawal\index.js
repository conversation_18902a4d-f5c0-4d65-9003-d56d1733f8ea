var util = require('../../../utils/util');
var api = require('../../../config/api.js');
const app = getApp()
Page({
  data: {
    phone: app.globalData.userInfo.phone,
    withdrawableBalance: 0,
    currentTime: 60,
    interval: null, //倒计时函数
    showSendCodeBtn: true, //显示倒计时时间
    allowClicking: false, //是否可以提交
    showTip: false,
    tipText: '',
    tip: {
      min: '提现金额不能小于最小提现金额',
      max: '提现金额不能大于最大提现金额',
      exceed: '提现金额已超过可提现金额'
    },
    amount:'',
    code:'',
    showSuccess:false
  },
  onLoad(options) {
    this.setData({
      withdrawableBalance: options.withdrawableBalance
    })
  },
  onReady() {

  },
  onShow() {

  },
  bindKeyInput: function (e) {
    var code = e.detail.value;
    this.setData({
      code: code
    })
  },
  check(e) {
    var value = e.detail.value
    if (value > 10000) {
      this.setData({
        showTip: true,
        tipText: this.data.tip.max
      })
    } else if (value < 1) {
      this.setData({
        showTip: true,
        tipText: this.data.tip.min
      })
    } else if (value >  this.data.withdrawableBalance) {
      this.setData({
        showTip: true,
        tipText: this.data.tip.exceed
      })
    } else {
      this.setData({
        showTip: false,
        amount:value
      })
    }
  },
  getCode: function (options) {
    console.log('getCode')
    var that = this;
    var currentTime = that.data.currentTime
    var i = setInterval(function () {
      currentTime--;
      that.setData({
        currentTime: currentTime
      })
      if (currentTime <= 0) {
        clearInterval(that.data.interval)
        that.setData({
          currentTime: 60,
          showSendCodeBtn: false,
        })
      }
    }, 1000)
    this.setData({
      interval: i
    })
  },
  getVerificationCode() {
    util.showLoading('正在加载…');
    var that = this
    that.setData({
      showSendCodeBtn: false
    })
    util.request(api.getCodeByWithdrawal, {
      "verificationType": 'WITHDRAW'
    }, 'POST').then(function (res) {
      if (res.code == '0') {
        wx.hideLoading();
        that.setData({
          verificationId: res.result
        })
        that.getCode();
      } else {
        wx.hideLoading();
        that.setData({
          currentTime: 60,
          showSendCodeBtn: true
        })
        clearInterval(that.data.interval)
        wx.showModal({
          title: '失败提醒',
          content: res.message,
          showCancel: false,
          confirmColor: app.globalData.tipBtnColor,
          success(res) {
            if (res.confirm) {
              that.triggerEvent("cancel")
            } else if (res.cancel) {
              console.log('用户点击取消')
            }
          }
        })
      }
    });
  },
  applyForWithdrawal(){
    util.showLoading('正在加载…');
    var that = this
    util.request(api.withdraw, {
      amount: that.data.amount,
      code: that.data.code,
      verificationId: that.data.verificationId
    }, 'POST').then(function (res) {
      if (res.code == '0') {
        wx.hideLoading();
        that.setData({
          showSuccess: true,
        })
        clearInterval(that.data.interval)
      } else {
        wx.hideLoading();
        clearInterval(that.data.interval)
        wx.showModal({
          title: '失败提醒',
          content: res.message,
          showCancel: false,
          confirmColor: app.globalData.tipBtnColor,
          success(res) {
            if (res.confirm) {
              that.triggerEvent("cancel")
            } else if (res.cancel) {
              console.log('用户点击取消')
            }
          }
        })
      }
    });
  },
  backPage(){
    wx.navigateBack({
      delta: 1
    })
  }
})