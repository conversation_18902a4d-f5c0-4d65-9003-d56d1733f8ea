<cu-custom bgColor="white-bg" contentTitle="优惠券" isBack="true"></cu-custom>
<view wx:if="{{ list.length == 0 }}">
  <image src="../../../image/qfjf-null.png" class="nullImg"></image>
  <view class="nullTip">无可用优惠券</view>
</view>
<scroll-view style="height: 100hv;" scroll-y="true">
  <block wx:for="{{list}}" wx:key="index">
    <view class="yhjBox rowView" bindtap="select" id="{{item.couponCode}}">
      <view class="yhjBox-l columnView yhjBox-lqm" wx:if="{{item.couponType == 3}}">
        <view class="yhjBox-l-t">
          <text class="unit">¥</text><text style="margin:0 8rpx;">0</text><text style="font-size: 26rpx;">元停</text>
        </view>
        <view class="yhjBox-l-b">
          全免券
        </view>
      </view>
      <view class="yhjBox-l columnView" wx:else>
        <view class="yhjBox-l-t min" wx:if="{{item.couponType == 2||item.couponType == 4}}">
          {{item.couponValueText}}
        </view>
        <view class="yhjBox-l-t" wx:else>
          <text style="font-size: 26rpx;">¥</text>{{item.couponValue}}
        </view>
        <view class="yhjBox-l-b">
          {{item.typeName}}
        </view>
      </view>
      <view class="yhjBox-r columnView">
        <view class="yhjBox-r-t rowView">
          <view style="min-height:90rpx;width: 100%;">{{item.desc==""?item.typeName:item.desc}}</view>
          <view >
            <image src="{{item.couponCode!=couponCode?imgUrl+'tran.png':'../../../image/yes-yhj.png'}}" style="height: 40rpx;width: 40rpx;margin: 25rpx;"></image>
          </view>
        </view>
        <view class="yhjBox-r-b">
          {{item.validDate}}
        </view>
      </view>
    </view>
  </block>
</scroll-view>
<view class="box_convention_button">
  <button class="convention_button " formType="submit" bindtap="sure">确定</button>
</view>