var util = require('../../utils/util')
var api = require('../../config/api.js')
var app = getApp()
var commonApi = require('../../utils/commonApi.js')
Page({
  data: {
    imageHeight: app.globalData.MyImageHeight,
    background: [],
    indicatorDots: true,
    vertical: false,
    autoplay: true,
    interval: 5000,
    duration: 500,
    imgUrl: api.imgUrl,
    isLogin: false,
    username: '登录/注册',
    phone: '', //
    balance: '--',
    integral: '--',
    coupon: '0', //优惠劵数量
    unpaid: '0', //待付订单数
    showLoginDialog: false,
    list: [
      {
        name: '我的车辆',
        id: 'myCar',
        url: '/pages/my/myCar/index',
      },
      {
        name: '充值记录',
        id: 'recharge',
        url: '/pages/recharge/detail/index',
      },
      {
        name: '我的月卡',
        id: 'monthlyPackage',
        url: '/pages/monthlyPackage/index',
      },
      {
        name: '我的积分',
        id: 'myPoints',
        url: '/pages/my/myPoints/index',
      },
      {
        name: '我的等级',
        id: 'myLevel',
        url: '/pages/my/myLevel/index',
      },
      {
        name: '车辆认证',
        id: 'carAuth',
        url: '/pages/my/myCar/index?isAuth=1',
      },
      // , {
      //   name: "系统消息",
      //   id: "systemMessage",
      //   url: "/pages/systemMessage/index"
      // }
      {
        name: '设置',
        id: 'set',
        url: '/pages/my/set/index',
      },
    ],
    monthlyCardCount: 0,
    monthlyStatus: 'NO_PARKING_LOT',
  },
  onShow() {
    this.refresh()
    this.getAdvert()
  },
  login() {
    if (app.globalData.userInfo.loginStatus !== 'LOGGED_IN') {
      wx.navigateTo({
        url: '/pages/common/loginTip/loginTip?tiptype=0',
      })
    }
  },
  refresh() {
    var that = this
    wx.login({
      success(resLogin) {
        util
          .request(api.getLoginInfo, { code: resLogin.code }, 'GET')
          .then(function (res) {
            if (res.code == '0') {
              var result = res.result
              app.globalData.userInfo = result
              app.globalData.userInfo.loginStatus = result.loginStatus
              if (result.loginStatus === 'LOGGED_IN') {
                //登陆
                that.setData({
                  isLogin: true,
                  username: result.username,
                  phone: result.phone,
                  balance: result.balance,
                  integral: result.integral,
                  coupon: result.coupon,
                  unpaid: result.unpaid,
                })
              } else {
                that.setData({
                  isLogin: false,
                  username: '登录/注册',
                  phone: '登录',
                  balance: '--',
                  integral: '--',
                  coupon: '0',
                  unpaid: '0',
                })
              }
            } else {
              that.setData({
                isLogin: false,
                username: '登录/注册',
                phone: '登录',
                balance: '--',
                integral: '--',
                coupon: '0',
                unpaid: '0',
              })
              util.showToast(res.message)
            }
          })
          .catch(err => {
            console.log('🚀 ~ getUserInfo err:', err)
          })
      },
    })
  },
  goToPage(e) {
    var currentTarget = e.currentTarget
    var route = currentTarget.dataset.route
    var check = currentTarget.dataset.check
    var type = currentTarget.dataset.type
    if (check != 'false' && app.globalData.userInfo.loginStatus != 'LOGGED_IN') {
      //校验是否已登陆
      console.log('登陆跳转设置的route' + route)
      wx.navigateTo({
        url: '/pages/common/loginTip/loginTip?tiptype=' + type + '&&route=' + route,
      })
    } else {
      wx.navigateTo({
        url: route,
      })
    }
  },
  showLogin(e) {
    var currentTarget = e.currentTarget
    var route = currentTarget.dataset.route
    this.setData({
      showLoginDialog: true,
      goPageRoute: route,
    })
  },
  goModulePage(e) {
    var currentTarget = e.currentTarget
    var id = currentTarget.id
    var route = e.currentTarget.dataset.route
    if (id == 'monthlyPackage') {
      if (app.globalData.userInfo.loginStatus != 'LOGGED_IN') {
        //校验是否已登陆
        wx.navigateTo({
          url: '/pages/monthlyPackage/add/index?status=wdl',
        })
      } else {
        util.request(api.getMonthlyStatus, null, 'GET').then(function (res) {
          //查询月卡状态
          var infos = res.result
          if (res.code == '0') {
            var monthlyStatus = infos.monthlyStatus
            if (monthlyStatus == 'NO_PARKING_LOT' || monthlyStatus == 'UNPURCHASED_MONTHLY') {
              //表示包月活动升级中或未购买月卡
              route = '/pages/monthlyPackage/add/index?status=' + monthlyStatus
            } else {
              route = route + '?status=' + monthlyStatus
            }
            wx.navigateTo({
              url: route,
            })
          } else {
            util.showToast(res.message)
          }
        })
      }
    } else if (id == 'myLevel' && app.globalData.userInfo.loginStatus != 'LOGGED_IN') {
      //1代付订单、订单查询、全部订单2优惠劵3余额充值、充值记录4我的积分5我的等级
      console.log('登陆跳转设置的route' + route)
      wx.navigateTo({
        url: '/pages/common/loginTip/loginTip?tiptype=5&&route=' + route,
      })
    } else if (id == 'myPoints' && app.globalData.userInfo.loginStatus != 'LOGGED_IN') {
      console.log('登陆跳转设置的route' + route)
      wx.navigateTo({
        url: '/pages/common/loginTip/loginTip?tiptype=4&&route=' + route,
      })
    } else if (id == 'recharge' && app.globalData.userInfo.loginStatus != 'LOGGED_IN') {
      console.log('登陆跳转设置的route' + route)
      wx.navigateTo({
        url: '/pages/common/loginTip/loginTip?tiptype=3&&route=' + route,
      })
    } else {
      wx.navigateTo({
        url: route,
      })
    }
  },
  async getAdvert() {
    try {
      const { data, id } = await commonApi.fetchAd(7)
      this.setData({
        background: data || [],
      })
      this.swiperId = id
    } catch (error) {
      console.log(error)
    }
  },
  onSwiperTap(e) {
    const { item } = e.currentTarget.dataset
    commonApi.handleAdClick(item, this.swiperId)
  },
  goPageMethod() {
    //登陆后跳转到这个方法
    wx.navigateBack()
  },
})
