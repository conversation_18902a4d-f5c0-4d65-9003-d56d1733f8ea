const app = getApp()
Page({

  data: {
    pageHeight: 0,
    invoiceInfoVos:[],
  },
  onLoad(options) {
    let height = app.globalData.windowHeight - app.globalData.CustomBar + 48; // 48是tabbar的高度
    this.setData({
      pageHeight: height
    })
  },
  onShow(){
    var that = this;
    wx.getStorage({
      key: 'invoiceInfoVos',
      success(res) {
        that.setData({
          invoiceInfoVos: res.data
        })
      }
    })
  },
})