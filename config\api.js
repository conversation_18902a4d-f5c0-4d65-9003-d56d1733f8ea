// 以下是业务服务器API地址
var WxApiRoot = 'https://app.okguilin.com/parking/' //后台app.okguilin.com/parking
var imgUrl = 'https://app.okguilin.com/image/' //图片parking.hivisicloud.com/parking
var agreementUrl = 'https://app.okguilin.cn/h5/' //协议
var onlineServiceUrl = 'https://app.okguilin.cn/chat/mobile?noCanClose=1&token=1d19436d79fa32f23ae8e2510e08be6d' //在线客服
const shengCai = 'https://omsapi.okguilin.com/' //盛才api
const idauth = 'https://idauth.okguilin.com' //盛才api2
var version = 'v1.0.1/'
var version2 = 'v1.0.2/'
module.exports = {
  onlineServiceUrl: onlineServiceUrl,
  agreementUrl: agreementUrl,
  imgUrl: imgUrl,
  getHomeData: WxApiRoot + 'user/home/<USER>',
  searchDDList: WxApiRoot + 'user/info/name/pwd',
  getMapMakers: WxApiRoot + 'lot/map/resources',
  getMapMakerList: WxApiRoot + 'lot/list',
  getParkingInfo: WxApiRoot + 'lot/info/detail',
  searchParkingPoi: WxApiRoot + 'lot/poi/parking/lot',
  getMapResInfo: WxApiRoot + 'lot/map/resource/info',
  getLoginInfo: WxApiRoot + 'user/wechat/mp/code2session',
  addCar: WxApiRoot + 'user/vehicle/add/vehicle',
  login: WxApiRoot + 'user/wechat/mp/phone',
  getCarList: WxApiRoot + 'user/vehicle/get/vehicle',
  getUserInfo: WxApiRoot + 'user/login/info',
  logout: WxApiRoot + 'user/logout',
  getArrearsList: WxApiRoot + version + 'arrears/page',
  getArrearInfo: WxApiRoot + version + 'arrears/detail/',
  pay: WxApiRoot + version + 'arrears/pay',
  getArrearNum: WxApiRoot + 'user/plate/data/',
  getRechargeInfo: WxApiRoot + version + 'recharge/page/data',
  payRecharge: WxApiRoot + version + 'recharge/pay',
  payRechargeRecordList: WxApiRoot + version + 'recharge/record',
  payMethodList: WxApiRoot + 'payment/method/{payBusiness}',
  logoff: WxApiRoot + '/user/logoff',
  getMonthlyStatus: WxApiRoot + version + 'monthly/card/status',
  searchMonthParkingPoi: WxApiRoot + 'lot/monthly',
  getMonthPackages: WxApiRoot + version + 'monthly/packages',
  payMonthly: WxApiRoot + version + 'monthly/pay',
  payMonthlyRecordList: WxApiRoot + version + 'monthly/record',
  setAcquiesceVehicle: WxApiRoot + 'user/vehicle/acquiesce/vehicle',
  delVehicle: WxApiRoot + 'user/vehicle/del/vehicle',
  vehicleAuthentication: WxApiRoot + 'user/vehicle/authentication/vehicle',
  reservation: WxApiRoot + version + 'space/reservation/',
  getReservationRecord: WxApiRoot + version + 'space/reservation/record',
  getMonthlyDetail: WxApiRoot + version + 'monthly/detail/',
  getParkingPaymentInfo: WxApiRoot + version + 'arrears/stopping',
  getCouponsList: WxApiRoot + version + 'arrears/stopping/coupons',
  payParkingPayment: WxApiRoot + version + 'arrears/stopping/pay',
  getPayMethod: WxApiRoot + version + 'payment/method/',
  balancePay: WxApiRoot + 'callback/balance/pay',
  getReservationRule: WxApiRoot + version + 'space/reservation/rule',
  cancelReservation: WxApiRoot + version + 'space/reservation',
  monthlyCancel: WxApiRoot + version + 'monthly/cancel',
  queryOrderList: WxApiRoot + 'order/list',
  getArrearsDetail: WxApiRoot + version + 'arrears/detail/',
  getRechargeDetail: WxApiRoot + version + 'recharge/detail/',
  rechargeRefund: WxApiRoot + version + 'recharge/refund/',
  checkReservation: WxApiRoot + version + 'space/reservation/check',
  getArrearsByQrcode: WxApiRoot + version + 'arrears/stopping/by/qrcode',
  getCouponsListByQrcode: WxApiRoot + version + 'coupon/coupons',
  addCarByQrcode: WxApiRoot + version + 'arrears/set/common/vehicle/by/arrears',
  getArrearsByPlate: WxApiRoot + version + '/arrears/stopping/by/plate',
  getUserStatus: WxApiRoot + '/user/status',
  getCodeByModifiPhone: WxApiRoot + '/user/logged/code/send',
  modifiPhone: WxApiRoot + '/user/phone',
  logoffable: WxApiRoot + '/user/logoffable',
  getCodeByLogoff: WxApiRoot + '/user/logoff/code/send',
  getPayMethodByParkId: WxApiRoot + version + 'payment/methods',
  getCodeByWithdrawal: WxApiRoot + '/user/code/send/login/phone',
  withdraw: WxApiRoot + version + '/recharge/withdraw/applyfor',
  getCustomerServiceAssistList: WxApiRoot + version + '/customerServiceAssist/list',
  getFeedbackDetail: WxApiRoot + version + '/customerServiceAssist/info/',
  getCType: WxApiRoot + version + '/customerServiceAssist/type/info',
  getAType: WxApiRoot + version + '/customerServiceAssist/appeal/info',
  addProblemConsultation: WxApiRoot + version + '/customerServiceAssist/problem/feedback',
  uploadPicture: WxApiRoot + '/file/upload/picture',
  addAppeal: WxApiRoot + version + '/customerServiceAssist/submit/appeal',
  getParkingAppealOrderDetails: WxApiRoot + version + '/customerServiceAssist/info/appeal/',
  fwSearchParkingPoi: WxApiRoot + 'lot/poi/',
  getVehicleType: WxApiRoot + 'user/vehicle/vehicle/type',
  getVehicleColour: WxApiRoot + 'user/vehicle/vehicle/colour',
  getAppealList: WxApiRoot + version + 'arrears/appeal/list',
  getInvoiceList: WxApiRoot + version + 'invoice/bill/list',
  proFormaInvoice: WxApiRoot + version + 'invoice/preview',
  getInvoiceRecordList: WxApiRoot + version + 'invoice/record',
  addInvoice: WxApiRoot + version + 'invoice/applyfor',
  getCanReserveCarList: WxApiRoot + version + 'space/reservation/plates/',
  monthlyCancelPreview: WxApiRoot + version + 'monthly/cancel/preview',
  monthlyCheck: WxApiRoot + version + 'monthly/check',
  getRenewalMonthPackages: WxApiRoot + version + 'monthly/packages/renewal',
  getRechargeDetailN: WxApiRoot + version + 'recharge/order/detail/',
  payMonthlyRenewal: WxApiRoot + version + 'monthly/pay/renewal',
  getMonthlyPayDetail: WxApiRoot + version + 'monthly/order/detail/',
  getParkingPaymentInfos: WxApiRoot + version + 'arrears/stopping/for/coupon',
  getAreaResource: WxApiRoot + 'lot/area/map/resources',
  getArrearsDetails: WxApiRoot + version + 'arrears/order/detail/',
  openOrCLoseBalance: WxApiRoot + '/user/vehicle/change/balance/deduction/state',
  getPlateList: WxApiRoot + 'user/vehicle/fee/deduction/vehicles',
  getArrearsInOder: WxApiRoot + version + 'arrears/orders',
  isAuthenticationable: WxApiRoot + 'user/vehicle/authenticationable/vehicle',
  gettRechargeBargains: WxApiRoot + version + 'recharge/bargains',
  getFilters: WxApiRoot + 'lot/filters',
  getAdvertList: WxApiRoot + version + 'advert/list',
  resendInvoice: WxApiRoot + version + 'invoice/resend',
  agreementAddress: WxApiRoot + version + 'agreement',
  cancelReservationPreview: WxApiRoot + version + 'space/reservation/cancel/preview',
  getReservationInfo: WxApiRoot + version + 'space/reservation/detail',
  getTempCarArrearsInfo: WxApiRoot + version + 'arrears/home/<USER>/by/plate',
  getLoginCode: WxApiRoot + 'user/code/send',
  loginByCode: WxApiRoot + 'user/mp/code/login',
  getTempCarArrears: WxApiRoot + version + 'arrears/page/by/plate',
  addBatchPay: WxApiRoot + version + 'arrears/batch/pay',
  getTempCarArrearTotal: WxApiRoot + version + 'arrears/count/by/plate',
  getArrearsAppealDetail: WxApiRoot + version + 'arrears/appeal/detail',
  checkMonth: WxApiRoot + version2 + 'monthly/vehicles/check',
  getMonthPackages_new: WxApiRoot + version2 + 'monthly/packages/new',
  getRenewalMonthPackages_new: WxApiRoot + version2 + 'monthly/packages/renew',
  getMonthParks: WxApiRoot + version2 + 'monthly/group/parks',
  getMonthRules: WxApiRoot + version2 + 'monthly/rules/renew',

  getAdvertList2: 'https://chargeadmin.okguilin.com/parking/v1.0.1/advert/list',
  getCouponPackage: shengCai + 'api/couponPackage/detail',
  getCoupon: shengCai + 'api/couponPackage/receiveCoupon',
  getFullPhone: shengCai + 'api/couponPackage/completePhone',
  wxClickRecord: shengCai + 'api/cxglAdvertisementStatistics/wxClickRecord',
  wxPageDetailed: shengCai + 'api/cxglAdvertisementSpace/wxPageDetailed',
  postSurveyAdd: shengCai + 'api/survey/add',
  getSurveyHavaAdd: shengCai + 'api/survey/isHavaAdd',
  getWxOpenid: shengCai + 'api/wxOpen/wxOpenId',
  //接口需要在header中带openId---start
  getOrderParking: shengCai + 'api/temporaryParking/orderParking',
  getDriverActiveCarOut: shengCai + 'api/temporaryParking/driverActiveCarOut',
  //接口需要在header中带openId---end
  // orderParkingByToken: 'http://47.107.60.59:8902/' + 'api/temporaryParking/orderParkingByToken',
  orderParkingByToken: shengCai + 'api/temporaryParking/orderParkingByToken',
  fullPlateList: shengCai + 'api/driver/plateList',
  //调起国家网络身份认证
  authRequest: idauth + '/api/nationalNetIdAuth/authRequest',
  //通过国家网络身份认证获取token信息
  cxglAuthRequest: idauth + '/api/cxgl/authRequest',
  //通过国家网络绑定token信息
  cxglAuthBindToken: idauth + '/api/cxgl/authBindToken',
}
