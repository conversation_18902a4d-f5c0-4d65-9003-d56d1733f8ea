// index.js
var util = require('../../utils/util')
var commonApi = require('../../utils/commonApi.js')
var api = require('../../config/api.js')
var app = getApp()
Page({
  data: {
    StatusBar: app.globalData.StatusBar,
    //临车牌缴费输入车牌版本
    showAddCarTip: false, //展示添加车牌的弹窗
    isKeyboard: false,
    isClick: false, //不允许点击
    yesIconUrl: 'yes-ico', //yes-ico yes-icon-select
    allowXY: false,
    isNumberKB: false,
    tapNum: true,
    showNewPower: true, //是否显示'新'
    // keyboardNumber: "1234567890ABCDEFGHJKLMNPQRSTUVWXYZ港澳学",
    keyboardNumber: '1234567890QWERTYUP港澳ASDFGHJKL学ZXCVBNM',
    keyboard1: '渝川京沪粤津冀晋蒙辽吉黑苏浙皖闽赣鲁豫鄂湘桂琼贵云藏陕甘青宁新',
    inputPlates: {
      index0: '桂',
      index1: '',
      index2: '',
      index3: '',
      index4: '',
      index5: '',
      index6: '',
      index7: '',
    },
    inputOnFocusIndex: '1',
    carNum: '',
    //临车牌缴费输入车牌版本 end
    loginStatus: app.globalData.userInfo.loginStatus, //登陆状态
    uniqueId: '', //用于停车缴费页面查询停车欠费详情
    currentCost: 0,
    isParking: false, //是否正在停车
    numData: {
      //代付订单和优惠卡劵的提醒
      pendingOrders: 0,
      discountCardCoupons: 0,
    },
    //keyWord: "查找停车位/公厕/加油站/充电站",
    carList: [],
    index: 0, //选择车辆索引
    imgUrl: api.imgUrl,
    latitude: app.globalData.latitude,
    longitude: app.globalData.longitude,
    pslist: [],
    currentPage: 1,
    pageSize: 20, //分页大小
    showNullMoreTip: false,
    showNullTip: false,
    orderBy: 'DISCOUNT',
    CustomBar: app.globalData.CustomBar,
    background: [],
    indicatorDots: true,
    vertical: false,
    autoplay: true,
    interval: 5000,
    duration: 500,
    showLoginDialog: false,
    goPageRoute: '', //即将跳转页面的Id
    moduleList: [
      {
        imgName: 'myCar',
        name: '我的车辆',
        route: '/pages/my/myCar/index',
      },
      // {
      //   imgName: 'supplementaryPayment',
      //   name: '车位预订',
      //   route: '/pages/parkingSpaceInquiry/index?isV=true&type=reservation'
      // },
      {
        imgName: 'discountCardCoupons',
        name: '优惠卡劵',
        route: '/pages/my/discountCoupon/index',
      },
      {
        imgName: 'pendingOrders',
        name: '待付订单',
        route: '/pages/supplementaryPayment/index',
      },
      {
        imgName: 'button_kefu',
        name: '客服帮助',
        route: '/pages/customerServiceHelp/index',
      },
      {
        imgName: 'button_cheweichaxun',
        name: '找停车场',
        route: '/pages/parkingSpaceInquiry/index?isV=false&&type=parkingSpaceInquiry',
      },
      {
        imgName: 'button_wc',
        name: '找公厕',
        route: '/pages/parkingSpaceInquiry/fwMap/index?type=TOILET',
      },
      {
        imgName: 'button_chogndianzhaung',
        name: '找充电桩',
        route: '/pages/parkingSpaceInquiry/fwMap/index?type=CHARGE',
      },
      {
        imgName: 'button_jiayouzhan',
        name: '找加油站',
        route: '/pages/parkingSpaceInquiry/fwMap/index?type=FILLING_STATION',
      },
    ],

    hiddenAd: true,
    ad: null,
    showPay: false, //显示车牌缴费
    showAIDialog: false,
  },
  onLoad() {
    this.fetchSwiper()
    if (app.globalData.isFirstOpen) {
      //扫描缴费二维码打开不显示开屏广告
      const scene = this.getScene()
      console.log('scene', scene)
      if (scene !== 1011 && scene !== 1012 && scene !== 1013) {
        this.fetchAd()
      }
    }
  },
  onShow: async function () {
    var that = this
    that.setData({
      isKeyboard: false,
    })
    util.fetchTokenSC()
    const info = await util.fetchToken()
    // util.fetchTokenSC()
    that.setData({
      loginStatus: info.loginStatus,
      // loginStatus: 'TEMP_LOGIN',
    })
    that.initData()
  },
  initData: function () {
    var that = this
    wx.getLocation({
      //获取当前位置
      type: 'gcj02',
      success(res) {
        var latitude = res.latitude
        var longitude = res.longitude
        that.setData({
          latitude: latitude,
          longitude: longitude,
        })
        app.globalData.latitude = latitude
        app.globalData.longitude = longitude
        that.cleanData()
        that.getHomeData(latitude, longitude)
      },
      fail(res) {
        that.getHomeData(that.data.latitude, that.data.longitude)
      },
    })
  },
  cleanData: function () {
    this.setData({
      currentPage: 1,
      numData: {
        //代付订单和优惠卡劵的提醒
        pendingOrders: 0,
        discountCardCoupons: 0,
      },
      index: '0',
      pslist: [],
      carList: [],
      showNullTip: true,
    })
  },
  getHomeData: function (latitude, longitude) {
    util.showLoading('正在加载…')
    var that = this
    util
      .request(
        api.getHomeData,
        {
          latitude: latitude,
          longitude: longitude,
          orderBy: 'DISCOUNT',
        },
        'GET'
      )
      .then(function (res) {
        var pslist = []
        var plates = []
        var showNullMoreTip = false
        var showNullTip = false
        if (res.code == '0') {
          var result = res.result
          var coupon = result.coupon
          pslist = util.sortParkingLotsByHoliday(result.nearbyParkingLots)
          plates = result.plates
          if (pslist.length == 0) {
            showNullTip = true
          }
          wx.hideLoading()
        } else {
          showNullMoreTip = true
          wx.hideLoading()
          util.showToast(res.message)
        }
        that.setData({
          'numData.discountCardCoupons': coupon ? coupon : 0, //优惠劵数量
          pslist: pslist,
          carList: plates,
          showNullTip: showNullTip,
          showNullMoreTip: showNullMoreTip,
          orderBy: 'DISCOUNT', //首页获取详情默认按照距离最近查询，所以需要重置排序条件
        })
        if (plates != null) {
          if (plates.length > 0) {
            that.setData({
              index: '0',
            })
            that.getSupplementaryPayment(plates[0]['vehicleId'])
          }
        }
      })
  },
  getMapMakerList: function (latitude, longitude) {
    util.showLoading('正在加载…')
    let that = this
    var data = that.data
    var currentPage = data.currentPage
    var pageSize = data.pageSize
    util
      .request(
        api.getMapMakerList,
        {
          latitude: latitude,
          longitude: longitude,
          currentPage: currentPage,
          filter: 'PARKING_LOT',
          orderBy: data.orderBy, //DISTANCE表示距离最短，FREE表示免费最久，SURPLUS表示余位最多),可用值:DISTANCE,FREE,SURPLUS
          pageSize: pageSize,
        },
        'GET'
      )
      .then(function (res) {
        var result = res.result
        var pslist = result.records
        if (res.code == '0') {
          var total = result.total
          if (total == 0) {
            if (currentPage == 1) {
              //没有数据
              that.setData({
                pslist: [],
                showNullTip: true,
                showNullMoreTip: false,
                tcdNum: 0,
              })
            } else {
              //没有更多数据
              that.setData({
                showNullMoreTip: true,
                showNullTip: false,
              })
            }
          } else {
            const sortPslist = util.sortParkingLotsByHoliday(pslist)
            that.setData({
              pslist: that.data.pslist.concat(sortPslist),
              showNullTip: false,
              tcdNum: total,
            })
            if (total <= pageSize || pslist.length == 0) {
              //没有更多数据
              that.setData({
                showNullMoreTip: true,
              })
            } else {
              that.setData({
                showNullMoreTip: false,
              })
            }
          }
          //加载下一页
          that.setData({
            currentPage: currentPage + 1,
          })
          wx.hideLoading()
        } else {
          wx.hideLoading()
          util.showToast(res.message)
        }
      })
  },
  bindPickerChange: function (e) {
    //切换车牌需要
    var index = e.detail.value
    this.setData({
      index: index,
    })
    this.getSupplementaryPayment(this.data.carList[index]['vehicleId'])
  },
  getSupplementaryPayment: function (vehicleId) {
    //查询是否有欠费提醒
    var that = this
    util.request(api.getArrearNum + '/' + vehicleId, null, 'GET').then(function (res) {
      console.log('查询是否有欠费提醒', res)
      if (res.code == '0') {
        var result = res.result
        that.setData({
          uniqueId: result.uniqueId, //用于停车缴费页面查询停车欠费详情
          ['numData.pendingOrders']: result.unpaid,
          currentCost: result.currentCost,
        })
        if (!result.currentCost) {
          that.fetchParkingState()
        }
      } else {
        util.showToast(res.message)
      }
    })
  },
  //根据vehicleId查询当前车辆是否正在停车
  fetchParkingState() {
    const that = this
    console.log('vehicleId：', that.data.carList[that.data.index]['vehicleId'])
    util
      .request(api.orderParkingByToken, { vehicleId: that.data.carList[that.data.index]['vehicleId'] }, 'GET')
      .then(function (res) {
        console.log('fetchParkingState结果：', res)
        wx.hideLoading()
        let { code, data } = res || {}
        if (code == '200') {
          that.setData({
            isParking: data ? true : false,
          })
        }
      })
      .catch(err => {
        console.log('fetchParkingState错误：', err)
        wx.hideLoading()
        util.showToast('服务异常，请稍后重试')
      })
  },
  goToPage: function (e) {
    var currentTarget = e.currentTarget
    var id = currentTarget.id
    var that = this
    var route = currentTarget.dataset.route
    if ((id == 'discountCardCoupons' || id == 'pendingOrders') && app.globalData.userInfo.loginStatus != 'LOGGED_IN') {
      var type = 1
      if (id == 'discountCardCoupons') {
        type = 2
      }
      console.log('登陆跳转设置的route' + route)
      wx.navigateTo({
        url: '/pages/common/loginTip/loginTip?tiptype=' + type + '&&route=' + route,
      })
    } else {
      that.toPageL(route)
    }
  },
  toPageL: function (route) {
    wx.navigateTo({
      url: route,
      fail: function () {
        util.showToast('正在研发中。')
      },
    })
  },
  goPage: function () {
    this.toPageL(this.data.goPageRoute)
  },
  goSupplementaryPage: function () {
    this.toPageL('supplementaryPayment')
  },
  selectItem: function (e) {
    let that = this
    var id = e.target.id + ''
    var data = that.data
    that.setData({
      pslist: [],
      currentPage: 1,
      orderBy: id,
    })
    that.getMapMakerList(data.latitude, data.longitude)
  },
  goxq: function (e) {
    var id = e.currentTarget.id
    var type = e.currentTarget.dataset.type
    if (type === 'PARKING_LOT' || type === 'PARKING_SPACE') {
      //停车场和露天泊位可以跳转到详情
      wx.navigateTo({
        url:
          '/pages/parkingSpaceInquiry/info/index?id=' +
          id +
          '&lat=' +
          this.data.latitude +
          '&long=' +
          this.data.longitude +
          '&resourceType=' +
          type,
      })
    }
  },
  openMapApp: function (e) {
    var dataset = e.currentTarget.dataset
    var latitude = dataset.lat
    var longitude = dataset.long
    var name = dataset.name
    var address = dataset.address
    util.openMapApp(latitude, longitude, app.globalData.mapScale, name, address)
  },
  goSearchPage: function () {
    var data = this.data
    wx.navigateTo({
      url: '/pages/parkingSpaceInquiry/searchPage/index?lat=' + data.latitude + '&lng=' + data.longitude + '&isV=false',
    })
  },
  payment: function () {
    var data = this.data
    if (data.currentCost <= 0) {
      var vehicleId = data.carList[data.index]['vehicleId']
      this.getSupplementaryPayment(vehicleId)
      util.showToast('刷新成功')
    } else {
      //跳转到支付页面
      wx.navigateTo({ url: '/pages/parkingPayment/index?uniqueId=' + data.uniqueId })
    }
  },
  goCarPage: function () {
    // if (app.globalData.userInfo.loginStatus != "LOGGED_IN") { //点击绑定车辆需要
    //   this.setData({
    //     showLoginDialog: true,
    //     goPageRoute: '/pages/my/myCar/index'
    //   })
    // } else {2023.11.27去掉点击模块进入时需要判断是否需要登陆绑定的逻辑，改为进入模块后在判断
    wx.navigateTo({
      url: '/pages/my/myCar/index',
    })
    // }
  },
  bindReachBottom: function () {
    console.log('上拉加载....')
    var that = this
    var data = that.data
    if (data.showNullMoreTip) {
      util.showToast('没有更多数据了')
    } else {
      that.getMapMakerList(data.latitude, data.longitude)
    }
  },
  onPullDownRefresh: function () {
    //10.7秦哥让增加首页下拉刷新
    console.log('下拉')
    this.initData()
    setTimeout(() => {
      wx.stopPullDownRefresh()
    }, 2000)
  },
  isVehicleNumber: function (a) {
    return true //暂时不验证
  },
  inputClick_licensePlate: function (t) {
    //点击车牌输入框
    var that = this
    var id = t.target.dataset.id
    if (id == 0) {
      that.setData({
        inputOnFocusIndex: id,
        isNumberKB: true,
        isKeyboard: true,
        tapNum: false,
      })
    } else if (id == 1) {
      that.setData({
        inputOnFocusIndex: id,
        isNumberKB: false,
        isKeyboard: true,
        tapNum: false,
      })
    } else if (id == 7) {
      that.setData({
        inputOnFocusIndex: id,
        isNumberKB: false,
        isKeyboard: true,
        tapNum: true,
        showNewPower: false,
      })
    } else if (id > 1) {
      that.setData({
        inputOnFocusIndex: id,
        isNumberKB: false,
        isKeyboard: true,
        tapNum: true,
      })
    }
  },
  tapKeyboard: function (t) {
    //键盘点击事件
    t.target.dataset.index
    var a = t.target.dataset.val
    switch (parseInt(this.data.inputOnFocusIndex)) {
      case 0:
        this.setData({
          'inputPlates.index0': a,
          inputOnFocusIndex: '1',
        })
        break
      case 1:
        this.setData({
          'inputPlates.index1': a,
          inputOnFocusIndex: '2',
        })
        break
      case 2:
        this.setData({
          'inputPlates.index2': a,
          inputOnFocusIndex: '3',
        })
        break
      case 3:
        this.setData({
          'inputPlates.index3': a,
          inputOnFocusIndex: '4',
        })
        break
      case 4:
        this.setData({
          'inputPlates.index4': a,
          inputOnFocusIndex: '5',
        })
        break
      case 5:
        this.setData({
          'inputPlates.index5': a,
          inputOnFocusIndex: '6',
        })
        break
      case 6:
        this.setData({
          'inputPlates.index6': a,
          inputOnFocusIndex: '7',
        })
        break
      case 7:
        this.setData({
          'inputPlates.index7': a,
          inputOnFocusIndex: '7',
          showNewPower: false,
        })
    }
    var n =
      this.data.inputPlates.index0 +
      this.data.inputPlates.index1 +
      this.data.inputPlates.index2 +
      this.data.inputPlates.index3 +
      this.data.inputPlates.index4 +
      this.data.inputPlates.index5 +
      this.data.inputPlates.index6 +
      this.data.inputPlates.index7
    console.log('车牌号:', n)
    this.data.carNum = n
    this.checkedKeyboard()
    if (
      this.data.inputPlates.index0 != '' &&
      this.data.inputPlates.index1 != '' &&
      this.data.inputPlates.index2 != '' &&
      this.data.inputPlates.index3 != '' &&
      this.data.inputPlates.index4 != '' &&
      this.data.inputPlates.index5 != '' &&
      this.data.inputPlates.index6 != ''
    ) {
      this.setData({
        isClick: true,
      })
    }
  },
  //点击键盘上的完成按钮
  tapSpecBtna: function (t) {
    var that = this
    that.setData({
      isKeyboard: false,
      inputOnFocusIndex: '7',
    })
  },
  //键盘删除按钮点击事件
  tapSpecBtn: function (t) {
    var data = this.data
    console.log(t)
    console.log(t.target.dataset.index)
    switch (parseInt(data.inputOnFocusIndex)) {
      case 0:
        this.setData({
          'inputPlates.index0': '',
          inputOnFocusIndex: '0',
        })
        break
      case 1:
        this.setData({
          'inputPlates.index1': '',
          inputOnFocusIndex: '0',
        })
        break
      case 2:
        this.setData({
          'inputPlates.index2': '',
          inputOnFocusIndex: '1',
        })
        break
      case 3:
        this.setData({
          'inputPlates.index3': '',
          inputOnFocusIndex: '2',
        })
        break
      case 4:
        this.setData({
          'inputPlates.index4': '',
          inputOnFocusIndex: '3',
        })
        break
      case 5:
        this.setData({
          'inputPlates.index5': '',
          inputOnFocusIndex: '4',
        })
        break
      case 6:
        this.setData({
          'inputPlates.index6': '',
          inputOnFocusIndex: '5',
        })
        break
      case 7:
        this.setData({
          'inputPlates.index7': '',
          inputOnFocusIndex: '6',
        })
    }
    this.checkedKeyboard()
    var inputPlates = data.inputPlates
    var n =
      inputPlates.index0 +
      inputPlates.index1 +
      inputPlates.index2 +
      inputPlates.index3 +
      inputPlates.index4 +
      inputPlates.index5 +
      inputPlates.index6 +
      inputPlates.index7
    this.setData({
      'carNum': n,
    })
  },
  checkedKeyboard: function () {
    //键盘切换
    var t = this
    var inputOnFocusIndex = this.data.inputOnFocusIndex
    if (inputOnFocusIndex == 0) {
      t.setData({
        tapNum: false, //是否可以点击
        isNumberKB: true, //省和
      })
    }
    if (inputOnFocusIndex == 1) {
      t.setData({
        tapNum: false,
        isNumberKB: false,
      })
    }
    if (inputOnFocusIndex > 1) {
      t.setData({
        tapNum: true,
        isNumberKB: false,
      })
    }
  },
  bindChange: function (e) {
    this.setData({
      allowXY: !this.data.allowXY,
    })
  },
  goFindPayMoneyPage() {
    var that = this
    var data = that.data
    if (!data.isClick) {
      return
    }
    var plateNo = data.carNum
    var platteColor = data.allowXY ? 'YELLOW' : 'OTHER'
    wx.navigateTo({
      url: '/pages/parkingPayment/getTempCarorder?plateNo=' + plateNo + '&plateColor=' + platteColor,
      fail: function (res) {
        console.log(res)
      },
    })
  },
  goLoginPage() {
    wx.navigateTo({
      url: '/pages/common/loginTip/loginTip?tiptype=0',
    })
  },
  goPageMethod() {
    //登陆后跳转到这个方法
    wx.navigateBack()
    if (app.globalData.userInfo.userStatus == 'REGISTERED') {
      //已注册但未绑定车牌
      this.setData({
        showAddCarTip: true, //展示添加车牌的弹窗
      })
    }
  },
  getLeftGo: function (e) {
    //添加车辆提示的----取消
    this.setData({
      showAddCarTip: false,
    })
  },
  getRightGo: function (e) {
    //添加车辆提示的----去添加
    wx.navigateTo({
      url: '/pages/my/myCar/index',
      fail: function (res) {
        console.log(res)
      },
    })
  },
  onShareAppMessage() {
    const promise = new Promise(resolve => {
      setTimeout(() => {
        resolve({
          title: '畅行桂林',
        })
      }, 2000)
    })
    return {
      title: '畅行桂林',
      path: '/pages/index/index',
      promise,
    }
  },
  onShareTimeline: function () {
    return {
      title: '畅行桂林',
      path: '/pages/index/index',
    }
  },
  onSwiperTap(e) {
    const { item } = e.currentTarget.dataset
    commonApi.handleAdClick(item, this.swiperId)
  },

  //cancelTap隐藏方向
  onAdCanceel() {
    this.setData({ hiddenAd: !this.data.hiddenAd })
  },
  getScene() {
    const res = wx.getLaunchOptionsSync()
    return res.scene
  },
  //开屏广告
  async fetchAd() {
    const { data, id } = await commonApi.fetchAd(2)
    if (data && data.length) {
      this.setData({ ad: data[0], hiddenAd: false })
      this.AdvertisementSpaceId = id
    }
    app.globalData.isFirstOpen = false
  },
  //轮播图
  async fetchSwiper() {
    try {
      const { data, id } = await commonApi.fetchAd(8)
      console.log('获取轮播图data', data)
      console.log('获取轮播图id', id)
      this.setData({
        background: data || [],
      })
      this.swiperId = id
    } catch (error) {
      console.log(error)
    }
  },
  //跳转广告
  onAdClick() {
    commonApi.handleAdClick(this.data.ad, this.AdvertisementSpaceId)
  },
  //阻止滚动穿透
  preventMove(e) {
    console.log(e)
  },
  viewParking() {
    wx.navigateTo({ url: '/pages/orderQuery/index?currentId=' + 1 })
  },

  setPayShow() {
    this.setData({ showPay: !this.data.showPay })
  },
  openAIDialog() {
    this.setData({ showAIDialog: true })
  },
  closeAIDialog() {
    this.setData({ showAIDialog: false })
  },
})
