var util = require('../../../utils/util');
var api = require('../../../config/api.js');
var app = getApp();
Page({

  /**
   * 页面的初始数据
   */
  data: {
    bgurl:'background-image: url('+api.imgUrl+'yhj-ysy.png);',
    imgUrl:api.imgUrl,
    list: [],
    couponStateEnumeration: {
      1: '可用',
      2: '已使用',
      3: '已过期'
    },
    couponState: '1' //优惠券状态，1：未使用；2：已使用；3 已过期，-1 全部
  },
  onLoad(options) {

  },
  onShow() {
    this.getList();
  },
  selectCoupon: function (e) {
    var id = e.currentTarget.id;
    this.setData({
      couponState: id,
    })
    if(id!='1'){
      var typeName = 'ysy';
      if(id=='3'){
        typeName = 'ygq';
      }
      this.setData({
        bgurl: 'background-image: url('+this.data.imgUrl+'yhj-'+typeName+'.png);'
      })
    }
    this.getList()
  },
  getList: function () {
    var that = this;
    var data = that.data;
    util.showLoading('正在加载...');
    util.request(api.getCouponsListByQrcode + "/-1/" + data.couponState, '', 'GET').then(function (res) {
      var list = [];
      wx.hideLoading();
      if (res.code == '0') {
        list = res.result;
      } else {
        util.showToast(res.message)
      }
      that.setData({
        list: list
      });
    }).catch(err => {
      wx.hideLoading();
      util.showToast('出错了' + JSON.stringify(err))
    })
  },
  goPage(e) {
    var dataset = e.currentTarget.dataset;
    wx.navigateTo({
      url: dataset.url,
    })
  },
  goIndex(e) {
    var dataset = e.currentTarget.dataset;
    wx.redirectTo({
      url: dataset.url,
    })
  },
  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {
    this.setData({
      currentPage: 1,
      list: []
    })
    this.getList();
    wx.stopPullDownRefresh();
  },
  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {
    if (this.data.showNullMoreTip) {
      util.showToast('没有更多数据了')
    } else {
      this.getList();
    }
  },
  open:function(){
    this.setData({
      isOpen:!this.data.isOpen
    })
  }
})