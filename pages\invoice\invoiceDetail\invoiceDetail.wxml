<cu-custom bgColor="white-bg" contentTitle="发票详情" isBack="true"></cu-custom>
<view class="container" style="height: {{pageHeight}}px;">
  <block wx:for="{{invoiceInfoVos}}"  wx:key="index">
    <view class="state-box">
    <view class="header">
      <view class="money">
        <text class="symbol">￥</text>
        <text class="num">{{item.invoiceAmount}}</text>
      </view>
      <view class="tip">发票总额</view>
    </view>
    <view class="row">
      <view class="label">发票类型</view>
      <view class="value">{{item.billTypeDesc}}</view>
    </view>
    <view class="row">
      <view class="label">开票方：</view>
      <view class="value">{{item.subjectName}}</view>
    </view>
    <view class="row">
      <view class="label">发票张数：</view>
      <view class="value">{{item.count}}张</view>
    </view>
  </view>
  </block>

</view>
