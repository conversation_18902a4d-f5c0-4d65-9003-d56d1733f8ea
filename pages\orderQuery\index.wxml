<view class="saveOutView">
  <cu-custom bgColor="white-bg" contentTitle="全部订单" isBack="true"></cu-custom>
  <view class="header">
    <view class="picker-box type">
      <picker
        class=""
        bindchange="changeType"
        value="{{currentTypeIndex}}"
        range="{{orderTypes}}"
        range-key="name"
        mode="selector"
      >
        <view class="picker-content">
          <view class="value">{{orderTypes[currentTypeIndex]["name"]}}</view>
          <view class="icon"></view>
        </view>
      </picker>
    </view>
    <view class="invoice" bindtap="gotoInvoice">
      <image class="icon" src="/image/invoice.png" />
      <text class="text">开发票</text>
    </view>
  </view>
  <view class="header month" wx:if="{{currentTypeIndex == 0}}">
    <view class="list-tool">
      <view
        wx:for="{{state}}"
        wx:key="index"
        class="list-tool-select {{currentId === item.id ? 'selected' : ''}} "
        data-id="{{item.id}}"
        bind:tap="changeState"
      >
        {{item.name}}
      </view>
    </view>
    <!-- <view class="picker-box month">
      <picker
        class=""
        bindchange="changeState"
        value="{{currentStateIndex}}"
        range="{{state}}"
        range-key="name"
        mode="selector"
      >
        <view class="picker-content">
          <view class="value">{{state[currentStateIndex]["name"]}}</view>
          <view class="icon"></view>
        </view>
      </picker>
    </view> -->
  </view>
  <scroll-view
    scroll-y="true"
    class="scrollViewSa"
    refresher-threshold="{{100}}"
    refresher-enabled="{{true}}"
    refresher-triggered="{{triggered}}"
    bindrefresherrefresh="onScrollRefresh"
    bindscrolltolower="onScrollTolower"
  >
    <view wx:if="{{ list.length == 0 }}">
      <image src="../../image/qfjf-null.png" class="nullImg"></image>
      <view class="nullTip">您还没有{{orderTypes[currentTypeIndex]["name"]}}</view>
    </view>
    <block wx:for="{{list}}" wx:key="index" scroll-y="true">
      <view class="order" bindtap="clickItem" data-info="{{item}}">
        <view class="left">
          <view class="name">{{item.parkName}}</view>
          <view class="address">{{item.plateNo}}</view>
          <view class="extra">
            <text>{{currentTypeIndex == 0?item.enterTime:item.reservationTime}}</text>
            <text style="padding: 20rpx"
              >{{currentTypeIndex ==
              0&&(item.deductMoney!=0&&item.deductMoney!=null)?'优惠￥'+item.deductMoney:''}}</text
            >
          </view>
        </view>
        <view class="right" wx:if="{{currentTypeIndex == 0&&(currentStateIndex!=1)}}">
          <view> ￥{{item.totalCost}} </view>
          <view
            wx:if="{{currentId!=3&&item.totalCost>0}}"
            class="qf-listDiv-right-three"
            catch:tap="goxq"
            id="{{item.uniqueId}}"
          >
            缴费
          </view>
        </view>
      </view>
    </block>
    <view hidden="{{!showBottom}}" style="width: 100%; height: 50px; color: #7e7e7e">正在加载更多</view>
  </scroll-view>
</view>
