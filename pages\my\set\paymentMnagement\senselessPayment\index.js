var util = require('../../../../../utils/util');
var api = require('../../../../../config/api.js');
Page({

  /**
   * 页面的初始数据
   */
  data: {

  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {

  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    this.getCarList();
  },
  getCarList() {
    var that = this;
    util.showLoading('正在加载…')
    util.request(api.getPlateList, {}, 'GET').then(function (res) {
      if (res.code == '0') {
        that.setData({
          carList: res.result,
        });
        wx.hideLoading();
      } else {
        wx.hideLoading();
        util.showToast(res.message)
      }
    })
  },
  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.getCarList();
  },
  switchChange(e) {
   var selected =  e.detail.value;//自动扣费开/关状态，1-开通，0-关闭
   var state;
   if(selected){
    state = 1
   }else{
    state = 0
   }
   util.showLoading('正在加载…')
   util.request(api.openOrCLoseBalance, {
    plateNo: e.currentTarget.dataset.plate,
    state:state,
   }, 'POST').then(function (res) {
     if (res.code == '0') {
       wx.hideLoading();
       if(selected){
         util.showToast("已开启");
       }else{
        util.showToast("已关闭");
       }
     } else {
       wx.hideLoading();
       util.showToast(res.message)
     }
   })
  },
})