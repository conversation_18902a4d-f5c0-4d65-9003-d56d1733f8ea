page {
  background-color: #F6F7FB;
}

.container {
  position: relative;
  width: 100%;
}

.section-title {
  padding: 30rpx 0 20rpx 0;
  width: 100%;
  height: 37rpx;
  font-size: 26rpx;
  color: #7E7E7E;
  line-height: 37rpx;
  text-indent: 30rpx;
}

.section-content {
  margin-bottom: 10rpx;
  width: 100%;
}

.form-row {
  display: flex;
  padding: 10rpx 30rpx;
  width: 100%;
  min-height: 88rpx;
  background-color: #ffffff;
  border-bottom: 1px solid rgba(0, 0, 0, 0.16);
  box-sizing: border-box;
}

.form-row .form-label {
  margin-right: 60rpx;
  width: 120rpx;
  height: 100%;
  width: 120rpx;
  font-size: 26rpx;
  color: #353535;
  line-height: 68rpx;
}

.form-row .form-label.required::after {
  content: '*';
  color: #DA5937;
}

.form-row .form-value {
  flex: 1;
  height: 100%;
  display: flex;
  flex-direction: row;
  align-items: center;
}

.form-row .form-value .input {
  width: 100%;
  height: 100%;
  font-size: 26rpx;
  color: #000000;
  line-height: 68rpx;
  margin-top: 12rpx;
}

.form-row .form-value .checkbox {
  margin-right: 40rpx;
  height: 37rpx;
  font-size: 26rpx;
  color: #353535;
  display: flex;
  align-items: center;
}

.form-row .form-value .checkbox image {
  margin-top: 2rpx;
  margin-right: 10rpx;
  width: 32rpx;
  height: 32rpx;
}

.form-row .form-value .text {
  width: 100%;
  min-height: 100%;
  font-size: 26rpx;
  color: #000000;
  line-height: 40rpx;
  margin-top: 12rpx;
}

.form-control {
  margin: 20rpx 0;
  width: 100%;
  height: 80rpx;
}

.form-control .btn {
  margin: 0 20rpx;
  width: calc(100% - 40rpx);
  height: 80rpx;
  background: linear-gradient(90deg, #458BFF 0%, #08D7AE 100%);
  box-shadow: 0rpx 3rpx 10rpx 1rpx rgba(37, 107, 245, 0.2);
  border-radius: 40rpx;
  text-align: center;
  font-size: 36rpx;
  color: #FFFFFF;
  line-height: 80rpx;
}


.form-value .amount {
  flex: 1;
  height: 88rpx;
  font-size: 26rpx;
  color: #256BF5;
  line-height: 88rpx;
}

.form-value .next {
  position: relative;
  width: 220rpx;
  height: 88rpx;
  font-size: 24rpx;
  color: #888888;
  line-height: 88rpx;
}

.form-value .next::after {
  content: '';
  display: inline-block;
  margin-left: 10rpx;
  height: 11rpx;
  width: 11rpx;
  border-width: 4rpx 4rpx 0 0;
  border-color: #888888;
  border-style: solid;
  transform: matrix(0.71, 0.71, -0.71, 0.71, 0, 0);
}

.preview-box {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  background: rgba(0, 0, 0, 0.6);
  z-index: 9999;
}

.preview-box .container {
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  min-height: 720rpx;
  background-color: #ffffff;
}

.preview-box .container .header {
  position: relative;
  width: 100%;
  height: 108rpx;
  line-height: 108rpx;
  text-indent: 30rpx;
  text-align: center;
  font-size: 34rpx;
  font-weight: bold;
  color: #353535;
}

.preview-box .container .header .close {
  position: absolute;
  top: 41rpx;
  right: 30rpx;
  width: 24rpx;
  height: 24rpx;
}

.preview-box .container .tip {
  padding: 0 30rpx;
  width: 100%;
  font-size: 26rpx;
  color: #7E7E7E;
  line-height: 32rpx;
  box-sizing: border-box;
}

.xy {
  width: calc(100% - 60rpx);
  height: 110rpx;
  font-size: 24rpx;
  font-weight: 400;
  color: rgba(163, 163, 163, 1);
  margin: 43rpx 30rpx 0 30rpx;
}

.yes_icon {
  width: 36rpx;
  height:36rpx;
  margin: 0 10rpx 0 0;
}
.text{
  width: calc(100% - 40rpx);
}