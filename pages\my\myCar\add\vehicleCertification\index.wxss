page {
  background: #F6F7FB;
}

.tip {
  width: calc(100% - 100rpx);
  height: 73rpx;
  font-size: 26rpx;
  font-weight: 400;
  color: #353535;
  line-height: 35rpx;
  margin: 20rpx 50rpx;
}

.text {
  width: 630rpx;
  min-height: 113rpx;
  font-size: 24rpx;

  font-weight: 400;
  color: #A3A3A3;
  line-height: 32rpx;
}

.carBox {
  width: calc(100% - 40rpx);
  min-height: 88rpx;
  background: #FFFFFF;
  opacity: 1;
  line-height: 88rpx;
  border-radius: 20rpx 20rpx 20rpx 20rpx;
  box-shadow: 0rpx 10rpx 0rpx 1rpx rgba(228, 234, 248, 0.16);
  margin: 20rpx 20rpx 0 20rpx;
}

.carBox.p {
  width: calc(100% - 80rpx);
  padding: 20rpx;
  flex-wrap: wrap;
  display: flex;
}

.carBox .imgBox {
  width: 320rpx;
  height: 200rpx;
  margin: 30rpx 0 0 10rpx;
  flex-direction: column;
  display: flex;
  align-items: center;
  flex-basis: auto;
  border-radius: 20rpx 20rpx 20rpx 20rpx;
}

.imgBox .img {
  width: 48rpx;
  height: 48rpx;
  margin-top: 48rpx;
}

.imgBox>.textTip {
  text-align: center;
  height: 40rpx;
  font-size: 28rpx;
  font-weight: 400;
  color: #FFFFFF;
  line-height: 32rpx;
  margin-top: 10rpx;
}

.titleBox {
  height: 40rpx;
  font-size: 28rpx;
  font-weight: 400;
  color: #7E7E7E;
  line-height: 54rpx;
  text-align: center;
}

.form-row {
  display: flex;
  padding: 10rpx 30rpx;
  width: 100%;
  height: 88rpx;
  background-color: #ffffff;
  border-bottom: 1px solid rgba(229, 229, 229, 1);
  box-sizing: border-box;
}

.form-row .form-label {
  font-size: 34rpx;
  margin-right: 30rpx;
  width: 142rpx;
  height: 100%;
  font-weight: 400;
  line-height: 68rpx;
}

.form-row .form-label.required::after {
  content: '*';
  color: #DA5937;
}

.form-row .form-value {
  height: 100%;
}
.form-row .form-value .input {
  width: 100%;
  height: 100%;
  font-size: 34rpx;
  font-weight: 400;

}

.noClike {
  background: #C0C0C0;
}

.carBox>.title {
  width: 100%;
  height: 48rpx;
  font-size: 34rpx;
  font-weight: 400;
  color: #000000;
  line-height: 54rpx;
  padding-left: 10rpx;
}
.xy {
  color: #256BF5;
}