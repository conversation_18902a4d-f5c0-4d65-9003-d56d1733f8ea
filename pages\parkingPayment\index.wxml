<view class="saveOutView">
  <cu-custom bgColor="white-bg" contentTitle="停车缴费" isBack="true"></cu-custom>
  <scroll-view class="scrollViewSa" scroll-y="true">
    <view wx:if="{{imageState!==0}}" class="rowView">
      <view class="cwxqzt">
        <image
          class="cwxqzt-img"
          mode="scaleToFill"
          src="{{info.parkingPicUrl}}"
          lazy-load="true"
          bindtap="previewSqs"
          data-src="{{info.parkingPicUrl}}"
          wx:if="{{info.parkingPicUrl!=null}}"
        ></image>
        <view class="cwxqzt-text" wx:else>暂无入车照片</view>
      </view>
      <view class="cwxqzt">
        <image
          class="cwxqzt-img"
          mode="scaleToFill"
          src="{{info.outImageUrl}}"
          lazy-load="true"
          bindtap="previewSqs"
          data-src="{{info.outImageUrl}}"
          wx:if="{{info.outImageUrl!=null}}"
        ></image>
        <view class="cwxqzt-text" wx:else>暂无出车照片</view>
      </view>
    </view>
    <view class="backWihte columnView paddingView-top" style="margin-top: 10rpx">
      <view class="title-value-box-line">
        <view class="title-value-box-line-title">停车场:</view>
        <view class="title-value-box-line-value">{{info.parkName}}</view>
      </view>
      <view class="title-value-box-line">
        <view class="title-value-box-line-title">车牌号码: </view>
        <view class="title-value-box-line-value">{{info.plateNo}}</view>
      </view>
      <view class="title-value-box-line">
        <view class="title-value-box-line-title">入场时间:</view>
        <view class="title-value-box-line-value">{{info.enterTime}}</view>
      </view>
      <view class="title-value-box-line">
        <view class="title-value-box-line-title">停车时长:</view>
        <view class="title-value-box-line-value">{{info.parkPeriodTime}}</view>
      </view>
      <!-- <view class="title-value-box-line">
        <view class="title-value-box-line-title">免费时长:</view>
        <view class="title-value-box-line-value">{{info.freeTime}}</view>
      </view> -->
    </view>
    <view class="backWihte columnView paddingView-top" style="margin-top: 10rpx">
      <view class="title-value-box-line">
        <view class="title-value-box-line-title">账单金额:</view>
        <view class="title-value-box-line-value" wx:if="{{info.evDeductMoney==0.0}}">¥{{info.billMoney}}</view>
        <view class="title-value-box-line-value" wx:else>
          <text class="xny">新能源车立减{{info.evDeductMoney}}元</text>
          <text class="lj">¥{{info.totalCost}}</text>
          <text class="zh">¥{{info.billMoney}}</text>
        </view>
      </view>
      <view class="title-value-box-line">
        <view class="title-value-box-line-title">优惠券:</view>
        <view class="title-value-box-line-value rowView">
          <view style="flex: 1; {{info.couponName==''?'':'color: #DA5937;'}}" bindtap="selectDiscountCoupon"
            >{{info.couponName==""?discountCoupon:info.couponName}}</view
          >
          <view class="icon-jt-left"></view>
        </view>
      </view>
      <view class="title-value-box-line">
        <view class="title-value-box-line-title">已付金额:</view>
        <view class="title-value-box-line-value">¥{{info.actualPaidMoney}}</view>
      </view>
      <view class="title-value-box-line">
        <view class="title-value-box-line-title">待付金额:</view>
        <view class="title-value-box-line-value">¥{{info.totalFee}}</view>
      </view>
    </view>
    <view class="backWihte columnView" style="margin-top: 10rpx">
      <view class="info-bt">
        <view class="icon-sjx-left-tilte"></view>
        <view class="info-bt-text">支付方式</view>
      </view>
      <view class="paymentListBox">
        <block wx:for="{{payList}}" wx:key="index">
          <view
            id="{{item.payType}}"
            class="infoBox  {{selectPay == item.payType ? 'choosePay':''}}"
            bindtap="selectPayMode"
            wx:if="{{item.payType=='BALANCE_PAY'}}"
          >
            <view class="infoBox-text" style="margin-top: 14rpx">余额</view>
            <view class="infoBox-text" id="ye" data-money="{{item.iconUrl}}">{{item.iconUrl}}元</view>
          </view>
          <view
            id="{{item.payType}}"
            class="infoBox {{selectPay == item.payType ? 'choosePay':''}}"
            style="display: flex; flex-direction: column"
            bindtap="selectPayMode"
            wx:else
          >
            <image
              class="img"
              src="{{item.iconUrl}}"
              style="width: 48rpx; height: 48rpx; position: static; margin: 14rpx auto 0"
            ></image>
            <view class="infoBox-text">微信支付</view>
          </view>
        </block>
      </view>
    </view>
    <view class="infoBox-money">
      <view class="infoBox-pay" style="margin-left: 52rpx">待付金额:</view>
      <view class="infoBox-pay" style="color: #ff2d2d">¥{{info.totalFee}}</view>
    </view>
    <view class="box_convention_button">
      <button class="convention_button" bindtap="pay">支付</button>
    </view>
  </scroll-view>
</view>
