<view class="saveOutView">
  <cu-custom bgColor="white-bg" contentTitle="{{title}}" isBack="true"></cu-custom>
  <view wx:if="{{ list.length == 0 }}">
    <image src="../../../image/qfjf-null.png" class="nullImg"></image>
    <view class="nullTip">您还没有{{type=="parkingBillAppeal"?"停车订单":title}}</view>
  </view>
  <scroll-view wx:else class="scrollViewSa" scroll-y="true" bindscrolltolower="bindReachBottom">
    <block wx:for="{{list}}" wx:key="index">
      <view class="listDiv-d" bindtap="select" style="{{index==0 ?'margin-top:20rpx':''}}" catchtap="goXq" id="{{item.recordId}}" data-recordtype="{{item.recordBizType}}">
        <view class="listDiv-d-left" wx:if="{{type=='feedback'}}">
          <view class="listDiv-search-name ">{{item.recordTitle}}：{{item.recordContent}}</view>
          <view class="listDiv-search-name-xq">反馈时间：{{item.createTime}}</view>
          <view class="listDiv-search-name-xq">反馈状态：{{item.stateText}}</view>
        </view>
        <view class="listDiv-d-left" catchtap="goXq" id="{{item.id}}" data-name="{{item.parkName}}" data-p="{{item.plateNo}}" data-time="{{item.parkStartTime}}" data-timed="{{item.timeDesc}}" data-amount="{{item.amount}}" data-status="{{item.status}}" data-uniqueid="{{item.uniqueId}}" data-parkcode="{{item.parkCode}}" data-endtime="{{item.parkEndTime}}" data-recordtype="{{item.recordBizType}}" wx:else>
          <view class="listDiv-search-name ">{{item.parkName}}</view>
          <view class="listDiv-search-name-xq">{{item.plateNo}}</view>
          <view class="listDiv-search-name-xq">{{item.parkStartTime}} {{item.timeDesc}}</view><!-- 海康只返回了开始停车时间 字段为：parkStartTime-->
          <view class="rowView">
            <view class="state {{statusClass[item.status]}}">{{item.statusDesc}}</view>
            <view class="">{{item.status=='PAID'?"已付：":"欠费"}}￥{{item.amount}}</view>
          </view>
        </view>
      </view>
    </block>
  </scroll-view>
</view>