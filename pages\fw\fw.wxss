/* pages/fw/fw.wxss */

@import '/pages/common/common.wxss';

page {
  background-color: #f6f6f6;
}

.center-one {
  flex-wrap: wrap;
  display: flex;
  margin-top: 46rpx;
  width: calc(100% - 100rpx);
  margin: 20rpx 50rpx;
  min-height: 544rpx;
  background: #FFFFFF;
  box-shadow: 0rpx 10rpx 20rpx 0rpx rgba(228, 234, 248, 0.5);
  border-radius: 20rpx;
}

.center-one-v {
  flex-direction: column;
  display: flex;
  align-items: center;
  margin: 24rpx 57rpx 24rpx 61rpx;
  flex-basis: auto;
  width: 100rpx;
  height: 120rpx;
}
.center-one-v-img {
  margin-top: 0;
  width: 90rpx;
  height: 90rpx;
  justify-content: center;
}

.center-one-v-img image,
.center-two-v-img image {
  width: 100%;
  height: 100%;
}

.center-one-v-text {
  font-weight: 400;
  line-height: 26rpx;
  margin-top: 10rpx;
  width: 100%;
  color: #353535;
  text-align: center;
  min-width: 86rpx;
  height: 26rpx;
  font-size: 24rpx;

  font-weight: 500;
}