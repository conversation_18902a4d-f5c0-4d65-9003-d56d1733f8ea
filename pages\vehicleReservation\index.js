var util = require('../../utils/util')
var api = require('../../config/api.js')
var app = getApp()
Page({
  /**
   * 页面的初始数据
   */
  data: {
    test: true,
    isClick: true,
    pageSize: 20,
    list: {
      currentPage: 1,
      records: [],
    },
  },
  onLoad(options) {},
  onShow() {
    this.setData({
      list: {
        currentPage: 1,
        records: [],
      },
    })
    this.getRecordData()
  },
  getRecordData: function () {
    let info = this.data.list
    let params = {
      currentPage: info.currentPage,
      pageSize: this.data.pageSize,
      'sorts[0].field': 'addTime',
      'sorts[0].orderBy': 'DESC',
    }
    var that = this
    util.showLoading('正在加载…')
    util.request(api.getReservationRecord, params, 'GET').then(function (res) {
      wx.hideLoading()
      let { code, result } = res || {}
      let records = info.records
      if (code == '0') {
        records = records.concat(result.records)
      } else {
        util.showToast(res.message)
      }
      let key = 'list.records'
      let data = {}
      data[key] = records
      that.setData(data)
    })
  },
  cancel: function (info) {
    var id = info.detail.id
    util.showLoading('正在加载…')
    util.request(api.cancelReservationPreview + '/' + id, '', 'GET').then(function (res) {
      wx.hideLoading()
      let { code, result } = res || {}
      if (code == '0') {
        if (result.cancelable == 0) {
          //不可取消
          util.showToast(result.explain) //展示不可以取消的原因
        } else {
          wx.showModal({
            title: '取消提醒',
            content: result.cancelMessage,
            confirmText: '确定取消',
            cancelText: '保留预约',
            confirmColor: '#4768F3',
            success(res) {
              if (res.confirm) {
                wx.navigateTo({
                  url: '/pages/vehicleReservation/cancel/index?id=' + id,
                })
              } else if (res.cancel) {
                console.log('用户点击取消')
              }
            },
          })
        }
      } else {
        util.showToast(res.message)
      }
    })
  },
  loadData() {
    let list = this.data.list
    list.records = []
    list.currentPage = 1
    this.setData({
      list,
    })
    this.getRecordData()
  },
  moreData() {
    console.log('moreData')
    // let info = this.data.list[index]
    // let currentPage = info.currentPage + 1
    let key = 'list.currentPage'
    let data = {}
    // data[key] = currentPage
    this.setData(data)
    this.getRecordData()
  },
  scrolltolower() {
    console.log('scrolltolower')
  },
  add(a) {
    util.showLoading('正在加载…')
    util
      .request(
        api.getParkingInfo,
        {
          resourceType: 'PARKING_LOT',
          latitude: app.globalData.latitude,
          longitude: app.globalData.longitude,
          id: a.detail.parkId,
        },
        'GET'
      )
      .then(function (res) {
        wx.hideLoading()
        if (res.code == '0') {
          wx.navigateTo({
            url: '/pages/vehicleReservation/add/index',
            success: resNa => {
              resNa.eventChannel.emit('setData', {
                data: res.result,
              })
            },
          })
        } else {
          wx.hideLoading()
          util.showToast(res.message)
        }
      })
  },
  goXq(a) {
    wx.navigateTo({
      url: '/pages/vehicleReservation/details/index?id=' + a.detail.id,
    })
  },
})
