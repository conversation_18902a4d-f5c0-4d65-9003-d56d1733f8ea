const app = getApp()
var util = require('../../utils/util')
var api = require('../../config/api.js')

Page({
  data: {
    state: [
      //停车订单类型:1-正在停车，2-欠费补缴，3-已完成
      {
        name: '欠费补缴',
        id: 2,
      },
      {
        name: '正在停车',
        id: 1,
      },
      {
        name: '已完成',
        id: 3,
      },
    ],
    currentId: 2,
    pageHeight: 0,
    orderTypes: [
      {
        name: '停车订单',
        id: 'ARREARS',
      },
      //  ,
      //  {
      //    name: '预约订单',
      //    id: 'RESERVATION_SPACE'
      //  }
    ],
    currentTypeIndex: 0, //当前选中的
    currentType: '',
    list: [],
    triggered: false,
    showBottom: false,
    currentPage: 1,
    pageSize: 50,
    recordTotalNum: 0,
  },
  onLoad(options) {
    console.log('setdata', options)
    let height = app.globalData.windowHeight - app.globalData.CustomBar + 48 // 48是tabbar的高度
    this.setData({
      pageHeight: height,
      currentTypeIndex: 0,
      currentType: this.data.orderTypes[0].id,
      currentId: options.currentId ? +options.currentId : 2,
    })
    this.initData()
  },
  onShow() {
    console.log('show')
    this.loadData()
  },
  initData() {},
  loadData() {
    let currentPage = 1
    this.setData({
      currentPage,
      list: [],
    })
    this.getRecordData(currentPage)
  },
  moreDate() {
    if (this.data.list.length >= this.data.recordTotalNum) {
      console.log('没有更多了')
      this.setData({
        showBottom: false,
      })
      return
    }
    let currentPage = this.data.currentPage
    currentPage++
    this.setData({
      currentPage,
      showBottom: false,
    })
    this.getRecordData(currentPage)
  },
  changeState(e) {
    const { id } = e.currentTarget.dataset
    this.setData({
      currentId: id,
    })
    this.loadData()
  },
  changeType(e) {
    console.log('changeType', e.detail.value)
    let index = e.detail.value
    this.setData({
      currentTypeIndex: index,
      currentType: this.data.orderTypes[index].id,
    })
    this.loadData()
  },
  clickItem(e) {
    var data = this.data
    wx.navigateTo({
      url: '/pages/orderQuery/detail',
      success: res => {
        res.eventChannel.emit('setData', {
          data: e.currentTarget.dataset.info,
          type: data.orderTypes[data.currentTypeIndex].id,
          state: this.data.currentId,
        })
      },
    })
  },
  gotoInvoice() {
    console.log('goto')
    wx.navigateTo({
      url: '/pages/invoice/menu',
    })
  },
  onScrollRefresh(e) {
    console.log('onScrollRefresh', e)
    this.loadData()
  },
  onScrollTolower(e) {
    console.log('到底部', e)
    this.setData({
      showBottom: true,
    })
    this.moreDate()
  },
  getRecordData: function (currentPage) {
    var data = this.data
    var url, param
    if (this.data.currentTypeIndex == 0) {
      //停车订单
      url = api.getArrearsInOder
      param = {
        orderType: this.data.currentId, //停车订单类型 ，1-正在停车，2-欠费补缴，3-已完成
        currentPage: currentPage,
        pageSize: data.pageSize,
      }
    } else {
      url = api.getReservationRecord
      param = {
        currentPage: currentPage,
        pageSize: data.pageSize,
        'sorts[0].field': 'addTime',
        'sorts[0].orderBy': 'DESC',
      }
    }
    console.log('param', param)
    var that = this
    util.showLoading('正在加载…')
    util
      .request(url, param, 'GET')
      .then(function (res) {
        wx.hideLoading()
        let { code, result } = res || {}
        let records = that.data.list
        if (code == '0') {
          records = records.concat(result.records)
        } else {
          util.showToast(res.message)
        }
        let data = {
          recordTotalNum: result.total,
          list: records,
        }
        if (currentPage == 1) {
          data.triggered = false
        } else {
          data.showBottom = false
        }
        that.setData(data)
      })
      .catch(err => {
        wx.hideLoading()
        let data = {}
        if (currentPage == 1) {
          data.triggered = false
        } else {
          data.showBottom = false
        }
        that.setData(data)
        util.showToast('服务异常，请稍后重试')
      })
  },

  goxq(e) {
    var currentTarget = e.currentTarget
    var id = currentTarget.id
    wx.navigateTo({
      url: '/pages/supplementaryPayment/details/index?uniqueId=' + id,
    })
  },
})
