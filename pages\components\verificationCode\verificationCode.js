var util = require('../../../utils/util');
var api = require('../../../config/api.js');
var app = getApp();
Component({
  /**
   * 组件的对外属性
   */
  properties: {
    phone: {
      type: String,
      default: ''
    },
    showVerification: {
      type: Boolean,
      default: false
    },
    showSuccess: {
      type: Boolean,
      default: false
    },
    successText: {
      type: String,
      default: ''
    },
    type: {
      type: String,
      default: ''
    },
    isClick: {
      type: Boolean,
      default: true
    }//防止连续多次点击
  },
  /**
   * 组件的初始数据
   */
  data: {
    phoneIsTrue: true, //手机号码是否正确
    currentTime: 60,
    interval: null, //倒计时函数
    showSendCodeBtn: true, //显示倒计时时间
    code: '', //验证码
    verificationId: '',
  },
  attached() {
    console.log(this.data.currentTime)
  },
  observers: {
    'showVerification': function (val) {
      if (val) {
        this.getVerificationCode()
      } else {
        clearInterval(this.data.interval)
      }
    }
  },
  /**
   * 组件的方法列表
   */
  methods: {
    BackPage() {
      wx.switchTab({
        url: '/pages/my/my'
      })
    },
    bindKeyInput: function (e) {
      var code = e.detail.value;
      this.setData({
        code: code
      })
    },
    getCode: function (options) {
      console.log('getCode')
      var that = this;
      var currentTime = that.data.currentTime
      var i = setInterval(function () {
        currentTime--;
        that.setData({
          currentTime: currentTime
        })
        if (currentTime <= 0) {
          clearInterval(that.data.interval)
          that.setData({
            phoneIsTrue: true,
            currentTime: 60,
            showSendCodeBtn: false,
          })
        }
      }, 1000)
      this.setData({
        interval: i,
        phoneIsTrue: false
      })
    },
    getVerificationCode() {
      util.showLoading('正在加载…');
      var that = this
      that.setData({
        showSendCodeBtn: true
      })
      var type = that.data.type;
      if (type == 'UPDATE_PHONE') { //修改手机号码
        util.request(api.getCodeByModifiPhone, {
          "phone": that.data.phone,
          "verificationType": type
        }, 'POST').then(function (res) {
          if (res.code == '0') {
            wx.hideLoading();
            that.setData({
              verificationId: res.result,
              phoneIsTrue: true
            })
            that.getCode();
          } else {
            wx.hideLoading();
            that.setData({
              phoneIsTrue: false,
              currentTime: 60,
              showSendCodeBtn: false
            })
            clearInterval(that.data.interval)
            wx.showModal({
              title: '失败提醒',
              content: res.message,
              showCancel: false,
              confirmColor: app.globalData.tipBtnColor,
              success(res) {
                if (res.confirm) {
                  that.triggerEvent("cancel")
                } else if (res.cancel) {
                  console.log('用户点击取消')
                }
              }
            })
          }
        });
      } else { //注销
        util.request(api.getCodeByLogoff, "", 'POST').then(function (res) {
          if (res.code == '0') {
            wx.hideLoading();
            that.setData({
              verificationId: res.result,
              phoneIsTrue: true
            })
            that.getCode();
          } else {
            wx.hideLoading();
            that.setData({
              phoneIsTrue: false,
              currentTime: 60,
              showSendCodeBtn: false
            })
            clearInterval(that.data.interval)
            wx.showModal({
              title: '失败提醒',
              content: res.message,
              showCancel: false,
              confirmColor: app.globalData.tipBtnColor,
              success(res) {
                if (res.confirm) {
                  that.triggerEvent("cancel")
                } else if (res.cancel) {
                  console.log('用户点击取消')
                }
              }
            })
          }
        });
      }
    },
    sure(e) {
      var that = this;
      var data = that.data;
      var code = data.code;
      if (code.length < 6) {
        util.showToast("请输入正确的验证码")
      } else { 
        if(data.isClick){
          util.showLoading('正在加载…')
          that.triggerEvent("sure", {
            verificationId: data.verificationId,
            code: data.code
          })
          that.setData({
            isClick:false
          })
        } 
      }
    }
  },
  pageLifetimes: {
    show: function () {
      // 页面被展示
    },
    hide: function () {
      clearInterval(this.data.interval)
    },
    resize: function (size) {
      // 页面尺寸变化
    }
  }
})