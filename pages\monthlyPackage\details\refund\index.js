var util = require('../../../../utils/util');
var api = require('../../../../config/api.js');
const app = getApp()
Page({
  data: {
    showSucc:false,
    certCode: "",
    info:{}
  },
  onLoad(options) {
    this.setData({
      certCode: options.certCode
    })
  },
  onShow() {
    this.getInfo();
  },
  getInfo: function () { 
    var that = this;
    util.showLoading("正在加载...")
    util.request(api.monthlyCancelPreview, {
      certCode: that.data.certCode
    }, 'GET').then(function (res) {
      var info = res.result;
      if (res.code == '0') {
        that.setData({
          info: info
        })
        wx.hideLoading()
      } else {
        wx.hideLoading()
        util.showToast(res.message);
      }
    });
  },
  refunding() {
    var that =this;
    util.showLoading('正在加载…')
    util.request(api.monthlyCancel+"?certCode="+that.data.certCode
     , {}, 'DELETE').then(function (res) {
      if (res.code == '0') {//成功后就返回信息了
        that.setData({
          showSucc:true
        })
        wx.hideLoading()
      } else {
        wx.hideLoading()
        util.showToast(res.message);
      }
    });
  },
  back(){
    wx.navigateBack({
      delta: 2
    })
  }
})