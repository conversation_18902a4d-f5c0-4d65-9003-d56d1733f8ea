@font-face {
  font-family: 'glicon';
  src: url('data:font/ttf;charset=utf-8;base64,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')
    format('truetype');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

[class^='glicon-'],
[class*=' glicon-'] {
  font-family: 'glicon' !important;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1; /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.glicon {
  font-family: 'glicon' !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.glicon-guanbi:before {
  content: '\e67d';
}
