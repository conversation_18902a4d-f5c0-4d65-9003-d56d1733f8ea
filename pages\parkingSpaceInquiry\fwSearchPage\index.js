var api = require('../../../config/api.js');
var util = require('../../../utils/util');
const app = getApp();
Page({

  /**
   * 页面的初始数据
   */
  data: {
    historyList: [], //历史记录列表
    showH: true, //显示历史记录
    placeholderText:{
     default: '查找停车位/公厕/加油站/充电站',
     CHARGE: '查找目的地/充电桩',
     FILLING_STATION: '查找目的地/加油站',
     TOILET: '查找目的地/公厕',
     reservation: '查找目的地/可预约车场'
    },
    isV:false,//判断跳转的页面是否是只有预约的车位
    type: "",//当前类型
    goback: "",
    lat: null,
    lng: null,
    keyword: '', //搜索关键字
    onSearch: false,
    currentPage: 1,
    showNullMoreTip: false,
    list: [],
    showNullTip: false,
    pageSize: 20, //分页大小
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    var that = this;
    var type = 'default';
    if(options.type!=undefined){
      type = options.type;
    }
    that.setData({
      lat: options.lat,
      lng: options.lng,
      goback: options.goback,
      type: type,
      isV:options.isV
    });
    var key = type+'history';
    console.log(key);
    wx.getStorage({
      key: key,
      success(res) {
        that.setData({
          historyList: res.data
        });
      }
    })
  },
  backPage: function (e) { //回到前一页
    var that = this;
    var dataset = e.currentTarget.dataset;
    var latitude = dataset.latitude;
    var longitude = dataset.longitude;
    var title = dataset.title;
    var save = dataset.save;
    var url = "../index?keyword=" + title + "&latitude=" + latitude + "&longitude=" + longitude
    if (this.data.goback == "fw") {
      url = "../fwMap/index?keyword=" + title + "&latitude=" + latitude + "&longitude=" + longitude + "&type=" + that.data.type+"&isV="+that.data.isV
    }
    wx.redirectTo({
      url: url + "&isV="+that.data.isV
    })
    if(save!=undefined){
      if(save=='true'){
        
        that.saveHistoryList(dataset.info);
      }
    }
  },
  /**
   * 进入搜索
   */
  keywordfocus: function (e) {
    this.setData({
      onSearch: true,
      currentPage: 1,
      showH: false
    })
  },
  /**
   * 退出搜索
   */
  offSearch: function () {
    this.setData({
      keyword: "",
      onSearch: false,
      currentPage: 1,
      list: [],
      showNullTip: false,
      showNullMoreTip: false,
      showH: true
    })
  },
  /**
   * 获取搜索关键字
   */
  getKeyword(e) {
    let that = this;
    let keyword = e.detail.value;
    console.log(keyword)
    that.setData({
      keyword: keyword
    })
    that.bindconfirm() //实时搜索
  },
  /**
   * 手机键盘确定搜索键
   */
  bindconfirm: function () {
    this.setData({
      currentPage: 1,
      showNullMoreTip: false,
      list: [],
      showNullTip: false,
      showH: false
    })
    this.getList();
  },
  getList: function () {
    var that = this;
    var keyword = that.data.keyword;
    keyword = keyword.replace(/\s*/g, "");
    if (keyword !== "") {
      util.showLoading('正在加载…')
      var currentPage = that.data.currentPage;
      util.request(api.fwSearchParkingPoi, {
        "keyword": keyword,
        "lat": that.data.lat,
        "lng": that.data.lng,
        "policy": 0,
        "region": '',
        "pageSize": that.data.pageSize,
        "currentPage": currentPage,
      }, 'GET').then(function (res) {
        if (res.code == '0') {
          wx.hideLoading();
          if (res.result.total == 0) {
            if (currentPage == 1) {
              //没有数据
              that.setData({
                list: [],
                showNullTip: true,
                showNullMoreTip: false
              });
            } else {
              //没有更多数据
              that.setData({
                showNullMoreTip: true,
                showNullTip: false
              });
            }
          } else {
            var records = res.result.records;
            that.setData({
              list: that.data.list.concat(records),
              showNullTip: false,
              showNullMoreTip: false
            });
            if (res.result.pages == currentPage) {
              //没有数据
              that.setData({
                showNullMoreTip: true
              });
            }
          }
          //加载下一页
          that.setData({
            currentPage: currentPage + 1,
          });

        } else {
          wx.hideLoading();
          util.showToast(res.message)
        }
      });
    }
  },
  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {
    this.setData({
      pageSize: 20,
      currentPage: 1,
      list: []
    })
    this.getList();
    wx.stopPullDownRefresh();
  },
  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {
    if (this.data.showNullMoreTip) {
      util.showToast('没有更多数据了')
    } else {
      this.getList();
    }
  },
  openMapApp: function (e) {
    var currentTarget = e.currentTarget;
    var latitude = currentTarget.dataset.lat;
    var longitude = currentTarget.dataset.long;
    var name = currentTarget.dataset.name;
    var address = currentTarget.dataset.address;
    wx.openLocation({
      longitude: longitude,
      latitude: latitude,
      scale: app.globalData.scale,
      name: name,
      address: address
    })
  },
  saveHistoryList: function (info) {
    var data =  this.data;
    var oldHistoryList = data.historyList;
    var index = oldHistoryList.findIndex(function (item) {return item.id == info.id; });
    if (index != -1) { //有搜索记录，删除之前的旧记录，将新搜索值重新push到数组首位
      oldHistoryList.splice(index, 1)
    }
    oldHistoryList.unshift(info);
    var key = data.type+'history';
    wx.setStorage({ //存储历史搜索记录
      key: key,
      data: oldHistoryList
    })
  },
  clearHistoryList:function(){//清空历史记录
    this.setData({
      historyList: []
    });
    var key = this.data.type+'history';
    wx.setStorage({ //清空存储历史搜索记录
      key: key,
      data: []
    })
  }
  ,
})