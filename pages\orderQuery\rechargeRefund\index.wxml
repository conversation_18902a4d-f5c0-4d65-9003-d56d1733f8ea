<cu-custom bgColor="white-bg" contentTitle="充值订单退款" isBack="true" bind:back="backList"></cu-custom>
<view class="container" style="height: calc({{pageHeight}}px - 60rpx);">
  <view class="info-box" hidden="{{refundState}}">
    <view class="header">
      <view class="money"><text class="symbol">￥</text><text class="num">{{info.refundableAmount}}</text></view>
      <view class="tip">{{info.refundableAmount=='0.00'?'当前余额不足':'可退额度'}}</view>
    </view>
    <view class="row">
      <view class="label">当前总余额：</view>
      <view class="value">¥{{info.balance}}</view>
    </view>
    <view class="row">
      <view class="label">{{info.refundableAmount=='0.00'?'本订单可退金额:':'充值额度：'}}</view>
      <view class="value">¥{{info.payAmount}}</view>
    </view>
    <view class="row">
      <view class="label">订单编号：</view>
      <view class="value">{{info.tradeNo}}</view>
    </view>
    <view class="footer">从余额退款，可退额度不超过当前余额</view>
  </view>
  <view class="bottomBtn" hidden="{{refundState}}">
    <view class="btn noClick" wx:if="{{info.refundableAmount=='0.00'}}">退款</view>
    <view class="btn" bindtap="refund" wx:else>退款</view>
  </view>
  <view class="state-box" hidden="{{!refundState}}">
    <view class="header">
      <!-- <view class="left"></view> -->
      <image class="left" src="/image/yes-yhj.png" />
      <view class="right">
        <view class="state">退款成功</view>
        <view class="money">已退：¥{{refund.backFee}}</view>
      </view>
    </view>
    <view class="row">
      <view class="label">退款时间：</view>
      <view class="value">{{refund.refundTime}}</view>
    </view>
    <view class="row">
      <view class="label">退款金额：</view>
      <view class="value">¥{{refund.refundFee}}</view>
    </view>
    <view class="row">
      <view class="label">退款渠道：</view>
      <view class="value">{{refund.refundChannel}}</view>
    </view>
  </view>
</view>