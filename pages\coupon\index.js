var util = require('../../utils/util')
var api = require('../../config/api')
var app = getApp()
Page({
  /**
   * 页面的初始数据
   */
  data: {
    list: [],
    show: false,
    name: '',
    content: '',
    parkCodes: '',
    activityStartTime: '',
    activityEndTime: '',
    parkInfo: '',
  },
  onLoad(options) {
    if (options.q) {
      const obj = util.getURLParameters(decodeURIComponent(options.q))
      if (!obj.couponCode) {
        return util.showToast('无效二维码')
      }
      this.code = obj.couponCode
      app.globalData.couponCode = this.code
      this.getList()
    } else {
      this.code = app.globalData.couponCode
      this.getList()
    }
  },
  getList: function () {
    var that = this
    util.showLoading('正在加载...')
    util
      .request(api.getCouponPackage, { code: this.code }, 'GET')
      .then(function (res) {
        var list = []
        wx.hideLoading()
        let name = ''
        let content = ''
        let parkCodes = ''
        let activityStartTime = ''
        let activityEndTime = ''
        let parkInfo = ''
        if (res.code == 200) {
          const result = res.result
          name = result.name
          content = result.content
          parkCodes = result.parkCodes
          activityStartTime = result.activityStartTime
          activityEndTime = result.activityEndTime
          parkInfo = result.parkInfo || '仅限本账号使用，适用车场以实际使用为准'
          if (result.deductionCount) {
            list.push({
              type: '减免券',
              count: result.deductionCount,
              value: '￥' + (result.deductionCent / 100).toFixed(2),
              availableStartTime: result.availableStartTime,
              availableEndTime: result.availableEndTime,
            })
          }
          if (result.discountCount) {
            list.push({
              type: '折扣券',
              count: result.discountCount,
              value: result.discountPercent / 10 + '折',
              availableStartTime: result.availableStartTime,
              availableEndTime: result.availableEndTime,
            })
          }
          if (result.timeReductionCount) {
            list.push({
              type: '减时券',
              count: result.timeReductionCount,
              value: result.timeReductionMinute + '分钟',
              availableStartTime: result.availableStartTime,
              availableEndTime: result.availableEndTime,
            })
          }
        } else {
          util.showToast(res.message)
        }
        that.setData({
          list,
          name,
          content,
          parkCodes,
          activityStartTime,
          activityEndTime,
          parkInfo,
        })
      })
      .catch(err => {
        wx.hideLoading()
        util.showToast('出错了' + JSON.stringify(err))
      })
  },
  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {
    this.setData({
      currentPage: 1,
      list: [],
    })
    this.getList()
    wx.stopPullDownRefresh()
  },
  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {
    if (this.data.showNullMoreTip) {
      util.showToast('没有更多数据了')
    } else {
      this.getList()
    }
  },
  openOrClose: function () {
    this.setData({
      show: !this.data.show,
    })
  },
  async getPhone() {
    try {
      const res = await util.request(
        api.getFullPhone,
        { driverId: app.globalData?.userInfo?.driverId, encryptedPhone: app.globalData?.userInfo?.phone },
        'GET'
      )
      if (res.code == 200) {
        this.mobile = res.result
      } else {
        wx.hideLoading()
        util.showToast(res.message)
      }
    } catch (error) {
      wx.hideLoading()
      util.showToast('出错了' + JSON.stringify(error))
    }
  },
  async onGet() {
    util.showLoading('正在加载...')
    if (app.globalData?.userInfo?.loginStatus !== 'LOGGED_IN') {
      try {
        await util.fetchToken()
      } catch (error) {
        wx.hideLoading()
      }
    }
    if (app.globalData?.userInfo?.loginStatus != 'LOGGED_IN') {
      wx.navigateTo({ url: '/pages/common/loginTip/loginTip?route=/pages/coupon/index' })
      wx.hideLoading()
      return
    }

    const { result } = await util.request(api.getArrearsList, { currentPage: 1, pageSize: 1 }, 'GET')
    if (result?.records?.length > 0) {
      util.showToast('存在未缴费订单，请缴费后再领取优惠券')
      return
    }

    await this.getPhone()

    util
      .request(api.getCoupon, { code: this.code, mobile: this.mobile }, 'GET')
      .then(function (res) {
        wx.hideLoading()
        if (res.code == 200) {
          util.showToast('领取成功')
          setTimeout(() => {
            wx.reLaunch({ url: '/pages/index/index' })
          }, 500)
        } else {
          util.showToast(res.message)
        }
      })
      .catch(err => {
        wx.hideLoading()
        util.showToast('出错了' + JSON.stringify(err))
      })
  },
  onBack() {
    wx.reLaunch({ url: '/pages/index/index' })
  },
})
