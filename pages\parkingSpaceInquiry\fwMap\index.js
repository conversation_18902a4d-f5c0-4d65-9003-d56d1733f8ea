var util = require('../../../utils/util')
var api = require('../../../config/api.js')
const app = getApp()
Page({
  data: {
    imgUrl: api.imgUrl,
    scale: app.globalData.scale, //地图层级
    jnTop: app.globalData.jnTop,
    swidth: 0,
    sheight: 0,
    markers: [],
    mapCtx: null,
    latitude: app.globalData.latitude, //中心点
    longitude: app.globalData.longitude,
    currentPage: 1,
    pageSize: 50, //分页大小
    is_selected: '',
    filter: 'ALL', //RESERVE,DISCOUNT,CHARGE,TOILET,ALL
    isShowPxSelect: false,
    orderByText: '距离最近',
    orderBy: 'DISTANCE', //DISTANCE表示距离最近，FREE表示免费最久，SURPLUS表示余位最多),可用值:DISTANCE,FREE,SURPLUS
    pslist: [],
    tcdNum: 0,
    listDivHeight: '501rpx', //calc(100% - 185rpx)  600rpx
    btntext: '点击查找更多', //点击查找更多 收回
    toolSelectTop: '30%', //-100%
    keyword: '查找停车场/地点',
    isSearch: false, //是否是搜索页面返回的
    showNullMoreTip: false,
    showNullTip: false,
    event: null, //保存地图触发事件
  },
  onLoad: function (options) {
    //只会调用一次
    var swidth = wx.getSystemInfoSync().windowWidth
    var sheight = wx.getSystemInfoSync().windowHeight - util.rpxToPx(500)
    var that = this
    var type = options.type
    var keyword = options.keyword
    if (keyword != undefined) {
      that.setData({
        isSearch: true,
        keyword: keyword,
        latitude: options.latitude,
        longitude: options.longitude,
        swidth: swidth,
        sheight: sheight,
        filter: type,
      })
    } else {
      if (type == 'CHARGE') {
        keyword = '查找地点/充电站'
      } else if (type == 'TOILET') {
        keyword = '查找公厕'
      } else if (type == 'SCENIC_AREA') {
        keyword = '查找景点'
      } else {
        keyword = '查找加油站'
      }
      that.setData({
        isSearch: false,
        keyword: keyword,
        swidth: swidth,
        sheight: sheight,
        filter: type,
      })
    }
  },
  onShow: function (e) {
    this.mapCtx = wx.createMapContext('myMap') //创建 map 上下文 MapContext 对象
    this.initData()
  },
  initData: function () {
    let that = this
    wx.getSetting({
      success: res => {
        if (res.authSetting['scope.userLocation'] === false) {
          // 提示用户授权
          wx.showModal({
            title: '提示',
            content: '您拒绝了授权，将无法为您自动搜索到周围车场的信息，请授权后重试',
            success: res => {
              if (res.confirm) {
                wx.openSetting({
                  // 打开设置页面
                  success: res => {
                    if (res.authSetting['scope.userLocation'] === true) {
                      that.getUserInfo()
                    }
                  },
                })
              } else {
                //点击取消
                that.getInfo(app.globalData.latitude, app.globalData.longitude) //使用默认经纬度进行查询
              }
            },
          })
        } else {
          // 已授权，调用wx.getLocation
          that.getUserInfo()
        }
      },
    })
  },
  getUserInfo: function () {
    let that = this
    var data = that.data
    var latitude = null
    var longitude = null
    if (data.isSearch) {
      //有搜索关键字
      latitude = data.latitude
      longitude = data.longitude
      that.getInfo(latitude, longitude)
    } else {
      //没有搜索关键字时用当前的位置
      wx.getLocation({
        //获取当前位置
        type: 'gcj02',
        success(res) {
          latitude = res.latitude
          longitude = res.longitude
          that.setData({
            latitude: latitude,
            longitude: longitude,
          })
          that.getInfo(latitude, longitude)
        },
        fail(res) {
          util.showToast(res)
        },
      })
    }
  },
  getInfo: function (latitude, longitude) {
    this.getMapMakers(latitude, longitude)
    this.getMapMakerList(latitude, longitude)
  },
  getMapMakers: function (latitude, longitude) {
    util.showLoading('正在加载…')
    let that = this
    var data = that.data
    util
      .request(
        api.getMapMakers,
        {
          latitude: latitude,
          longitude: longitude,
          orderBy: data.orderBy,
          filter: data.filter,
        },
        'GET'
      )
      .then(function (res) {
        var markers = res.result
        if (res.code == '0') {
          markers = markers.concat({
            id: 0,
            latitude: latitude,
            longitude: longitude,
            iconPath: data.imgUrl + 'iMarker.png?ra=' + Math.random(),
            width: 25,
            height: 30,
          }) //追加新数据
          that.setData({
            markers: markers,
          })
        } else {
          util.showToast(res.message)
        }
      })
  },
  getMapMakerList: function (latitude, longitude) {
    util.showLoading('正在加载…')
    let that = this
    var data = that.data
    var currentPage = data.currentPage
    var pageSize = data.pageSize
    util
      .request(
        api.getMapMakerList,
        {
          latitude: latitude,
          longitude: longitude,
          currentPage: currentPage,
          filter: data.filter,
          orderBy: data.orderBy, //DISTANCE表示距离最短，FREE表示免费最久，SURPLUS表示余位最多，DISCOUNT优惠车场),可用值:DISTANCE,FREE,SURPLUS
          pageSize: pageSize,
        },
        'GET'
      )
      .then(function (res) {
        var result = res.result
        var pslist = result.records
        if (res.code == '0') {
          var total = result.total
          if (total == 0) {
            if (currentPage == 1) {
              //没有数据
              that.setData({
                pslist: [],
                showNullTip: true,
                showNullMoreTip: false,
                tcdNum: 0,
              })
            } else {
              //没有更多数据
              that.setData({
                showNullMoreTip: true,
                showNullTip: false,
              })
            }
          } else {
            that.setData({
              pslist: that.data.pslist.concat(pslist),
              showNullTip: false,
              tcdNum: total,
            })
            if (total <= pageSize || pslist.length == 0) {
              //没有更多数据
              that.setData({
                showNullMoreTip: true,
              })
            } else {
              that.setData({
                showNullMoreTip: false,
              })
            }
          }
          //加载下一页
          that.setData({
            currentPage: currentPage + 1,
          })
          wx.hideLoading()
        } else {
          wx.hideLoading()
          util.showToast(res.message)
        }
      })
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {
    var data = this.data
    this.setData({
      currentPage: 1,
      pslist: [],
    })
    this.getMapMakerList(data.latitude, data.longitude)
    wx.stopPullDownRefresh()
  },
  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {
    var that = this
    var data = that.data
    if (data.showNullMoreTip) {
      util.showToast('没有更多数据了')
    } else {
      that.getMapMakerList(data.latitude, data.longitude)
    }
  },
  markertap: function (e) {
    console.log('点击标记点时触发' + e)
    // 根据标记点的 id 来显示详情
    var _this = this
    var markerId = e.markerId
    if (markerId != null) {
      if (markerId == 0) {
        return
      }
      var resourceType = ''
      //循环遍历数据
      var lat, lng
      var data = _this.data
      let markers = data.markers //获取到需要遍历的集合数据
      var marker = [] //修改为只展示选中的那个图标
      markers.forEach(function (item, index) {
        if (item.id == markerId) {
          resourceType = item.type
          lat = item.latitude
          lng = item.longitude
          item['width'] = 40
          item['height'] = 50
          marker.push(item)
        }
      })
      wx.setStorage({
        key: 'markers',
        data: marker,
      })
      wx.navigateTo({
        url:
          '/pages/parkingSpaceInquiry/mapInfo/index?id=' +
          markerId +
          '&lat=' +
          lat +
          '&long=' +
          lng +
          '&resourceType=' +
          resourceType +
          '&destinationlat=' +
          data.latitude +
          '&destinationlng=' +
          data.longitude,
      })
    }
  },
  modifyLocation: function (e) {
    console.log('点击地图时触发' + e)
    // 根据标记点的 id 来显示详情
    var _this = this
    var detail = e.detail
    var latitude = detail.latitude
    var longitude = detail.longitude
    var markerIndex = _this.data.markers.findIndex(t => t.id == 0)
    var markersLat = `markers[${markerIndex}].latitude`
    var markersLnt = `markers[${markerIndex}].longitude`
    _this.setData({
      [markersLat]: latitude,
      [markersLnt]: longitude,
      latitude: latitude,
      longitude: longitude,
    }) //修改位置
    _this.setData({
      pslist: [],
      currentPage: 1,
    })
    util.showLoading('正在加载...')
    _this.getInfo(latitude, longitude)
  },
  openMapApp: function (e) {
    var dataset = e.currentTarget.dataset
    var latitude = dataset.lat
    var longitude = dataset.long
    var name = dataset.name
    var address = dataset.address
    util.openMapApp(latitude, longitude, app.globalData.mapScale, name, address)
  },
  goSearchPage: function () {
    let that = this
    var data = that.data
    wx.navigateTo({
      url:
        '/pages/parkingSpaceInquiry/fwSearchPage/index?lat=' +
        app.globalData.latitude +
        '&lng=' +
        app.globalData.longitude +
        '&goback=fw' +
        '&type=' +
        data.filter +
        '&isV=false',
    })
  },
  addListHeight: function (e) {
    let that = this
    var listDivHeight = that.data.listDivHeight
    if (listDivHeight == '501rpx') {
      //固定列表的长度
      that.setData({
        btntext: '收起',
        listDivHeight: '1020rpx',
        isShowPxSelect: false,
      })
    } else {
      that.setData({
        btntext: '点击查找更多',
        listDivHeight: '501rpx',
        isShowPxSelect: false,
      })
    }
  },
  goBack: function (e) {
    wx.switchTab({
      url: '/pages/fw/fw',
      fail(msg) {
        console.log(msg)
      },
    })
  },
  bindReachBottom: function () {
    console.log('上拉加载....')
    util.showLoading('正在加载…')
    var that = this
    var data = that.data
    if (data.showNullMoreTip) {
      util.showToast('没有更多数据了')
    } else {
      that.getMapMakerList(data.latitude, data.longitude)
    }
  },
  setCenter: function (e) {
    let mpCtx = wx.createMapContext('myMap')
    mpCtx.moveToLocation()
  },
  goxq: function (e) {
    var id = e.currentTarget.id
    var type = e.currentTarget.dataset.type
    if (type === 'CHARGE') {
      //充电桩点击可以跳转到详情

      wx.navigateTo({
        url:
          '/pages/parkingSpaceInquiry/info/index?id=' +
          id +
          '&lat=' +
          this.data.latitude +
          '&long=' +
          this.data.longitude +
          '&resourceType=' +
          type,
      })
    }
  },
})
