<!--pages/myCar/myCar.wxml-->

<view class="saveOutView" wx:if="{{carList.length == 0}}">
  <cu-custom bgColor="white-bg" contentTitle="我的车辆" isBack="true"></cu-custom>
  <view class="columnView">
    <text class="nullTip">未绑定车辆</text>
    <form>
      <view class="top">
        <view class="ptitle">请添加车牌号享受智慧停车服务 </view>
        <view class="con-query">
          <view class="plate-input-body">
            <view class="plate-input-content">
              <view class="{{inputOnFocusIndex=='0'?'plate-nums-foc':'plate-nums-first'}}">
                <text catchtap="inputClick" class="plate-num-text" data-id="0">{{inputPlates.index0}}</text>
              </view>
              <view class="{{inputOnFocusIndex=='1'?'plate-nums-foc':'plate-nums-first'}}">
                <text catchtap="inputClick" class="plate-num-text" data-id="1">{{inputPlates.index1}}</text>
              </view>
              <view class="{{inputOnFocusIndex=='2'?'plate-nums-foc':'plate-nums-first'}}">
                <text catchtap="inputClick" class="plate-num-text" data-id="2">{{inputPlates.index2}}</text>
              </view>
              <view class="{{inputOnFocusIndex=='3'?'plate-nums-foc':'plate-nums-first'}}">
                <text catchtap="inputClick" class="plate-num-text" data-id="3">{{inputPlates.index3}}</text>
              </view>
              <view class="{{inputOnFocusIndex=='4'?'plate-nums-foc':'plate-nums-first'}}">
                <text catchtap="inputClick" class="plate-num-text" data-id="4">{{inputPlates.index4}}</text>
              </view>
              <view class="{{inputOnFocusIndex=='5'?'plate-nums-foc':'plate-nums-first'}}">
                <text catchtap="inputClick" class="plate-num-text" data-id="5">{{inputPlates.index5}}</text>
              </view>
              <view class="{{inputOnFocusIndex=='6'?'plate-nums-foc':'plate-nums-first'}}">
                <text catchtap="inputClick" class="plate-num-text" data-id="6">{{inputPlates.index6}}</text>
              </view>
              <view class="{{inputOnFocusIndex=='7'?'plate-nums-foc':'plate-nums-first'}}">
                <text wx:if="{{!showNewPower}}" catchtap="inputClick" class="plate-num-text" data-id="7"
                  >{{inputPlates.index7}}</text
                >
                <text
                  wx:if="{{showNewPower}}"
                  catchtap="inputClick"
                  class="plate-num-text"
                  style="color: #c0c0c0"
                  data-id="7"
                  >新</text
                >
              </view>
            </view>
          </view>
          <view class="box columnView">
            <view class="content rowView">
              <view class="navigator-text"> 车牌颜色 </view>
              <picker
                bindchange="bindPickerChange_colour"
                value="{{index_colour}}"
                range="{{vehicleColour}}"
                range-key="title"
              >
                <view class="picker"> {{vehicleColour[index_colour]['title']}} </view>
              </picker>
              <view class="navigator-arrow"></view>
            </view>
            <!-- <view class="content rowView">
              <view class="navigator-text">
                车辆类型
              </view>
              <picker bindchange="bindPickerChange_type" value="{{index_colour}}" range="{{vehicleType}}" range-key="title">
                <view class="picker">
                  {{vehicleType[index_type]['title']}}
                </view>
              </picker>
              <view class="navigator-arrow"></view>
            </view> 12.5去掉车辆类型。-->
            <view class="content">
              <view class="navigator-text">
                <view class="rowView">
                  <view style="flex: 1">设为默认车辆 </view>
                  <switch checked="{{defaultVehicle}}" bindchange="switchChange" color="#41E0AC" />
                </view>
              </view>
            </view>
          </view>
          <button class="convention_button" bindtap="addCar">添加车辆</button>
        </view>
      </view>
    </form>
    <view class="keyboard" wx:if="{{isKeyboard}}">
      <view class="kb_top">
        <text class="kb_top_text" data-index="1" catchtap="tapSpecBtna"> 完成 </text>
      </view>
      <view style="width: 100%; text-align: center" wx:if="{{isNumberKB}}">
        <view style="width: 99%; display: flex; text-align: center; margin: 0 auto">
          <view
            catchtap="tapKeyboard"
            class="td td_nor"
            data-index="{{idx}}"
            data-val="{{itemName}}"
            hoverClass="board_bg"
            hoverStartTime="0"
            hoverStayTime="80"
            wx:if="{{idx<=9}}"
            wx:for="{{keyboard1}}"
            wx:for-index="idx"
            wx:for-item="itemName"
            wx:key="itemName"
          >
            {{itemName}}
          </view>
        </view>
        <view style="display: flex; text-align: center; width: 90%; margin: 0 auto">
          <view
            catchtap="tapKeyboard"
            class="td td_nor"
            data-index="{{idx}}"
            data-val="{{itemName}}"
            hoverClass="board_bg"
            hoverStartTime="0"
            hoverStayTime="80"
            wx:if="{{idx<=18&&idx>9}}"
            wx:for="{{keyboard1}}"
            wx:for-index="idx"
            wx:for-item="itemName"
            wx:key="itemName"
          >
            {{itemName}}
          </view>
        </view>
        <view style="display: flex; text-align: center; width: 70%; margin: 0 auto">
          <view
            catchtap="tapKeyboard"
            class="td td_nor"
            data-index="{{idx}}"
            data-val="{{itemName}}"
            hoverClass="board_bg"
            hoverStartTime="0"
            hoverStayTime="80"
            wx:if="{{idx<=25&&idx>18}}"
            wx:for="{{keyboard1}}"
            wx:for-index="idx"
            wx:for-item="itemName"
            wx:key="itemName"
          >
            {{itemName}}
          </view>
        </view>
        <view style="display: flex; width: 50%; margin: 0 auto; text-align: center">
          <view
            catchtap="tapKeyboard"
            class="td td_nor"
            data-index="{{idx}}"
            data-val="{{itemName}}"
            hoverClass="board_bg"
            hoverStartTime="0"
            hoverStayTime="80"
            wx:if="{{idx>25}}"
            wx:for="{{keyboard1}}"
            wx:for-index="idx"
            wx:for-item="itemName"
            wx:key="itemName"
          >
            {{itemName}}
          </view>
        </view>
        <view
          bindtap="tapSpecBtn"
          class="del-first"
          data-index="0"
          hoverClass="del-hover"
          hoverStartTime="0"
          hoverStayTime="80"
        >
          <text>删除</text>
        </view>
      </view>
      <view style="width: 100%; text-align: center" wx:if="{{!isNumberKB}}">
        <view style="width: 99%; display: flex; text-align: center; margin: 0 auto">
          <view
            class="td td_num board_bg"
            wx:if="{{!tapNum&&idx<=9}}"
            wx:for="{{keyboardNumber}}"
            wx:for-index="idx"
            wx:for-item="itemName"
            wx:key="itemName"
          >
            {{itemName}}
          </view>
        </view>
        <view style="width: 99%; display: flex; text-align: center; margin: 0 auto">
          <view
            catchtap="tapKeyboard"
            class="td td_num"
            data-index="{{idx}}"
            data-val="{{itemName}}"
            hoverClass="board_bg"
            hoverStartTime="0"
            hoverStayTime="80"
            wx:if="{{tapNum&&idx<=9}}"
            wx:for="{{keyboardNumber}}"
            wx:for-index="idx"
            wx:for-item="itemName"
            wx:key="itemName"
          >
            {{itemName}}
          </view>
        </view>
        <view style="width: 99%; display: flex; text-align: center; margin: 0 auto">
          <view
            catchtap="tapKeyboard"
            class="td td_num"
            data-index="{{idx}}"
            data-val="{{itemName}}"
            hoverClass="board_bg"
            hoverStartTime="0"
            hoverStayTime="80"
            wx:if="{{idx>9&&idx<=17}}"
            wx:for="{{keyboardNumber}}"
            wx:for-index="idx"
            wx:for-item="itemName"
            wx:key="itemName"
          >
            {{itemName}}
          </view>
          <view
            catchtap="tapKeyboard"
            class="td td_num"
            data-index="{{idx}}"
            data-val="{{itemName}}"
            hoverClass="board_bg"
            hoverStartTime="0"
            hoverStayTime="80"
            wx:if="{{tapNum&&18<=idx&&idx<=19}}"
            wx:for="{{keyboardNumber}}"
            wx:for-index="idx"
            wx:for-item="itemName"
            wx:key="itemName"
          >
            {{itemName}}
          </view>
          <view
            class="td td_num board_bg"
            wx:if="{{!tapNum&&18<=idx&&idx<=19}}"
            wx:for="{{keyboardNumber}}"
            wx:for-index="idx"
            wx:for-item="itemName"
            wx:key="itemName"
          >
            {{itemName}}
          </view>
        </view>
        <view style="width: 99%; display: flex; text-align: center; margin: 0 auto">
          <view
            catchtap="tapKeyboard"
            class="td td_num"
            data-index="{{idx}}"
            data-val="{{itemName}}"
            hoverClass="board_bg"
            hoverStartTime="0"
            hoverStayTime="80"
            wx:if="{{idx>19&&idx<=28}}"
            wx:for="{{keyboardNumber}}"
            wx:for-index="idx"
            wx:for-item="itemName"
            wx:key="itemName"
          >
            {{itemName}}
          </view>
          <view
            catchtap="tapKeyboard"
            class="td td_num"
            data-index="{{idx}}"
            data-val="{{itemName}}"
            hoverClass="board_bg"
            hoverStartTime="0"
            hoverStayTime="80"
            wx:if="{{tapNum&&29==idx}}"
            wx:for="{{keyboardNumber}}"
            wx:for-index="idx"
            wx:for-item="itemName"
            wx:key="itemName"
          >
            {{itemName}}
          </view>
          <view
            class="td td_num board_bg"
            wx:if="{{!tapNum&&29==idx}}"
            wx:for="{{keyboardNumber}}"
            wx:for-index="idx"
            wx:for-item="itemName"
            wx:key="itemName"
          >
            {{itemName}}
          </view>
        </view>
        <view style="width: 69%; display: flex; text-align: left; margin-left: 5rpx">
          <view
            catchtap="tapKeyboard"
            class="td td_num"
            data-index="{{idx}}"
            data-val="{{itemName}}"
            hoverClass="board_bg"
            hoverStartTime="0"
            hoverStayTime="80"
            wx:if="{{idx>29}}"
            wx:for="{{keyboardNumber}}"
            wx:for-index="idx"
            wx:for-item="itemName"
            wx:key="itemName"
          >
            {{itemName}}
          </view>
        </view>
        <view
          bindtap="tapSpecBtn"
          class="del-first"
          data-index="0"
          hoverClass="del-hover"
          hoverStartTime="0"
          hoverStayTime="80"
        >
          <text>删除</text>
        </view>
      </view>
    </view>
  </view>
</view>
<view class="saveOutView" wx:else>
  <cu-custom bgColor="white-bg" contentTitle="{{contentTitle}}" isBack="true"></cu-custom>
  <view class="title rowView">
    <image src="../../../image/bt-jt.png" style="width: 9rpx; height: 14rpx; margin: auto 10rpx"></image>
    <text>我的车辆</text>
  </view>
  <scroll-view scroll-y="true" style="height: 1rpx; flex: 1">
    <block wx:for="{{carList}}" wx:key="index" scroll-y="true">
      <view class="carListInfo-d columnView">
        <view class="rowView">
          <image src="../../../image/car.png" style="width: 64rpx; height: 64rpx; margin: auto 10rpx"></image>
          <text
            class="carListInfo-t"
            style="{{((item.defaultPlateNo==1&&item.authenticationStatus!=1)||(item.defaultPlateNo!=1&&item.authenticationStatus==1)||(item.defaultPlateNo==1&&item.authenticationStatus==1))? '':'flex: 1;'}}"
            >{{item.plateNo}}</text
          >
          <text
            class="carListInfo-d-mrcl"
            wx:if="{{item.defaultPlateNo==1}}"
            style="{{(item.defaultPlateNo==1&&item.plateStatus==2)? 'margin-right: 10rpx;':''}}"
            >默认车辆</text
          >
          <text class="carListInfo-d-yrz" wx:if="{{item.authenticationStatus==1}}">已认证</text>
          <text
            class="carListInfo-d-gd"
            data-id="{{item.vehicleId}}"
            data-name="{{item.plateNo}}"
            bindtap="changeRange2"
            >…</text
          >
        </view>
        <view class="rowView">
          <view style="padding: 0 15rpx 0 84rpx">{{item.plateColorText}}</view>
          <!-- <view>{{item.vehicleTypeText}}</view> 12.5去掉车辆类型-->
          <view
            wx:if="{{item.authenticationStatus==0&&isAuth}}"
            class="carListInfo-d-btn blue"
            data-id="{{item.vehicleId}}"
            data-carNum="{{item.plateNo}}"
            data-type="0"
            bindtap="vehicleAuthentication"
            data-index="{{index}}"
            hidden="{{item.hide}}"
          >
            去认证
          </view>
          <view
            class="carListInfo-d-btn blue2"
            data-id="{{item.vehicleId}}"
            data-carNum="{{item.plateNo}}"
            data-type="3"
            bindtap="vehicleAuthentication"
            wx:if="{{item.authenticationStatus==3}}"
            >认证审核中</view
          >
          <view
            class="carListInfo-d-btn red"
            data-id="{{item.vehicleId}}"
            data-carNum="{{item.plateNo}}"
            data-type="2"
            bindtap="vehicleAuthentication"
            wx:if="{{item.authenticationStatus==2}}"
            >认证失败</view
          >
        </view>
      </view>
    </block>
  </scroll-view>
  <view class="box_convention_button">
    <button class="convention_button" formType="submit" bindtap="goAddCarPage">添加车辆</button>
  </view>
</view>

<bottomDialog id="dialog" catchtouchmove="preventTouchMove" bind:selectItem="selectItem"></bottomDialog>
