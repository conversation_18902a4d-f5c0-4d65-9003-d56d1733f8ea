<view class="half-screen" catchtouchmove="preventTouchMove">
  <!--屏幕背景变暗的背景  -->
  <view class="background_screen" catchtap="hideModal" wx:if="{{isShow}}"></view>
  <!--弹出框  -->
  <view animation="{{animationData}}" class="attr_box" wx:if="{{isShow}}">
    <view class="dialog-box">
      <view class="dialog-head">
        <view class="dialog-title">请输入车牌</view>
        <view class="close2ImgBox">
          <image src="../../../image/close.png" class="close2Img" catchtap="hideModal"></image>
        </view>
      </view>
      <view class='dialog-content'>
        <view style="overflow: hidden;clear: both;height: 100%;">
          <view class="top">
            <view class="con-query">
              <view class="plate-input-body">
                <view class="plate-input-content">
                  <view class="{{inputOnFocusIndex=='0'?'plate-nums-foc':'plate-nums-first'}}">
                    <text catchtap="inputClick" class="plate-num-text" data-id="0">{{inputPlates.index0}}</text>
                  </view>
                  <view class="{{inputOnFocusIndex=='1'?'plate-nums-foc':'plate-nums-first'}}">
                    <text catchtap="inputClick" class="plate-num-text" data-id="1">{{inputPlates.index1}}</text>
                  </view>
                  <view class="{{inputOnFocusIndex=='2'?'plate-nums-foc':'plate-nums-first'}}">
                    <text catchtap="inputClick" class="plate-num-text" data-id="2">{{inputPlates.index2}}</text>
                  </view>
                  <view class="{{inputOnFocusIndex=='3'?'plate-nums-foc':'plate-nums-first'}}">
                    <text catchtap="inputClick" class="plate-num-text" data-id="3">{{inputPlates.index3}}</text>
                  </view>
                  <view class="{{inputOnFocusIndex=='4'?'plate-nums-foc':'plate-nums-first'}}">
                    <text catchtap="inputClick" class="plate-num-text" data-id="4">{{inputPlates.index4}}</text>
                  </view>
                  <view class="{{inputOnFocusIndex=='5'?'plate-nums-foc':'plate-nums-first'}}">
                    <text catchtap="inputClick" class="plate-num-text" data-id="5">{{inputPlates.index5}}</text>
                  </view>
                  <view class="{{inputOnFocusIndex=='6'?'plate-nums-foc':'plate-nums-first'}}">
                    <text catchtap="inputClick" class="plate-num-text" data-id="6">{{inputPlates.index6}}</text>
                  </view>
                  <view class="{{inputOnFocusIndex=='7'?'plate-nums-foc':'plate-nums-first'}}">
                    <text wx:if="{{!showNewPower}}" catchtap="inputClick" class="plate-num-text" data-id="7">{{inputPlates.index7}}</text>
                    <text wx:if="{{showNewPower}}" catchtap="inputClick" class="plate-num-text" style="color: #C0C0C0;" data-id="7">新</text>
                  </view>
                </view>
              </view>
              <view class="dx">
                <image class='yes_icon' wx:if="{{!allowXY}}" src="../../../image/yes-icon.png" bindtap='bindChange'></image>
                <image class='yes_icon' wx:if="{{allowXY}}" src="../../../image/yes-icon-select.png" bindtap='bindChange'></image>
                <view class="text">中大型车辆(黄牌车) </view>
              </view>
              <button class="convention_button {{isClick?'':'noAllowClick'}}" bindtap="addCar">输入车牌缴费</button>
            </view>
            <view class="keyboard">
              <view class="kb_top">
                <text catchtap="tapSpecBtna" data-index="1" style="position:absolute;right:0;display:block;height:74rpx;padding:0 34rpx; color:#6883F5;line-height:74rpx; font-size: 30rpx;">完成</text>
              </view>
              <view style="width:100%; text-align:center;" wx:if="{{isNumberKB}}">
                <view style="width:99%;display:flex;text-align:center;margin:0 auto">
                  <view catchtap="tapKeyboard" class="td td_nor" data-index="{{idx}}" data-val="{{itemName}}" hoverClass="board_bg" hoverStartTime="0" hoverStayTime="80" wx:if="{{idx<=9}}" wx:for="{{keyboard1}}" wx:for-index="idx" wx:for-item="itemName" wx:key="itemName">
                    {{itemName}}
                  </view>
                </view>
                <view style="display:flex;text-align:center; width:90%;margin:0 auto">
                  <view catchtap="tapKeyboard" class="td td_nor" data-index="{{idx}}" data-val="{{itemName}}" hoverClass="board_bg" hoverStartTime="0" hoverStayTime="80" wx:if="{{idx<=18&&idx>9}}" wx:for="{{keyboard1}}" wx:for-index="idx" wx:for-item="itemName" wx:key="itemName">
                    {{itemName}}
                  </view>
                </view>
                <view style="display:flex;text-align:center; width:70%;margin:0 auto">
                  <view catchtap="tapKeyboard" class="td td_nor" data-index="{{idx}}" data-val="{{itemName}}" hoverClass="board_bg" hoverStartTime="0" hoverStayTime="80" wx:if="{{idx<=25&&idx>18}}" wx:for="{{keyboard1}}" wx:for-index="idx" wx:for-item="itemName" wx:key="itemName">
                    {{itemName}}
                  </view>
                </view>
                <view style="display:flex; width:50%;margin:0 auto;text-align:center;">
                  <view catchtap="tapKeyboard" class="td td_nor" data-index="{{idx}}" data-val="{{itemName}}" hoverClass="board_bg" hoverStartTime="0" hoverStayTime="80" wx:if="{{idx>25}}" wx:for="{{keyboard1}}" wx:for-index="idx" wx:for-item="itemName" wx:key="itemName">
                    {{itemName}}
                  </view>
                </view>
                <view bindtap="tapSpecBtn" class="del-first" data-index="0" hoverClass="del-hover" hoverStartTime="0" hoverStayTime="80">
                  <text>删除</text>
                </view>
              </view>
              <view style="width:100%; text-align:center;" wx:if="{{!isNumberKB}}">
                <view style="width:99%;display:flex;text-align:center;margin:0 auto">
                  <view class="td td_num board_bg" wx:if="{{!tapNum&&idx<=9}}" wx:for="{{keyboardNumber}}" wx:for-index="idx" wx:for-item="itemName" wx:key="itemName">
                    {{itemName}}
                  </view>
                </view>
                <view style="width:99%;display:flex;text-align:center;margin:0 auto">
                  <view catchtap="tapKeyboard" class="td td_num" data-index="{{idx}}" data-val="{{itemName}}" hoverClass="board_bg" hoverStartTime="0" hoverStayTime="80" wx:if="{{tapNum&&idx<=9}}" wx:for="{{keyboardNumber}}" wx:for-index="idx" wx:for-item="itemName" wx:key="itemName">
                    {{itemName}}
                  </view>
                </view>
                <view style="width:99%;display:flex;text-align:center;margin:0 auto">
                  <view catchtap="tapKeyboard" class="td td_num" data-index="{{idx}}" data-val="{{itemName}}" hoverClass="board_bg" hoverStartTime="0" hoverStayTime="80" wx:if="{{idx>9&&idx<=17}}" wx:for="{{keyboardNumber}}" wx:for-index="idx" wx:for-item="itemName" wx:key="itemName">
                    {{itemName}}
                  </view>
                  <view catchtap="tapKeyboard" class="td td_num" data-index="{{idx}}" data-val="{{itemName}}" hoverClass="board_bg" hoverStartTime="0" hoverStayTime="80" wx:if="{{tapNum&&18<=idx&&idx<=19}}" wx:for="{{keyboardNumber}}" wx:for-index="idx" wx:for-item="itemName" wx:key="itemName">
                    {{itemName}}
                  </view>
                  <view class="td td_num board_bg" wx:if="{{!tapNum&&18<=idx&&idx<=19}}" wx:for="{{keyboardNumber}}" wx:for-index="idx" wx:for-item="itemName" wx:key="itemName">
                    {{itemName}}
                  </view>
                </view>
                <view style="width:99%;display:flex;text-align:center;margin:0 auto">
                  <view catchtap="tapKeyboard" class="td td_num" data-index="{{idx}}" data-val="{{itemName}}" hoverClass="board_bg" hoverStartTime="0" hoverStayTime="80" wx:if="{{idx>19&&idx<=28}}" wx:for="{{keyboardNumber}}" wx:for-index="idx" wx:for-item="itemName" wx:key="itemName">
                    {{itemName}}
                  </view>
                  <view catchtap="tapKeyboard" class="td td_num" data-index="{{idx}}" data-val="{{itemName}}" hoverClass="board_bg" hoverStartTime="0" hoverStayTime="80" wx:if="{{tapNum&&29==idx}}" wx:for="{{keyboardNumber}}" wx:for-index="idx" wx:for-item="itemName" wx:key="itemName">
                    {{itemName}}
                  </view>
                  <view class="td td_num board_bg" wx:if="{{!tapNum&&29==idx}}" wx:for="{{keyboardNumber}}" wx:for-index="idx" wx:for-item="itemName" wx:key="itemName">
                    {{itemName}}
                  </view>
                </view>
                <view style="width:69%;display:flex;text-align:left; margin-left:5rpx;">
                  <view catchtap="tapKeyboard" class="td td_num" data-index="{{idx}}" data-val="{{itemName}}" hoverClass="board_bg" hoverStartTime="0" hoverStayTime="80" wx:if="{{idx>29}}" wx:for="{{keyboardNumber}}" wx:for-index="idx" wx:for-item="itemName" wx:key="itemName">
                    {{itemName}}
                  </view>
                </view>
                <view bindtap="tapSpecBtn" class="del-first" data-index="0" hoverClass="del-hover" hoverStartTime="0" hoverStayTime="80">
                  <text>删除</text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</view>