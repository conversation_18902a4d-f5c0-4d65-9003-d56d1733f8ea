
page {
  background-color: #F6F7FB;
}
.container{
  position: relative;
  width: 100%;
}
.info-box{
  padding: 60rpx 60rpx 50rpx 60rpx;
  margin: 20rpx;
  width: calc(100% - 20rpx * 2);
  height: 560rpx;
  background: #FFFFFF;
  box-shadow: 0rpx 10rpx 0rpx 1rpx rgba(228,234,248,0.16);
  border-radius: 20rpx 20rpx 20rpx 20rpx;
  box-sizing: border-box;
}
.info-box .header{
  width: 100%;
}
.info-box .header .money{
  width: 100%;
  display: flex;
  justify-content: center;
}
.info-box .header .money .symbol{
  font-size: 24rpx;
  color: #353535;
  line-height: 78rpx;
}
.info-box .header .money .num{
height: 78rpx;
font-size: 56rpx;
font-weight: bold;
color: #E94F4F;
line-height: 56rpx;
}
.info-box .header .tip{
  margin-bottom: 70rpx;
  width: 100%;
  font-size: 26rpx;
  font-weight: bold;
  color: #353535;
  line-height: 54rpx;
  text-align: center;
}
.info-box .row,
.state-box .row{
  display: flex;
  margin-bottom: 16rpx;
  width: 100%;
  height: 37rpx;
}

.info-box .row .label,
.info-box .row .value,
.state-box .row .label,
.state-box .row .value{
  height: 100%;
  font-size: 26rpx;
  font-weight: 400;
  line-height: 37rpx;
}

.info-box .row .label,
.state-box .row .label{
  width: 210rpx;
  color: #353535;
  text-align: left;
}
.info-box .row .value,
.state-box .row .value{
  flex: 1;
  color: #7E7E7E;
  text-align: right;
}

.info-box .footer{
  margin-top: 24rpx;
  font-size: 26rpx;
  color: #256BF5;
  line-height: 54rpx;
}
.bottomBtn{
  position: absolute;
    width: 100%;
    height: 171rpx;
    bottom: 0;
}
.bottomBtn .btn{
  margin: 0 20rpx;
  width: calc(100% - 40rpx);
  height: 80rpx;
  background: linear-gradient(90deg, #458BFF 0%, #08D7AE 100%);
  box-shadow: 0rpx 3rpx 10rpx 1rpx rgba(37,107,245,0.2);
  border-radius: 40rpx;
  text-align: center;
  font-size: 36rpx;
  color: #FFFFFF;
  line-height: 80rpx;
}

.noClick{
  background: #DFDFDF;
  box-shadow: 0rpx 3rpx 10rpx 1rpx rgba(37,107,245,0.2);
}

.state-box{
  padding: 60rpx 60rpx 50rpx 60rpx;
  margin: 20rpx;
  width: calc(100% - 20rpx * 2);
  height: 560rpx;
  background: #FFFFFF;
  box-shadow: 0rpx 10rpx 0rpx 1rpx rgba(228,234,248,0.16);
  border-radius: 20rpx 20rpx 20rpx 20rpx;
  box-sizing: border-box;
}
.state-box .header{
  margin-bottom: 89rpx;
  width: 100%;
  display: flex;
  justify-content: center;
}
.state-box .header .left{
  margin-right: 20rpx;
  width: 72rpx;
  height: 72rpx;
}

.state-box .header .right .state,
.state-box .header .right .money{
  font-size: 26rpx;
  font-weight: bold;
  color: #353535;
  line-height: 37rpx;
}