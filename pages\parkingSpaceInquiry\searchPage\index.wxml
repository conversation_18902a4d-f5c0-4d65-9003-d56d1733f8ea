<!--pages/khzl/searchkhzl/searchkhzl.wxml-->
<cu-custom bgColor="white-bg" contentTitle="畅行桂林" isBack="true"></cu-custom>
<view class="searchBar">
  <image class="Icon-search" src="../../../image/merry-search.png"></image>
  <input type="text" placeholder="{{placeholderText[type]}}" confirm-type="search" value="{{keyword}}" bindfocus="keywordfocus" bindinput="getKeyword" bindconfirm='bindconfirm'></input>
  <image class="cuIcon-delete" wx:if="{{onSearch}}" bindtap="offSearch" src="../../../image/input-close.png"></image>
</view>
<view class="oldT rowView" wx:if="{{showH}}">
  <view style="flex: 1;">历史记录</view>
  <view class="clear" catchtap="clearHistoryList">清空</view>
</view>
<image src="../../../image/qfjf-null.png" class="nullImg" wx:if="{{historyList.length==0&&showH}}"></image>
<view class="nullTip" wx:if="{{historyList.length==0&&showH}}"> 暂无历史记录</view>
<block wx:for="{{historyList}}" wx:key="index" wx:if="{{showH}}">
  <view class="listDiv-d" bindtap="backPage" id="{{item.id}}" data-latitude="{{item.location.lat}}" data-longitude="{{item.location.lng}}" data-title="{{item.title}}" data-info="{{item}}" data-save="true">
    <view class="listDiv-d-left">
      <view class="listDiv-search-name ">{{item.title}}</view>
      <view class="listDiv-search-name-xq">{{item.address}}</view>
    </view>
    <view class="listDiv-d-right">
      <view class="listDiv-d-dhBtn" catchtap="openMapApp" data-lat="{{item.location.lat}}" data-long="{{item.location.lng}}" data-name="{{item.title}}" data-address="{{item.address}}">
        <image src="../../../image/dh.png"></image>
        <text>导航</text>
      </view>
      <view class="listDiv-search-dw">{{item.distance}}km</view>
    </view>
  </view>
</block>
<block wx:for="{{list}}" wx:key="index">
  <view class="listDiv-d" bindtap="backPage" id="{{item.id}}" data-latitude="{{item.location.lat}}" data-longitude="{{item.location.lng}}" data-title="{{item.title}}" data-info="{{item}}" data-save="true">
    <view class="listDiv-d-left">
      <view class="listDiv-search-name ">{{item.title}}</view>
      <view class="listDiv-search-name-xq">{{item.address}}</view>
      <!-- <view class="listDiv-search-dc">
        <view class="listDiv-search-dc-num flex-wrp">
          <view class="istDiv-search-yw flex-wrp">余位 {{item.surplus}}/</view>
          <view class="listDiv-search-dc-zw flex-wrp">{{item.total}}</view>
          <view class="listDiv-search-dc-yhj" catchtap="goxq">优惠停车</view>
        </view>
        <view class="listDiv-search-dc-tip">{{item.description}}</view>
      </view> -->
    </view>
    <view class="listDiv-d-right">
      <view class="listDiv-d-dhBtn" catchtap="openMapApp" data-lat="{{item.location.lat}}" data-long="{{item.location.lng}}" data-name="{{item.title}}" data-address="{{item.address}}">
        <image src="../../../image/dh.png"></image>
        <text>导航</text>
      </view>
      <view class="listDiv-search-dw">{{item.distance}}km</view>
    </view>
  </view>
</block>
<view wx:if="{{showNullTip}}" class="njgtip">无符合查询条件数据</view>
<view wx:if="{{showNullMoreTip}}" class="njgtip">没有更多信息了</view>