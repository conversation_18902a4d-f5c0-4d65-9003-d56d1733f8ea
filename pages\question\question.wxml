<view class="page">
  <cu-custom bgColor="white-bg" contentTitle="畅行桂林" isBack isCustomBack bind:back="onBack"></cu-custom>
  <image src="../../image/question/zhufang.png" mode="widthFix" class="header-image" />
  <view class="list">
    <!-- <view class="title">{{title}}</view> -->
    <view class="desc">{{desc}}</view>
    <view class="desc" style="margin-bottom: 60rpx">{{desc2}}</view>
    <view wx:for="{{list}}" wx:key="index" class="list-item">
      <view class="list-header">
        <view class="list-title {{item.required?'required':''}}">{{index+1}}.{{item.title}}</view>
        <view wx:if="{{item.error}}" class="list-required-tip">{{item.errorTip||'这道题未回答'}}</view>
      </view>

      <input
        wx:if="{{item.type==='input'}}"
        class="input"
        value="{{item.value}}"
        disabled="{{item.disabled}}"
        data-index="{{index}}"
        bind:input="bindKeyInput"
      />

      <textarea
        wx:if="{{item.type==='textarea'}}"
        class="textarea"
        maxlength="{{-1}}"
        disabled="{{item.disabled}}"
        data-index="{{index}}"
        bind:input="bindKeyInput"
      />

      <radio-group
        wx:if="{{item.type==='radio'}}"
        data-index="{{index}}"
        disabled="{{item.disabled}}"
        bind:change="radioChange"
      >
        <label class="list-flex" wx:for="{{item.options}}" wx:for-index="index2" wx:for-item="item2" wx:key="index2">
          <view class="list-radio">
            <radio value="{{item2.value}}" />
          </view>
          <view class="list-text">{{item2.label}}</view>
        </label>
      </radio-group>

      <checkbox-group
        wx:if="{{item.type==='checkbox'}}"
        data-index="{{index}}"
        disabled="{{item.disabled}}"
        bind:change="checkboxChange"
      >
        <label class="list-flex" wx:for="{{item.options}}" wx:for-index="index2" wx:for-item="item2" wx:key="index2">
          <view class="list-checkbox">
            <checkbox value="{{item2.value}}" disabled="{{item2.disabled}}" />
          </view>
          <view class="list-text">{{item2.label}}</view>
        </label>
      </checkbox-group>
    </view>

    <view class="btn-container">
      <button class="convention_button.next" bind:tap="onSave">提交</button>
    </view>
  </view>
</view>
