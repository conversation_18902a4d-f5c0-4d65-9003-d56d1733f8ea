
page {
  background-color: #F6F7FB;
}

.container{
  width: 100%;
}
.container .header{
  width: 100%;
  height: 100rpx;
  background: linear-gradient(218deg, #5A7EFF 0%, #478BFF 100%);
  box-shadow: inset 0rpx 3rpx 15rpx 1rpx #98ACFF;

  font-size: 36rpx;
  color: #FFFFFF;
  line-height: 100rpx;
  text-align: center;
}
.container .content{
  padding: 0 50rpx;
  width: 100%;
  box-sizing: border-box;
}
.container .content .begin{
  margin-top: 30rpx;
  margin-bottom: 20rpx;
  width: 100%;
  font-size: 30rpx;
  color: #000000;
  line-height: 48rpx;
}

.container .content .item{
  margin-top: 30rpx;
  margin-bottom: 6rpx;
  width: 100%;
  display: flex;
  flex-direction: row;
  display: flex;
  align-items: center;
}
.container .content .item .desc{
  flex: 1;
font-size: 26rpx;
color: #606266;
line-height: 36rpx;
}
.container .content .item .status{
  margin-left: 170rpx;
  width: 137rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
}
.bottom-control{
  height: 258rpx;
}
