var util = require('../../../utils/util')
var api = require('../../../config/api.js')
Page({
  data: {
    imgUrl: api.imgUrl,
    dictionary: {
      1: '减免券',
      2: '折扣券',
      3: '全免券',
      4: '减时券',
    },
    couponCode: '',
    uniqueId: '',
    parkCode: '',
    moneyText: '无优惠',
    list: [],
    isVisitor: false, //是否是游客
  },
  onLoad(options) {
    this.setData({
      isVisitor: options.isVisitor,
      couponCode: options.couponCode,
      uniqueId: options.uniqueId,
      parkCode: options.parkCode,
    })
  },
  select(e) {
    var id = e.currentTarget.id
    this.setData({
      couponCode: id,
    })
  },
  sure() {
    var pages = getCurrentPages() // 获取页面栈
    var prevPage = pages[pages.length - 2] // 上一个页面

    prevPage.setData({
      discountCoupon: this.data.list.length == 0 ? '无可用优惠券' : '去选择',
      couponCode: this.data.couponCode,
      uniqueId: this.data.uniqueId,
    })
    wx.navigateBack({
      delta: 1,
    })
  },
  onShow() {
    this.getList()
  },
  getList: function () {
    var that = this
    var url = api.getCouponsList
    var data = that.data
    util.showLoading('正在加载...')
    // if(data.isVisitor){
    //   url = api.getCouponsListByQrcode;
    // }
    util
      .request(
        url,
        {
          uniqueId: data.uniqueId,
          parkCode: data.parkCode,
        },
        'GET'
      )
      .then(function (res) {
        var list = []
        wx.hideLoading()
        if (res.code == '0') {
          list = res.result
        } else {
          util.showToast(res.message)
        }
        that.setData({
          list: list,
        })
      })
      .catch(err => {
        wx.hideLoading()
        util.showToast('出错了' + JSON.stringify(err))
      })
  },
})
