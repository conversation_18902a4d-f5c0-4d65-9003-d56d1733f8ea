<view class="half-screen" catchtouchmove="preventTouchMove">
  <!--屏幕背景变暗的背景  -->
  <view class="background_screen" catchtap="hideModal" wx:if="{{isShow}}"></view>
  <!--弹出框  -->
  <view animation="{{animationData}}" class="attr_box" wx:if="{{isShow}}">
    <view class="dialog-box">
      <view class="dialog-head">
        <view class="dialog-title">登录</view>
        <view class="close2ImgBox">
          <image src="../../../image/close.png" class="close2Img" catchtap="hideModal"></image>
        </view>
      </view>
      <view class="dialog-content" wx:if="{{!showVerification}}">
        <input
          class="inputa"
          auto-focus
          placeholder="请输入手机号码"
          type="number"
          bindinput="bindKeyInput"
          maxlength="11"
        />
        <button class="convention_button {{allowClicking?'':'noClike'}}" bindtap="nextStep">获取验证码</button>
        <view class="modalDlg-xyView">
          <image class="yes_icon" wx:if="{{!allowXY}}" src="../../../image/yes-icon.png" bindtap="allow"></image>
          <image class="yes_icon" wx:if="{{allowXY}}" src="../../../image/yes-icon-select.png" bindtap="allow"></image>
          <text class="modalDlg-xyView-text">阅读并同意</text>
          <text class="modalDlg-xyView-text xy" id="serviceUrl" bindtap="viewAgreement">《服务协议》、</text>
          <text class="modalDlg-xyView-text xy" id="privacyUrl" bindtap="viewAgreement"
            >《软件注册及隐私政策协议》、</text
          >
          <text class="modalDlg-xyView-text xy" id="logonUrl" bindtap="viewAgreement">《登录政策》</text>
        </view>
      </view>
      <view class="dialog-content vBox" wx:else>
        <view class="tipV">请输入短信验证码</view>
        <view class="tipVT">已发送验证码至{{phoneNumber}}</view>
        <input
          class="inputV"
          auto-focus
          placeholder="请输入验证码"
          type="number"
          bindinput="bindKeyCodeInput"
          maxlength="6"
        />
        <view class="tipVTT" wx:if="{{showSendCodeBtn}}">{{currentTime}}s重新发送</view>
        <view class="tipVTT" wx:else
          ><text wx:if="{{phoneIsTrue}}">5分钟内有效</text
          ><text bindtap="getVerificationCode" style="color: #256bf5; padding: 18rpx 20rpx">重新发送</text></view
        >
        <view class="loseTip" wx:if="{{showLoseTip}}">验证码校验失败，请重新输入</view>
        <button class="convention_button {{isClick?'':'noClike'}} a" bindtap="sure">确定</button>
      </view>
    </view>
  </view>
</view>
