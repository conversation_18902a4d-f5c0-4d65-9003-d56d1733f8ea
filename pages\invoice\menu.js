const app = getApp()
const api = require("../../config/api");
var util = require('../../utils/util');
Page({
  data: {
    pageHeight: 0,
    imgUrl: api.imgUrl,
    menu_1:[{
      name: '停车订单',
      pageUrl: '/pages/invoice/orderList/orderList',
      pageType: 'ARREARS',
      iconUrl: '/sy/button_mycar.png',
    }
    // ,{
    //   name: '预约订单',
    //   pageUrl: '/pages/invoice/orderList/orderList',
    //   pageType: 'RESERVATION_SPACE',
    //   iconUrl: '/sy/button_yuyuedingdan.png',
    // }
    ,{
      name: '包月订单',
      pageUrl: '/pages/invoice/orderList/orderList',
      pageType: 'MONTHLY_PACKAGE',
      iconUrl: '/sy/icon_yuekadingdan.png',
    }],
    menu_2: [{
      name: '开票历史',
      pageUrl: '/pages/invoice/invoiceHistory/invoiceHistory',
      pageType: '',
      iconUrl: '/sy/icon_kaipiaolishi.png',
    },
    {
      name: '开票须知',
      pageUrl: '/pages/common/viewAgreement/index?id=invoiceUrl',
      pageType: '',
      iconUrl: '/sy/invoicingInstructions.png',
    }]
  },
  onLoad(options) {
    let height = app.globalData.windowHeight - app.globalData.CustomBar + 48; // 48是tabbar的高度
    this.setData({
      pageHeight: height
    })
  },
  goToPage(e){
    var data = e.currentTarget.dataset;
    let url = data.route
    if(!url){
      util.showToast('正在开发中....')
      return
    }
    wx.navigateTo({
      url: url + '?type='+data.type
    })
  }
})