var util = require('../../../utils/util')
var api = require('../../../config/api.js')
var dateTimePicker = require('../../../utils/dateTimePicker.js')
Page({
  data: {
    show: {
      title: '',
      content: '',
    },
    type: '',
    sourceType: ['camera', 'album'],
    imgUrl: '',
    //停车订单申诉
    showQImg: true, //是否显示问题图片
    appealType: [], //申诉类型
    appealImgUrl: '', //问题咨询的base64
    showAImg: true, //是否显示问题图片
    selectAppealType: 'WRONG_PARKING_TIME', //选择问题自选类型
    parkingBillInfo: {},
    appealRemark: '', //申诉原因
    end_time: '',
    start_time: '',
    dateTimeArray: '', //时间数组
    startYear: 2000, //最小年份
    endYear: 2050, // 最大年份
    start_time_p: '', //显示的入车时间
    end_time_p: '', //显示的出车时间
  },
  onLoad(options) {
    var type = options.type
    this.setData({
      type: type,
    })
    this.getAtyp()
    this.setData({
      parkingBillInfo: {
        parkCode: options.parkCode,
        uniqueId: options.uniqueId,
        parkName: options.parkName,
        plateNo: options.plateNo,
        time: options.time,
        timeDesc: options.timeDesc,
        amount: options.amount,
        status: options.status,
      },
    })
    this.getArrearsAppealDetail(options.parkCode, options.uniqueId)
  },
  getArrearsAppealDetail: function (parkCode, uniqueId) {
    //获取申诉订单详情
    var that = this
    util
      .request(
        api.getArrearsAppealDetail,
        {
          parkCode: parkCode,
          uniqueId: uniqueId,
        },
        'GET'
      )
      .then(function (res) {
        if (res.code == '0') {
          wx.hideLoading()
          var parkEndTime = res.result.parkEndTime //出车时间

          var obj = dateTimePicker.dateTimePicker(that.data.startYear, that.data.endYear)

          that.setData({
            start_time_p: res.result.parkStartTime,
            start_time: obj.dateTime,
            end_time: obj.dateTime,
            end_time_p: parkEndTime == null ? that.getNowTime() : parkEndTime, //1.8修改
            dateTimeArray: obj.dateTimeArray,
          })
        } else {
          util.showToast(res.message)
        }
      })
  },
  getNowTime() {
    var date = new Date() //获取当前时间
    var year = date.getFullYear() //获取当前年份
    var month = this.withData(date.getMonth() + 1) //获取当前月份
    var day = this.withData(date.getDate()) //获取当前日期
    var hour = this.withData(date.getHours()) //获取当前小时
    var minute = this.withData(date.getMinutes()) //获取当前分钟
    var second = this.withData(date.getSeconds()) //获取当前秒钟
    return year + '-' + month + '-' + day + ' ' + hour + ':' + minute + ':' + second //输出当前时间
  },
  withData(param) {
    return param < 10 ? '0' + param : '' + param
  },
  getAtyp: function (id) {
    var that = this
    util.request(api.getAType, {}, 'GET').then(function (res) {
      if (res.code == '0') {
        wx.hideLoading()
        that.setData({
          appealType: res.result,
        })
      } else {
        wx.hideLoading()
        util.showToast(res.message)
      }
    })
  },
  appealInput: function (e) {
    var flag = false
    var value = e.detail.value
    if (value.length > 0) {
      //申诉原因必填
      // var data = this.data;
      // if (data.selectAppealType == 'WRONG_PARKING_TIME' || data.selectAppealType == 'STILL_CHARGING') { //停车时间有误或人在计费
      //   if (data.start_time_p != '' && data.end_time_p != '') {
      //     flag = true;
      //   }
      // } else {
      //   flag = true;
      // }
      flag = true
    }

    this.setData({
      appealRemark: value,
      allowClicking: flag,
    })
  },
  chooseAppealType: function (e) {
    var id = e.currentTarget.id
    var remark = e.currentTarget.dataset.remark
    this.setData({
      selectAppealType: id,
      describe: remark,
    })
    // var allowClicking = false;
    // if (id == 'WRONG_PARKING_TIME' || id == 'STILL_CHARGING') { //停车时间有误或人在计费
    //   var data = this.data;
    //   if (data.start_time_p != '' && data.end_time_p != '' && data.appealRemark != '') {
    //     allowClicking = true;
    //   }
    // } else {
    //   allowClicking = true;
    // }
    // this.setData({
    //   allowClicking: allowClicking
    // })
  },
  tip: function () {
    wx.showModal({
      title: '提示',
      content: '请填写申诉原因',
      showCancel: false,
      success(res) {},
    })
  },
  showActionSheet: function () {
    const that = this
    wx.showActionSheet({
      itemList: ['拍照', '相册'],
      itemColor: '',
      //成功时回调
      success: function (res) {
        if (!res.cancel) {
          /*
           res.tapIndex返回用户点击的按钮序号，从上到下的顺序，从0开始
           比如用户点击本例中的拍照就返回0，相册就返回1
           我们res.tapIndex的值传给chooseImage()
          */
          that.chooseImage(res.tapIndex)
        }
      },
      //失败时回调
      fail: function (res) {
        console.log('获取图片调用失败')
      },
      complete: function (res) {},
    })
  },
  chooseImage(tapIndex) {
    const checkeddata = true
    const that = this
    wx.chooseImage({
      //count表示一次可以选择多少照片
      count: 1,
      //sizeType所选的图片的尺寸，original原图，compressed压缩图
      sizeType: ['original'],
      //如果sourceType为camera则调用摄像头，为album时调用相册
      sourceType: [that.data.sourceType[tapIndex]],
      success(res) {
        // tempFilePath可以作为img标签的src属性显示图片
        const tempFilePaths = res.tempFilePaths
        for (var x = 0; x < tempFilePaths.length; x++) {
          wx.getFileSystemManager().readFile({
            filePath: tempFilePaths[x], //选择图片返回的相对路径
            encoding: 'base64', //这个是很重要的
            success: res => {
              //成功的回调//返回base64格式
              console.log('data:image/png;base64,' + res.data)
              that.upImg(res.data)
            },
          })
        }
        that.setData({
          showQImg: false,
          showImgUrl: tempFilePaths,
        })
      },
    })
  },
  upImg: function (img) {
    var fileBase = 'data:image/png;base64,' + img
    var that = this
    util.showLoading('上传中')
    var fileType = 4
    var type = that.data.type
    if (type == 'ProblemConsultation') {
      fileType = 3
    }
    util
      .request(
        api.uploadPicture,
        {
          fileBase: fileBase,
          fileType: fileType,
        },
        'POST'
      )
      .then(function (res) {
        if (res.code == '0') {
          wx.hideLoading()
          if (type == 'ProblemConsultation') {
            that.setData({
              questionImgUrl: res.result,
            })
          } else {
            that.setData({
              appealImgUrl: res.result,
            })
          }
        } else {
          wx.hideLoading()
          util.showToast(res.message)
        }
      })
  },
  addParkingBillAppeal: function () {
    var that = this
    util.showLoading('正在加载...')
    var data = that.data
    util
      .request(
        api.addAppeal,
        {
          appealInTime: data.start_time_p,
          appealOutTime: data.end_time_p,
          parkCode: data.parkingBillInfo.parkCode,
          uniqueId: data.parkingBillInfo.uniqueId,
          appealRemark: data.appealRemark,
          appealType: data.selectAppealType,
          picUrls: [data.appealImgUrl],
        },
        'POST'
      )
      .then(function (res) {
        if (res.code == '0') {
          wx.hideLoading()
          wx.showModal({
            title: '提示',
            content: '提交成功！',
            showCancel: false,
            success(res) {
              if (res.confirm) {
                wx.navigateBack({
                  delta: 1,
                })
              }
            },
          })
        } else {
          wx.hideLoading()
          util.showToast(res.message)
        }
      })
  },
  /**
   * 选择时间
   * @param {*} e
   */
  changeDateTime(e) {
    let dateTimeArray = this.data.dateTimeArray,
      { type, param } = e.currentTarget.dataset
    this.setData({
      [type]: e.detail.value,
      [param]:
        dateTimeArray[0][e.detail.value[0]] +
        '-' +
        dateTimeArray[1][e.detail.value[1]] +
        '-' +
        dateTimeArray[2][e.detail.value[2]] +
        ' ' +
        dateTimeArray[3][e.detail.value[3]] +
        ':' +
        dateTimeArray[4][e.detail.value[4]],
    })
  },
  changeDateTimeColumn(e) {
    var dateArr = this.data.dateTimeArray,
      { type } = e.currentTarget.dataset,
      arr = this.data[type]
    arr[e.detail.column] = e.detail.value
    dateArr[2] = dateTimePicker.getMonthDay(dateArr[0][arr[0]], dateArr[1][arr[1]])
    this.setData({
      dateTimeArray: dateArr,
      [type]: arr,
    })
  },
})
