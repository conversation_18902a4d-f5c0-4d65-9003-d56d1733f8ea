<!--pages/components/twoBtnDialog/twoBtnDialog.wxml-->
<view class="model" catchtouchmove='preventTouchMove' wx:if='{{showModal}}'></view>
<view class="modalDlgTwo" catchtouchmove='preventTouchMove' wx:if='{{showModal}}'>
  <text class='modalDlgTwo-title'>{{title}}</text>
  <text class='modalDlgTwo-title-t'>{{tipText}}</text>
  <view class="modalDlgTwo-border"></view>
  <view class='modalDlgTwo-xyView'>
    <view class='modalDlgTwo-xyView-text' bindtap="leftBtn">{{leftBtnText}}</view>
    <view class='modalDlgTwo-xyView-text ' bindtap="rightBtn" style="color: #4768F3;">{{rightBtnText}}</view>
  </view>
</view>
