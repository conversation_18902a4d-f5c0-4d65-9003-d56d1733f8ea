/**app.wxss**/
@import 'pages/components/loginDialog/loginDialog.wxss';
@import 'pages/components/twoBtnDialog/twoBtnDialog.wxss';
@import 'assets/icon/index.wxss';

.border-b {
  border-bottom: 1rpx solid #dfdfdf;
}

.scrollPage {
  height: calc(100vh - 100rpx);
}
.common-title {
  display: flex;
  min-width: 120rpx;
  height: 38rpx;
  font-size: 30rpx;
  font-weight: 800;
  text-align: center;
  color: #000000;
  line-height: 39rpx;
  margin: 0 auto 20rpx 50rpx;
  background: linear-gradient(90deg, rgba(158, 156, 247, 0), #0160fd);
  background-size: 120rpx 6rpx;
  background-repeat: no-repeat;
  background-position: 0 bottom;
}

.backgroundImg {
  background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAHgAAAAGCAYAAAAITFpjAAAAAXNSR0IArs4c6QAAAOZJREFUSEvtlktuxDAMQ0naRmdX9P7X6El6nGIiFpSng1l02aWDIKKsRztB4A/fvz4/6tuj3mrcLs26e5Q1a3Gu4rQ5y5zT2BpcA1sLXAamUiOX7pjJyblgT6ZWYdXaUOcUhxOtAYIy5QsSSVsSEiHBcokK4jwaEQzyig2Sxe6i/TFKLMtpJ9MkXJHxkSwoNgKEkb5EJ33ErrPN2Lq5ZhIzRFHjUQfy0r/cg21Pxkbu6GY228wzf7ZFePP72zfXdQDpAy/Mi061kb8unh98fvCZwWcGnyV6L+VniT578NmDzyHrPw9ZP9SFRPMpSb2tAAAAAElFTkSuQmCC');
}

.iphoneX-safe-area {
  padding-top: 44rpx;
  padding-bottom: 34rpx;
}

.saveOutView {
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow: hidden;
}

.rowView {
  flex-direction: row;
  display: flex;
}

.columnView {
  flex-direction: column;
  display: flex;
}

.self-adaptionBox {
  display: flex;
  flex-direction: column;
  flex: 1;
  height: 1rpx;
}
.scrollViewSa {
  flex: 1;
  height: 1rpx;
}

/* 常规按钮样式 */
.convention_button {
  /* 通用渐变蓝绿大按钮 */
  text-align: center;
  border-bottom: 10px;
  font-size: 34rpx;
  font-weight: 400;
  color: #ffffff;
  line-height: 40rpx;
  width: calc(100% - 40rpx) !important;
  height: 80rpx;
  background: linear-gradient(90deg, #458bff 0%, #08d7ae 100%);
  box-shadow: 0rpx 3rpx 10rpx 1rpx rgba(37, 107, 245, 0.2);
  border-radius: 40rpx 40rpx 40rpx 40rpx;
  margin-bottom: 20rpx;
}
/* 通用渐变蓝蓝大按钮 */
.convention_button.cancel {
  background: linear-gradient(270deg, #458bff 0%, #256bf5 100%);
}
/* 通用渐变橘黄大按钮 */
.convention_button.next {
  background: linear-gradient(90deg, #ff5745 0%, #ffae00 100%);
}

.convention_bottom_btn {
  position: absolute;
  bottom: 60rpx;
  left: 20rpx;
}

.box_convention_button {
  width: 100%;
  height: 80rpx;
  padding: 80rpx 0;
  background-color: #f6f7fb;
}

/* 中等按钮样式 */
.convention_bottom_btn_medium {
  width: 156rpx;
  height: 56rpx;
  background: linear-gradient(90deg, #ff5745 0%, #ffae00 100%);
  opacity: 1;
  border-radius: 40rpx 40rpx 40rpx 40rpx;
  font-size: 26rpx;
  font-weight: 400;
  color: #ffffff;
  line-height: 56rpx;
  text-align: center;
  margin: auto;
}

/* 双按钮样式 */
.box_conventionTwo_button {
  font-size: 34rpx;
  font-weight: 400;
  color: #ffffff;
  line-height: 80rpx;
  text-align: center;
  margin-left: 20rpx;
  margin-bottom: 60rpx;
  width: calc(100% - 40rpx);
  display: flex;
  flex-direction: row;
  height: 80rpx;
}

.conventionTwo_button-left {
  width: 355rpx;
  height: 80rpx;
  background: #41e0ac;
  border-radius: 100rpx 0rpx 0rpx 100rpx;
  opacity: 1;
}

.conventionTwo_button-right {
  width: 355rpx;
  height: 80rpx;
  background: linear-gradient(270deg, #458bff 0%, #256bf5 100%);
  box-shadow: 0rpx 3rpx 10rpx 1rpx rgba(37, 107, 245, 0.2);
  border-radius: 0rpx 100rpx 100rpx 0rpx;
  opacity: 1;
}

/* 成功状态的图标样式 */
.successStatus-icon {
  margin: 163rpx auto 22rpx auto;
  width: 94rpx;
  height: 94rpx;
}

.successStatus-icon-img {
  width: 94rpx;
  height: 94rpx;
}

.successStatus-text {
  text-align: center;
  height: 50rpx;
  font-size: 26rpx;
  font-weight: bold;
  color: #353535;
  line-height: 50rpx;
}
/* 自动换行 */
.autowrapBox {
  display: flex;
  flex-wrap: wrap;
  flex-direction: row;
  box-sizing: content-box;
}
/* 纵向居中 */
.verticalCenter {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
}
page {
  background: #f6f7fb;
}
/* 向右剪头 */
.icon-jt-left {
  width: 15rpx;
  height: 15rpx;
  opacity: 1;
  background-color: transparent;
  /* 模块背景为透明 */
  border-color: #888888;
  border-style: solid;
  border-width: 4rpx 4rpx 0 0;
  transform: rotate(45deg);
  /*箭头方向可以自由切换角度*/
  margin: auto;
}
.viewPlaceholder_private {
  height: 50rpx;
  width: 100%;
}
.convention_button.bcolor {
  /* 通用蓝大按钮 */
  background: #458bff;
}
.convention_button.gcolor {
  /* 通用绿大按钮 */
  background: #41e0ac;
}
.convention_button.ocolor {
  /* 通用绿大按钮 */
  background: #ff5745;
}
