const api = require("../../../config/api");
var util = require('../../../utils/util');
Page({

  /**
   * 页面的初始数据
   */
  data: {
    url:''
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.setData({
      type: options.id
    })
    this.getInfo();
  },
  getInfo: function () {
    util.showLoading('正在加载…')
    var that = this;
    util.request(api.agreementAddress, {}, 'GET').then(function (res) {
      if (res.code == '0') {
        that.setData({
          url: res.result[that.data.type]
        })
        wx.hideLoading();
      } else {
        wx.hideLoading();
        util.showToast(res.message)
      }
    });
  }
})