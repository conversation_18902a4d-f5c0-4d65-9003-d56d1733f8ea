.icon-sjx-down {
  width: 0;
  height: 0;
  border: 10rpx solid;
  border-color: #353535 transparent transparent;
  margin-top: 20rpx;
}

.icon-sjx-up {
  width: 0;
  height: 0;
  border: 10rpx solid;
  margin-top: 10rpx;
  border-color: transparent transparent #353535;
}

.icon-sjx-left-tilte {
  height: 0px;
  width: 0px;
  border: 10rpx solid #000;
  border-top-color: transparent;
  border-bottom-color: transparent;
  border-left-color: #4768f3;
  border-right-color: transparent;
  margin: auto;
}

.btnBox-down {
  width: 100%;
  height: 96rpx;
  margin: 30rpx 0x 45rpx 0;
  display: flex;
  flex-direction: row;
}

.btnBox-down button {
  width: 328rpx;
  height: 96rpx;
}

.btn-color-blue {
  background-color: #4768f3 !important;
}

.nullImg {
  height: 283rpx;
  width: 367rpx;
  margin: 138rpx 193rpx 57rpx 193rpx;
}

.nullTip {
  width: 100%;
  height: 42rpx;
  font-size: 30rpx;
  font-weight: bold;
  color: #353535;
  line-height: 56rpx;
  text-align: center;
}

.Icon-search {
  width: 30rpx;
  height: 30rpx;
  margin: 32rpx 10rpx auto 0;
}

.zhtc-back {
  width: 70rpx;
  height: 55rpx;
  position: absolute;
  top: 66rpx;
  left: 23rpx;
}

.mode-back {
  width: 102rpx;
  height: 40rpx;
  position: absolute;
  top: 186rpx;
  left: 23rpx;
  background-color: #41e0ac;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 400;
  color: #ffffff;
  line-height: 40rpx;
  text-align: center;
  background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABcAAAAXCAYAAADgKtSgAAAAAXNSR0IArs4c6QAAAUNJREFUSEutlUsuBFEUhv8v8Qim3kuwB4KJoTERBhLbsQMSIuzAEGERRp5TCzD75XRudbp1ddWtrqrhzbnff/7zuIUyP9vrki4l7QO/OdfICbK9JulD0rSkH2A5514t3PZqAs8m4AVw2hpue0XSu6S5BLsCTnLAETM28xLwNXCcCx4LT+A3SfMJdgMcNQGXwm0vpRoX4FvgsCl4BG47piBq3Bo8BO8a3IfbXpT0OZDxHXAwSSkG71DSvE7Avcxt30vaK8YSqF2sXEcBn0kliYUJ8AuwmQuoiutlaXtK0pekWPU4ewa22gr0S2A7HqUQKBy0Fhiqb4nAE7A9qYOR5nUpUDoZJU2eyEHVq/i/B4/ATpMSVc50cvAtKR6ziG3koHZhbMcfKKaoEHgAdnMc1MLTHgwKvAIbncGTwIKkc+AsBxwxf70ucRgR/4hWAAAAAElFTkSuQmCC');
  background-size: 23rpx 23rpx;
  background-repeat: no-repeat;
  background-position: 10rpx;
}

.navigationPng {
  width: 100%;
  height: 750rpx;
  position: absolute;
  top: 0;
  display: block;
  width: 100%;
  z-index: -1;
}

.scroll {
  flex: 1;
  overflow: scroll;
}

.saveBottom {
  height: 100rpx;
  padding-bottom: constant(safe-area-inset-bottom);
  /*兼容 IOS<11.2*/
  padding-bottom: env(safe-area-inset-bottom);
  /*兼容 IOS>11.2*/
}

.safe-padding {
  box-sizing: border-box;
  padding-bottom: calc(20rpx + constant(safe-area-inset-bottom));
  /*兼容 IOS<11.2*/
  padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
  /*兼容 IOS>11.2*/
}

/* 广告栏s */
.swiper {
  width: calc(100% - 100rpx);
  height: 261rpx;
  margin: 0 50rpx 30rpx 50rpx;
  border-radius: 30rpx;
}

.swiper swiper {
  width: 100%;
  height: 100%;
  border-radius: 30rpx;
}

.swiper .wx-swiper-dot {
  border-radius: 30rpx;
}

/* 广告栏e */
.center-one {
  flex-direction: row;
  display: flex;
  margin: 50rpx 0 32rpx 0;
}

.center-one-v {
  flex-direction: column;
  display: flex;
  align-items: center;
}

.center-one-v-img {
  margin-top: 20%;
  width: 86rpx;
  height: 86rpx;
  justify-content: center;
  position: relative;
}

.center-one-v-img image {
  width: 100%;
  height: 100%;
  z-index: 1;
}

.center-one-v-text {
  width: 100%;
  height: 25rpx;
  font-size: 22rpx;
  font-weight: 500;
  color: #ffffff;
  line-height: 25rpx;
  text-align: center;
  margin-top: 14rpx;
}

/* 提醒数目 B*/
.cu-tag {
  vertical-align: middle;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  white-space: nowrap;
  font-size: 26rpx;
  font-weight: bold;
  color: #ffffff;
}

.cu-tag.badge {
  position: absolute;
  top: -10rpx;
  right: -10rpx;
  min-width: 32rpx;
  min-height: 32rpx;
  line-height: 32rpx;
  background: #ff2d2d;
  border-radius: 50%;
  z-index: 1;
}

/* 提醒数目 E*/
/* 规则图标橘色版本圆圈i s*/
.circle-i-orange {
  min-width: 28rpx;
  min-height: 28rpx;
  font-size: 26rpx;
  font-weight: 400;
  color: #606266;
  line-height: 40rpx;
  background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABwAAAAcCAYAAAByDd+UAAAAAXNSR0IArs4c6QAABStJREFUSEudlm1sU2UUx//nubcbY5uAb2AIZmNrO8YEQte7DyaKMX4QgpkzXW8nA4RgIIZEGWKI74aQyGtCDGZk+LIJazdRGRiMMRE/YLLbLaCw0RfGi2DwJRjoYOu23ueYWzYcc22n/dAvz/mfX57nnHvOnzDB3wWfq2QIyuMAisA0JSkjvmFK2SMS/IPzUGdoIqkoXVCPxzVFKspaJloJoCRtQsZZMH9MMaq3H2uPpYpNCQzpFasE8TaA7gM4DKZWQfJEfx+6RNat362E2UrudJO5jIR4FEA1QHYw/8mMjc6A0Tge9F/ALk9plqrkNRBRLZh7JFOdM9DeRgCnuyEDFPW6n2WinURUAOaPYlFzbXln59Bo3V3AC4sKJg3NmN4GwlNg/rT/r/hL87/9+dZEajMS0+UpzbMpefUgqgHz0VjUrBoNvQsY8WktAHnAcofDH3z1v4DGxoa9FTtIoI4lNzoDxoqR8zvAcLV7DSliH4EP2JuNZZlgp72uomwh1pOJH+0twZbx4iO6dmD4ps87/MbBZGNbf5dqHpk2ICefY3AsbouXzW9K/4w/1c7LzRnKCYMwE4yELT7wQOFXp66Phf5WOy/3RiKnSzBn99nixVbeJDDqq3iLgXeZpe70BwOZbhfW3U8Sie+G4+L91/rvT1XriE/zAXSQmDfY/cZuegcQNbp2ESDT7m8vIkBmAnZXuR5Ss9QzIM4TEnXFAeODVJoWD5QFinYBRL2O5va5dNaz0KWotg5I3uYIGK9lgo2cW0NhMKGoc740rmXShHVtFxG9IhJDdop4tfUQtEdCPl3SHPwmk/j/nEd97qUM0caSV1DYq+0hQetpALPsX7RfyZTQ+s5UMblGCFHFTEXM/L4zYDSk00Vq3LPBogeMLRTWtU+IaEXsZiK3/EhnX1qh7l7JoFoCHweJ95JtLlFpD7QfTqcLPePMF7lTY2Dss4DWGFvdr/bnpfsczuoLCgTUJQ5/x96oV1sCQUfAfNluGoXUCjMdMDl91PxeZt5PId29XZDYqNJgweyDJy9lelLrPOKr+BrAYrB80+EPbsmkCXm0QqHSeasxKewtf5GEUs/SXOwMdBzLJL5dD4oyYNpAs2Y3tyc3R/pSaEtAdJQlr6Fub3mZKpTTAG93NBubMomjunsrk9jMzIedfqMyU/ztwaLtZNAGyKHS5KSJ+LQeAFm/XjUKnziORJokFLGGBNHDkk1Pib/j80zADpfLlu9QLglGr91vOEdG22YGtjKby53+jqZUSc55FrqkNSSYB2O3zGnT8tWZJmOzYPl2sT94eTxdSC9fLUhpgORNjoCxPQm0poapKucAxEnQXPuB8S1C1Otex0LsBTBk7ToQ+kwefGOO/9TF8WDWUojLnG4iElMTvUUPtnbfvLOeQnr5KkHKfinlIWcg6Blvw4eeWziPbOrrBP7FNGXjnNbO06leI+kAfNphgJYSZK29OfhZ8rsdLQj7tACBqhm8y9ls1GWqT9rO9Lp3Q4iXmbnJ6TeWj8TeBfx+UcGkmcMWg5ib8m3xdTMy7Max0OToU/I/JMKypMW4x6wq3/ePrxnXRGUpeQ1smSjweZK0oXiCJqpH1ypNwNoMBQRuuHLVWDe261PaxKiurZagbUS4d8QmSpInlAFxZlDp/cO6mWUTwVwmLZvIqAYN20TIulTdntYIn6xcMDUv27ZWEr1ARI70NeVuywjLvlh9SVu4N1VsWuBoUdRTUcoKPwaCNdpuW33m6wzZo0IcLwoYkYk02d/0x029oWxESwAAAABJRU5ErkJggg==');
  background-position: left;
  background-repeat: no-repeat;
  background-size: 28rpx 28rpx;
  text-align: right;
}

/* 规则图标橘色版本圆圈i e*/
/* 圆角列表背景 */
.common_listBg {
  width: calc(100% - 130rpx);
  height: 96rpx;
  background: #ffffff;
  border-radius: 20rpx 20rpx 20rpx 20rpx;
  opacity: 0.96;
  margin: 20rpx;
  font-size: 34rpx;
  font-weight: 400;
  color: #353535;
  line-height: 96rpx;
  padding: 0 50rpx 0 40rpx;
}

.common_listBg_text {
  color: #7e7e7e;
}
