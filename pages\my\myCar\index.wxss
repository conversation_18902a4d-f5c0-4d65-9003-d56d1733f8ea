@import '../../components/common.wxss';
@import '../../../pages/common/common.wxss';
@import '../../../pages/common/index.wxss';

page {
  background: #f6f6f6;
  width: 100%;
  height: 100%;
}

.nullTip {
  text-align: center;
  width: 100%;
  height: 47rpx;
  font-size: 34rpx;
  font-weight: 400;
  color: #888888;
  margin-top: 40rpx;
}

.title {
  margin: 40rpx 20rpx 20rpx 20rpx;
  min-width: 120rpx;
  height: 42rpx;
  font-size: 30rpx;
  font-weight: bold;
  color: #000000;
  line-height: 42rpx;
}

.carListInfo-scroll {
  width: 100%;
}

.carListInfo-d {
  background: #ffffff;
  border-radius: 0rpx 0rpx 0rpx 0rpx;
  opacity: 1;
  width: calc(100% - 130rpx);
  height: 142rpx;
  background: #ffffff;
  border-radius: 20rpx 20rpx 20rpx 20rpx;
  opacity: 1;
  margin: 20rpx 0 20rpx 20rpx;
  padding: 27rpx 45rpx;
}

.carListInfo-scroll image {
  width: 64rpx;
  height: 64rpx;
  margin: 27rpx 10rpx 27rpx 45rpx;
}

.carListInfo-t {
  font-weight: 400;
  line-height: 83rpx;
  min-width: 156rpx;
  height: 83rpx;
  font-size: 34rpx;
  font-weight: bold;
  color: #353535;
  margin-right: 20rpx;
}

.carListInfo-d-yrz {
  width: 100rpx;
  height: 38rpx;
  background: #d9f9ee;
  border-radius: 5rpx 5rpx 5rpx 5rpx;
  opacity: 1;
  font-size: 24rpx;
  font-weight: 400;
  color: #34ba8e;
  line-height: 38rpx;
  text-align: center;
  margin-top: 26rpx;
  margin-right: 10rpx;
}

.carListInfo-d-mrcl {
  width: 124rpx;
  height: 38rpx;
  background: #ffffff;
  border-radius: 5rpx 5rpx 5rpx 5rpx;
  opacity: 1;
  font-size: 24rpx;
  background: #ffe7d5;
  font-weight: 400;
  color: #da5937;
  line-height: 38rpx;
  text-align: center;
  margin-top: 24rpx;
  margin-right: 10rpx;
}

.carListInfo-d-gd {
  width: 32rpx;
  height: 44rpx;
  font-size: 32rpx;
  font-weight: bold;
  color: #7e7e7e;
  line-height: 44rpx;
  margin-left: auto;
}

.carListInfo-d-btn {
  width: 200rpx;
  height: 56rpx;
  line-height: 56rpx;
  opacity: 1;
  font-size: 26rpx;
  font-weight: 400;
  text-align: center;
  margin-left: auto;
  border-radius: 50rpx;
}

.carListInfo-d-btn.blue {
  background: #256bf5;
  border: 1rpx solid #256bf5;
  color: #ffffff;
}

.carListInfo-d-btn.red {
  background: rgba(218, 89, 55, 0.2);
  border: 1rpx solid #da5937;
  color: #da5937;
}

.carListInfo-d-btn.blue2 {
  background: rgba(37, 107, 245, 0.1);
  border: 1rpx solid rgba(37, 107, 245, 0.2);
  color: #256bf5;
}

.top {
  width: 100%;
  height: 430rpx;
  margin: 0 auto;
  /* background-color: #fff; */
  border-radius: 15rpx;
}

.imagesize {
  margin-left: 15rpx;
  justify-content: center;
  align-items: center;
  vertical-align: middle;
  margin-top: -7rpx;
}

.toptwo {
  width: 100%;
  display: flex;
}

.paizhao {
  height: 40rpx;
  line-height: 40rpx;
  margin-top: 30rpx;
  background-color: #e5f4ff;
  border-radius: 10rpx;
  padding-right: 13rpx;
}

.ptitle {
  height: 38rpx;
  font-size: 28rpx;
  font-weight: 400;
  color: #6883f5;
  text-align: center;
  margin-bottom: 28rpx;
}

.cartitle {
  width: 61%;
  height: 100rpx;
  line-height: 100rpx;
  font-size: 20px;
  font-weight: 600;
  margin-left: 7%;
  color: #333333;
  background-color: #fff;
}

.bottomtext {
  width: 100%;
  text-align: center;
  height: 38rpx;
  font-size: 28rpx;

  font-weight: 400;
  color: #6883f5;
  margin: 10rpx 0 20rpx 0;
}

.con-query {
  /* height: 4.8rem; */
  width: 100%;
  border-radius: 8px;
  /* background-color: #FFF; */
}

.pages_header {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.pages_header_top {
  width: 33.3%;
  height: 60rpx;
  border-left: 5px solid green;
  border-right: 5px solid green;
}

.pages_header_btm {
  width: 70%;
  background: green;
  height: 120rpx;
  line-height: 120rpx;
  text-align: center;
  color: white;
  border-radius: 10rpx;
  font-weight: normal;
  font-size: 16pt;
}

.tips {
  text-align: center;
  margin: 60rpx 0;
  font-size: 12pt;
  color: #888888;
}

.plate-input-text {
  text-align: center;
  line-height: 90rpx;
  color: #f39900;
}

.plate-input-flag {
  float: right;
  margin-right: 8%;
  font-size: 14px;
}

.plate-input-flag .new-energy {
  color: #14c414;
}

.plate-input-body {
  height: 80rpx;
  width: calc(100% - 18rpx);
  margin: 0 0 35rpx 0;
  margin-left: 9rpx;
}

.plate-input-content {
  display: flex;
  flex-direction: row;
  height: 80rpx;
}

.plate-nums-foc {
  width: 70rpx;
  height: 80rpx;
  display: flex;
  flex: 1;
  margin: 0 5rpx 0 5rpx;
  align-items: center;
  justify-content: center;
  text-align: center;
  box-sizing: border-box;
  background: #ffffff;
  opacity: 1;
  border-radius: 5rpx 5rpx 5rpx 5rpx;
}

.plate-nums-foc .plate-num-text {
  border: 2rpx solid #4768f3;
}

.plate-nums-first {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.plate-num-text {
  font-size: 40rpx;
  font-weight: 300;
  border-radius: 5rpx 5rpx 5rpx 5rpx;
  opacity: 1;
  border: 1rpx solid #7e7e7e;
  line-height: 80rpx;
  width: 70rpx;
  height: 80rpx;
  background: #ffffff;
}

.new-plate-input-content {
  display: flex;
  flex-direction: row;
  height: 100rpx;
}

.kb_top {
  align-content: relative;
  width: 100%;
  height: 74rpx;
  background: #fff;
  border-top: solid #ebebeb 2rpx;
  border-bottom: 15rpx solid #f4f4f4;
}
.kb_top_text {
  position: absolute;
  right: 0;
  display: block;
  height: 74rpx;
  padding: 0 34rpx;
  color: #6883f5;
  line-height: 74rpx;
  font-size: 30rpx;
}

.keyboard {
  z-index: 9999;
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  height: auto;
  background: #f4f4f4;
  display: flex;
  flex-wrap: wrap;
  border-bottom: 15rpx solid #f4f4f4;
}

.td {
  flex-grow: 1;
  text-align: center;
  font-size: 34rpx;
  height: 86rpx;
  line-height: 80rpx;
  background: #fff;
  margin: 10rpx 5rpx;
  color: #333;
  border-radius: 2rpx;
  box-shadow: 0rpx 2rpx 0rpx #a9a9a9;
}

.td_nor {
  flex: 1 1 6%;
}

.td_num {
  flex: 1 1 8%;
}

.td_spec {
  flex: 1 1 12%;
}

.board_bg {
  box-shadow: 0 0 0 #e5e5e5;
  background: #e5e5e5;
}

.del-first {
  position: absolute;
  bottom: 10rpx;
  right: 10rpx;
  width: 100rpx;
  height: 86rpx;
  background-color: #fff;
  box-shadow: 0rpx 2rpx 0rpx #a9a9a9;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 10rpx;
}

.del-hover {
  position: absolute;
  bottom: 10rpx;
  right: 10rpx;
  width: 137rpx;
  height: 86rpx;
  background-color: #e5e5e5;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 10rpx;
  box-shadow: 0 0 0 #e5e5e5;
}

.del-img {
  display: block;
  width: 46rpx;
  height: 38rpx;
}

.color-white {
  color: #ffffff;
}

.color-red {
  color: #ff0000;
}

.bule {
  color: #0000ff;
}

.navigator {
  width: calc(100% - 160rpx);
  margin: 0 50rpx;
}

.navigator-text,
.picker {
  font-size: 34rpx;
  font-weight: 400;
  color: #353535;
}

.box {
  margin: 20rpx;
  width: calc('100%-100rpx');
  background: #ffffff;
  box-shadow: 0rpx 10rpx 20rpx 0rpx rgba(228, 234, 248, 0.5);
  border-radius: 20rpx;
  box-sizing: border-box;
  padding: 0 23rpx;
}

.box .content {
  height: 88rpx;
  font-size: 34rpx;
  font-weight: 400;
  color: #353535;
  line-height: 88rpx;
}

.navigator-arrow {
  padding-right: 13px;
  padding-top: 35rpx;
  position: relative;
}

.navigator-arrow:after {
  border-color: #c7c7cc;
}
