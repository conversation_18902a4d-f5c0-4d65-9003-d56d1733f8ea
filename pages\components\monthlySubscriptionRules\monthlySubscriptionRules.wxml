<view class="half-screen" catchtouchmove="preventTouchMove">
  <!--屏幕背景变暗的背景  -->
  <view class="background_screen" catchtap="hideModal" wx:if="{{isShow}}"></view>
  <!--弹出框  -->
  <view animation="{{animationData}}" class="attr_box" wx:if="{{isShow}}">
    <view class="dialog-box">
      <view class="dialog-head">
        <view class="dialog-title">包月规则</view>
        <view class="close2ImgBox">
          <image src="../../../image/close.png" class="close2Img" catchtap="hideModal"></image>
        </view>
      </view>
      <scroll-view scroll-y="true" style="max-height: 910rpx;">
        <view class='dialog-content'>
          <block wx:for="{{rules}}" wx:key="index">
            <view class="box rowView " data-dialogid="{{index}}" catchtap="getValueTap">
              <view class="left columnView">
                <view class="one">{{item.ruleName}}</view>
                <view class="two">{{item.timeInterval}}</view>
                <view class="rowView three">
                  <view class="text">{{item.parkInfoDesc}}</view>
                  <view class="typeBox typeBox{{item.energy}}">{{type[item.energy]}}</view>
                </view>
              </view>
              <view class="right rowView">
                <view class="text">{{item.pkgDesc}}</view>
                <view class="icon-jt-left"></view>
              </view>
            </view>
          </block>
        </view>
      </scroll-view>
    </view>
  </view>
</view>