{"pages": ["pages/index/index", "pages/my/my", "pages/fw/fw", "pages/parkingSpaceInquiry/index", "pages/parkingSpaceInquiry/mapInfo/index", "pages/parkingSpaceInquiry/searchPage/index", "pages/parkingSpaceInquiry/info/index", "pages/my/set/index", "pages/my/set/about/index", "pages/supplementaryPayment/index", "pages/supplementaryPayment/details/index", "pages/monthlyPackage/index", "pages/monthlyPackage/add/index", "pages/monthlyPackage/add/find/index", "pages/monthlyPackage/add/pay/index", "pages/monthlyPackage/details/index", "pages/parkingSpaceInquiry/fwMap/index", "pages/vehicleReservation/index", "pages/vehicleReservation/add/index", "pages/my/myCar/add/index", "pages/my/myCar/index", "pages/recharge/index", "pages/recharge/details/index", "pages/customerServiceHelp/index", "pages/customerServiceHelp/subpage/problemstatement", "pages/my/myCar/add/vehicleCertification/index", "pages/monthlyPackage/add/noData/index", "pages/orderQuery/index", "pages/orderQuery/detail", "pages/parkingPayment/index", "pages/parkingPayment/discountCoupon/index", "pages/common/payState/index", "pages/orderQuery/rechargeRefund/index", "pages/monthlyPackage/details/refund/index", "pages/monthlyPackage/details/refund/result/index", "pages/my/myPoints/index", "pages/my/myLevel/index", "pages/my/discountCoupon/index", "pages/discountCoupon/h5/index", "pages/my/set/logoff/logoffCheck/index", "pages/my/set/logoff/logoffConfirm/index", "pages/my/set/logoff/logoffConfirm/verification/index", "pages/systemMessage/index", "pages/common/viewAgreement/index", "pages/vehicleReservation/add/pay/index", "pages/vehicleReservation/cancel/index", "pages/my/set/account/index", "pages/my/set/account/changePhone/index", "pages/my/set/paymentMnagement/index", "pages/my/set/paymentMnagement/senselessPayment/index", "pages/customerServiceHelp/subpage/order/index", "pages/customerServiceHelp/feedback/index", "pages/recharge/withdrawal/index", "pages/parkingSpaceInquiry/fwSearchPage/index", "pages/customerServiceHelp/subpage/parkingOrderAppeal", "pages/invoice/menu", "pages/invoice/orderList/orderList", "pages/invoice/invoiceTitle/invoiceTitle", "pages/invoice/invoiceDetail/invoiceDetail", "pages/invoice/invoiceResult/invoiceResult", "pages/invoice/invoiceHistory/invoiceHistory", "pages/invoice/invoiceHistory/invoiceDetail", "pages/my/myCar/add/vehicleCertification/details", "pages/recharge/detail/index", "pages/vehicleReservation/details/index", "pages/common/loginTip/loginTip", "pages/parkingPayment/getTempCarorder", "pages/monthlyPackage/add/payNew/index", "pages/monthlyPackage/add/payNew/parkList", "pages/webview/webview", "pages/coupon/index", "pages/question/question", "pages/question-success/question-success", "pages/qrcodePay/index"], "window": {"navigationStyle": "custom", "navigationBarBackgroundColor": "#ffffff", "navigationBarTitleText": "畅行桂林", "navigationBarTextStyle": "black"}, "tabBar": {"color": "#888888", "borderStyle": "white", "selectedColor": "#4768F3", "list": [{"pagePath": "pages/index/index", "iconPath": "/image/my/home_normal.png", "selectedIconPath": "/image/my/home_selected.png", "text": "首页"}, {"pagePath": "pages/fw/fw", "iconPath": "/image/my/fw_normal.png", "selectedIconPath": "/image/my/fw_selected.png", "text": "服务"}, {"pagePath": "pages/my/my", "iconPath": "/image/my/my_normal.png", "selectedIconPath": "/image/my/my_selected.png", "text": "我的"}]}, "style": "v2", "sitemapLocation": "sitemap.json", "permission": {"scope.userLocation": {"desc": "你的位置信息将用于小程序定位"}}, "requiredPrivateInfos": ["getLocation"], "networkTimeout": {"request": 10000, "downloadFile": 10000}, "lazyCodeLoading": "requiredComponents", "usingComponents": {"cu-custom": "/components/cu-custom"}, "plugins": {"WechatSI": {"version": "0.3.6", "provider": "wx069ba97219f66d99"}}}