var util = require('../../utils/util')
var api = require('../../config/api.js')
var app = getApp()
Page({
  data: {
    loginStatus: app.globalData.userInfo.loginStatus, //登陆状态
    isBack: true, //是否显示返回按钮
    showLoginDialog: false, //登陆弹窗
    qflist: [],
    currentPage: 1,
    showNullMoreTip: false,
    showNullTip: false,
    pageSize: 20, //分页大小

    payList: [],
    balanceText: '', //当前余额
    selectPay: '', //支付方式
    discountCoupon: '无可用优惠券',
    couponCode: '', //优惠劵ID

    selectedList: [],
    invoiceAmout: 0,
    batchType: '',
    tradeNo: '',
  },
  onLoad(options) {
    console.log('🚀 ~ onLoad ~ options:', options)
    //10.11新增功能从公众号跳转到代付订单页面，扫码进入页面
    var that = this
    if (options.plateNo != undefined || options.q != undefined) {
      //说明是从公众号跳转过来的，或者扫码进入的
      that.setData({
        isBack: false,
      })
      wx.login({
        success: res => {
          if (res.code) {
            util
              .request(
                api.getLoginInfo,
                {
                  code: res.code,
                },
                'GET'
              )
              .then(function (res) {
                if (res.code == '0') {
                  var info = res.result
                  try {
                    wx.setStorageSync('token', info.token)
                    var userStatus = info.userStatus
                    var loginStatus = info.loginStatus
                    if (userStatus == 'REGISTERED_BOUND' && loginStatus == 'LOGGED_IN') {
                      //已注册且有车
                      that.initData()
                    } else {
                      //未注册获未登录时，跳转到登录页面

                      wx.navigateTo({
                        url: '/pages/common/loginTip/loginTip?tiptype=1',
                      })
                    }
                  } catch (e) {
                    console.log(e)
                  }
                  app.globalData.userInfo = info
                } else {
                  util.showToast(res.message)
                }
              })
          } else {
            console.log('登录失败！' + res.errMsg)
          }
        },
      })
    } else {
      if (app.globalData.userInfo.loginStatus != 'LOGGED_IN') {
        wx.navigateTo({
          url: '/pages/common/loginTip/loginTip?tiptype=1&&route=/pages/supplementaryPayment/index',
        })
      } else {
        that.initData()
      }
    }
  },
  initData: function () {
    this.setData({
      currentPage: 1,
      qflist: [],
      showNullMoreTip: false,
      showNullTip: false,
      selectedList: [],
      invoiceAmout: 0,
      batchType: '',
    })
    this.getList()
    this.getPayMethod()
  },
  getList: function () {
    util.showLoading('正在加载…')
    var that = this
    var data = that.data
    var currentPage = data.currentPage
    var pageSize = data.pageSize
    util
      .request(
        api.getArrearsList,
        {
          currentPage: currentPage,
          pageSize: pageSize,
        },
        'GET'
      )
      .then(function (res) {
        if (res.code == '0') {
          wx.hideLoading()
          if (res.result.total == 0) {
            that.setData({
              qflist: [],
              showNullTip: true,
            })
          } else {
            var records = res.result.records
            that.setData({
              qflist: that.data.qflist.concat(records),
              showNullTip: false,
            })
            if (res.result.pages == currentPage) {
              //没有数据
              that.setData({
                showNullMoreTip: true,
              })
            }
          }
          //加载下一页
          that.setData({
            currentPage: currentPage + 1,
          })
        } else {
          wx.hideLoading()
          util.showToast(res.message)
        }
      })
  },
  goxq(e) {
    var currentTarget = e.currentTarget
    var id = currentTarget.id
    wx.navigateTo({
      url: '/pages/supplementaryPayment/details/index?uniqueId=' + id,
    })
    // wx.navigateTo({
    //   url: '/pages/parkingPayment/index?uniqueId=' + id
    // })
  },
  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {
    if (this.data.showNullMoreTip) {
      util.showToast('没有更多数据了')
    } else {
      this.getList()
    }
  },
  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {
    this.setData({
      pageSize: 20,
      currentPage: 1,
      qflist: [],
    })
    this.getList()
    wx.stopPullDownRefresh()
  },
  goPage: function () {
    wx.redirectTo({
      url: '/pages/supplementaryPayment/index?plateNo=aaa',
    })
  },
  goPageMethod: function () {
    //登陆成功后跳转到点击登陆前选择的页面。

    wx.redirectTo({
      url: '/pages/supplementaryPayment/index?plateNo=aaa',
    })
  },
  getPayMethod() {
    const that = this
    util.request(api.getRechargeInfo, null, 'GET').then(function (res) {
      if (res.code !== '0') return
      const balance = Number(res.result.balance) || 0
      const payList = [
        { iconUrl: res.result.balance, name: '余额', payType: 'BALANCE_PAY' },
        {
          iconUrl: 'https://app.okguilin.com/image/payment/wechat_pay.png',
          name: '微信支付',
          payType: 'WECHAT_PAY',
        },
      ]
      that.setData({
        payList,
        selectPay: balance > 0 ? 'BALANCE_PAY' : 'WECHAT_PAY',
        balanceText: res.result.balance,
      })
    })
    //获取支付方式
    // util.showLoading('正在加载…')
    // var that = this
    // util
    //   .request(
    //     api.getPayMethodByParkId,
    //     {
    //       payBusiness: 'ARREARS',
    //     },
    //     'GET'
    //   )
    //   .then(function (res) {
    //     var list = res.result
    //     if (res.code == '0') {
    //       console.log('获取支付方式成功')
    //       that.setData({
    //         payList: list,
    //       })
    //       if (list.length > 0) {
    //         var payType = list[0]['payType']
    //         console.log('获取支付方式成功---默认支付类型' + payType)
    //         that.setData({
    //           selectPay: payType,
    //         })
    //         if (payType === 'BALANCE_PAY') {
    //           that.setData({
    //             balanceText: list[0]['iconUrl'], //用来对比余额
    //           })
    //         }
    //       }
    //       wx.hideLoading()
    //     } else {
    //       console.log('获取支付方式失败' + res.message)
    //       wx.hideLoading()
    //     }
    //   })
  },
  /* 当item被选择时 */
  changeCheckState(e) {
    var checkState, info
    var dataset = e.currentTarget.dataset
    let selectedList = this.data.selectedList
    info = dataset.info
    checkState = dataset.checkstate
    if (checkState == undefined) {
      checkState = false
    }

    if (!checkState) {
      checkState = true
      selectedList.push(info)
    } else {
      checkState = false
      selectedList = this.findDataInSelectedList(selectedList, info)
    }
    let invoiceAmout = this.calculateAmout(selectedList)
    console.log(invoiceAmout)

    this.setData({
      selectedList: selectedList,
      invoiceAmout: invoiceAmout,
    })
    var index = e.currentTarget.dataset.index
    this.setData({
      ['qflist[' + index + '].checkstate']: checkState,
    })

    // 根据选中金额自动切换支付方式
    this.autoSelectPayMethod(selectedList)
  },

  // 根据选中金额自动选择支付方式
  autoSelectPayMethod(selectedList) {
    // 获取实际金额数值（不带千分位逗号）
    let amountStr = this.calculateAmout(selectedList).replace(/,/g, '')
    let amount = parseFloat(amountStr) || 0
    let balance = parseFloat(this.data.balanceText) || 0

    // 如果余额大于等于补缴金额，选择余额支付，否则选择微信支付
    if (balance >= amount && amount > 0) {
      this.setData({
        selectPay: 'BALANCE_PAY',
      })
    } else if (amount > 0) {
      this.setData({
        selectPay: 'WECHAT_PAY',
      })
    }
  },

  findDataInSelectedList(list, data) {
    if (!list) {
      return list
    }
    list.splice(
      list.findIndex(item => item.id === data.id),
      1
    )
    return list
  },

  /* 根据list计算金额 */
  calculateAmout(list) {
    if (!list) {
      return '0.00'
    }
    let amount = list.reduce((value, item) => value + parseFloat(item['totalFee']), 0) //求和
    return (+amount).toFixed(2).replace(/^-?\d+/g, item => item.replace(/(?=(?!\b)(\d{3})+$)/g, ','))
  },

  /* 切换批量选择类型 全部 */
  batchSelected(e) {
    let type = e.currentTarget.dataset.type
    if (type == this.data.batchType) {
      type = ''
    }
    this.setData({
      batchType: type,
    })
    let list = this.data.qflist
    if (type == 'all') {
      var arry = []
      for (let i = 0; i < list.length; i++) {
        list[i]['checkstate'] = true
        arry.push(list[i])
      }
      let invoiceAmout = this.calculateAmout(list)
      this.setData({
        selectedList: arry,
        invoiceAmout: invoiceAmout,
      })

      // 批量选择后自动选择支付方式
      this.autoSelectPayMethod(arry)
    } else if (type == '') {
      for (let i = 0; i < list.length; i++) {
        list[i]['checkstate'] = false
      }
      this.setData({
        selectedList: [],
        invoiceAmout: 0,
      })

      // 清空选择后自动选择支付方式
      this.autoSelectPayMethod([])
    }
    this.setData({
      qflist: list,
    })
  },
  handlePayment() {
    const that = this
    util.showLoading('正在加载…')
    const ids = that.data.selectedList.map(item => item.arrearsId)

    util
      .request(
        api.addBatchPay,
        {
          ids,
          payType: that.data.selectPay,
        },
        'POST'
      )
      .then(res => {
        if (res.code === '0') {
          const infos = res.result
          that.setData({ tradeNo: infos.tradeNo })
          wx.hideLoading()

          if (infos.payStatus === 'PAID') {
            wx.redirectTo({
              url: `/pages/common/payState/index?type=lstcjfqfbj&id=${that.data.tradeNo}`,
            })
          } else {
            that.payWeiPay(infos.timestamp, infos.nonceStr, infos.packageVal, infos.paySign, infos.signType)
          }
        } else {
          wx.hideLoading()
          wx.showModal({
            title: '提示',
            content: res.message || '生成支付订单失败',
          })
        }
      })
      .finally(() => {
        wx.hideLoading()
      })
  },

  gotoInvoiceTitle() {
    const that = this

    if (that.data.selectedList.length < 1) {
      util.showToast('请勾选订单')
      return
    }

    if (that.data.selectPay === 'BALANCE_PAY') {
      wx.showModal({
        title: '确认支付',
        content: '确定支付吗？',
        success: res => {
          if (res.confirm) {
            that.handlePayment(that)
          }
        },
      })
    } else {
      that.handlePayment(that)
    }
  },
  payWeiPay: function (timeStamp, nonceStr, packageWord, paySign, signType) {
    var that = this
    wx.requestPayment({
      //吊起微信支付
      timeStamp: timeStamp,
      nonceStr: nonceStr,
      package: packageWord,
      signType: signType,
      paySign: paySign,
      success(res) {
        wx.hideLoading()
        console.log('支付成功')
      },
      fail(res) {
        wx.hideLoading()
        console.log('支付失败')
      },
      'complete': function (res) {
        console.log('支付完成')
        if (res.errMsg == 'requestPayment:ok') {
          wx.redirectTo({
            //直接跳转到成功界面
            url: '/pages/common/payState/index?type=lstcjfqfbj&id=' + that.data.tradeNo,
          })
        } else {
          wx.showModal({
            title: '提示',
            content: '支付失败',
          })
        }
        return
      },
    })
  },
  selectPayMode(e) {
    var currentTarget = e.currentTarget
    this.setData({
      selectPay: currentTarget.id,
    })
  },
})
