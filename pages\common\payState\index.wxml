<cu-custom bgColor="white-bg" contentTitle="停车缴费" isBack="{{type=='visitor'?false:true}}"></cu-custom>
<view class="infoBox">
  <view class="infoBox-t rowView">
    <image src="../../../image/yes-yhj.png" style="height: 72rpx; width: 72rpx;"></image>
    <view class="infoBox-t-r columnView">
      <view>支付成功</view>
      <view>实付：¥{{info.payMoney}}</view>
    </view>
  </view>
  <view class="infoBox-b rowView" hidden="{{type=='lstcjfqfbj'}}">
    <view class="infoBox-b-l">停车位置:</view>
    <view class="infoBox-b-r">{{info.parkingAddress}}</view>
  </view>
  <view class="infoBox-b rowView" hidden="{{type=='lstcjfqfbj'}}">
    <view class="infoBox-b-l">车牌号码:</view>
    <view class="infoBox-b-r">{{info.plateNo}}</view>
  </view>
  <view class="infoBox-b rowView"  hidden="{{type=='lstcjfqfbj'}}">
    <view class="infoBox-b-l">入场时间:</view>
    <view class="infoBox-b-r">{{info.enterTime === null ? '' : info.enterTime}}</view>
  </view>
 <view class="infoBox-b rowView" wx:if="{{type=='dfdd'}}" hidden="{{type=='lstcjfqfbj'}}">
    <view class="infoBox-b-l">离场时间:</view>
    <view class="infoBox-b-r">{{info.arrearsTime}}</view>
  </view>
  <view class="infoBox-b rowView" wx:if="{{type=='dfdd'||type=='tcjf'}}" hidden="{{type=='lstcjfqfbj'}}">
    <view class="infoBox-b-l">停车时长:</view>
    <view class="infoBox-b-r">{{info.parkPeriodTime}}</view>
  </view>
  <!-- <view class="infoBox-b rowView" wx:if="{{type=='tcjf'}}" hidden="{{type=='lstcjfqfbj'}}">
    <view class="infoBox-b-l">免费时长:</view>
    <view class="infoBox-b-r">{{info.freeTime === null ? '0' : info.freeTime}}</view>
  </view> -->
  <view class="infoBox-b rowView">
    <view class="infoBox-b-l">账单金额:</view>
    <view class="infoBox-b-r">¥{{info.totalCost}}</view>
  </view>
  <view class="infoBox-b rowView" wx:if="{{type=='tcjf'||type=='visitor'}}" hidden="{{type=='lstcjfqfbj'}}">
    <view class="infoBox-b-l">优惠金额:</view>
    <view class="infoBox-b-r">¥{{info.deductMoney}}</view>
  </view>
</view>
<view class='modalDlg-xyView' wx:if="{{type=='visitor'}}">
  <image class='yes_icon' wx:if="{{!allowXY}}" src="../../../image/yes-icon.png" bindtap='allow'></image>
  <image class='yes_icon' wx:if="{{allowXY}}" src="../../../image/yes-icon-select.png" bindtap='allow'></image>
  <text class='modalDlg-xyView-text'>将本次停车车辆添加为常用车辆</text>
</view>
<button class="convention_button" bindtap="goIndex" wx:if="{{type=='tcjf'}}">完成</button>
<button class="convention_button" bindtap="addCar" wx:elif="{{type=='visitor'}}">完成</button>
<button class="convention_button" bindtap="goBack" wx:else>返回</button>