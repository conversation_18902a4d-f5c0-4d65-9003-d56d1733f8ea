
let stateNames = [
  'EFFECTIVE','COMPLETED','EXPIRED','CANCELED'
]
function createParkingList(page){
  page = page + ''
  let list = []
  for(let i = 0; i< 10; i++){
    list.push({
      id: 'a' + page + i,
      parkingName: '某某停车场xx_' + page + i,
      address: '在哪里哪里xxx_' + i,
      time: '共13时33分',
      state: stateNames[i%4],
      money: '2.0' + i,
      plateNo:   '桂CLX667',
      entryTime: '2023-05-22-17:45',
      leaveTime: '2023-05-23-06:01',
      coupon: '15.00'

    })
  }
  return list;
}
let parkingList = createParkingList(1);
function createReservationList(){
  let list = []
  for(let i = 0; i< 6; i++){
    list.push({
      id: 'a' + i,
      parkingName: '预约xx_' + i,
      address: '在哪里哪里xxx_' + i,
      time: '共13时33分',
      state: stateNames[i%4],
      money: '2.0' + i,
      plateNo:   '桂CLX667',
      entryTime: '2023-05-22-17:45',
      leaveTime: '2023-05-23-06:01',
      coupon: '15.00'

    })
  }
  return list;
}
let reservationList = createReservationList();
function createrechargeList(){
  let list = []
  for(let i = 0; i< 8; i++){
    list.push({
      id: 'a' + i,
      phone: '18607712121',
      time: '2023.06.02 16:32',
      state: stateNames[i%4],
      money: '200.0' + i,
      channel: '微信',
      orderNum: '202315150141'+i

    })
  }
  return list;
}
let rechargeList = createrechargeList();
function createmonthlyList(){
  let list = []
  for(let i = 0; i< 5; i++){
    list.push({
      id: 'a' + i,
      parkingName: '包月x_' + i,
      address: '在哪里哪里xxx_' + i,
      time: '2023.06.02至2023.0902',
      state: stateNames[i%4],
      money: '2.0' + i,
      plateNo:   '桂CLX667',
      channel: '微信',
      orderNum: '202315150141'+i,
      deadlineTime: '2023-08-22',
      payTime: '2023-08-22'

    })
  }
  return list;
}
let monthlyList = createmonthlyList();
module.exports = {
  parkingList,
  reservationList,
  rechargeList,
  monthlyList,
  createParkingList
}