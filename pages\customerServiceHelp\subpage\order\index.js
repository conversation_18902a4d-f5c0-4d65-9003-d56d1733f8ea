var util = require('../../../../utils/util')
var api = require('../../../../config/api.js')
Page({
  data: {
    type: '',
    list: [],
    currentPage: 1,
    pageSize: 50,
    statusName: {
      EFFECTIVE: '生效中',
      COMPLETED: '已完成',
      EXPIRED: '已失效',
      CANCELED: '已取消',
    },
    statusClass: {
      EFFECTIVE: '',
      COMPLETED: 'blue',
      EXPIRED: 'grey',
      CANCELED: 'grey',
    },
    name: {
      monthlyConsultation: '包月',
      parkingAppointmentConsultation: '车场预约',
    },
  },
  onLoad(options) {
    this.setData({
      type: options.type,
    })
  },
  onShow() {
    var type = this.data.type

    if (type == 'monthlyConsultation') {
      //包期咨询==1||车场预约咨询==7
      this.getMonthlyConsultationData('MONTHLY_PACKAGE', 1)
    } else {
      this.getMonthlyConsultationData('RESERVATION_SPACE', 1)
    }
  },
  getMonthlyConsultationData: function (payBusiness, currentPage) {
    var that = this
    let params = {
      payBusiness: payBusiness,
      currentPage: currentPage,
      pageSize: that.data.pageSize,
      time: '',
    }
    util.showLoading('正在加载…')
    util
      .request(api.queryOrderList, params, 'GET')
      .then(function (res) {
        wx.hideLoading()
        let { code, result } = res || {}
        let records = that.data.list
        if (code == '0') {
          records = records.concat(result.records)
          that.setData({
            currentPage: currentPage + 1,
            list: records,
          })
        } else {
          util.showToast(res.message)
        }
      })
      .catch(err => {
        wx.hideLoading()
        util.showToast('服务异常，请稍后重试')
      })
  },
})
