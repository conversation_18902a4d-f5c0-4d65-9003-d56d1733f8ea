var util = require('../../../../../utils/util');
var api = require('../../../../../config/api.js');
Page({
  data: {
    carNum: '',
    id: '',
    frontFile_sl: api.imgUrl + 'drivingLicense.png',
    contraryFile_sl: api.imgUrl + 'drivingLicense_back.png',
    vehicleFile_sl: api.imgUrl + 'drivingLicense_car.png',
    info:{}
  },
  onLoad(options) {
    this.setData({
      carNum: options.carNum,
      id: options.id
    })
  },
  onShow() {
    this.getInfo();
  },
  getInfo: function () {
    var that = this;
    util.showLoading('正在加载...')
    util.request(api.vehicleAuthentication+"/"+ that.data.id,'', 'GET').then(function (res) {
      if (res.code == '0') {
        wx.hideLoading();
        that.setData({
          info:res.result
        });
      } else {
        wx.hideLoading();
        util.showToast(res.message)
      }
    })
  },
  goAddPage() {
    var data =  this.data;
    wx.navigateTo({
      url: '/pages/my/myCar/add/vehicleCertification/index?id=' + data.id + '&carNum=' + data.carNum + '&operate=modify',
      success: function(res) {
        // 通过eventChannel向被打开页面传送数据
        res.eventChannel.emit('acceptDataFromOpenerPage', data.info)
      }
    })
  }

})