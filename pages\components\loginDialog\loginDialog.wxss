/* pages/login/index.wxss */
/* 弹窗样式 */
@import "../common.wxss";

.modalDlg {
  position: fixed;
  top: 25%;
  left: 0;
  right: 0;
  z-index: 99999999;
  margin: 0 auto;
  padding: 55rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 560rpx;
  height: 720rpx;
  background: #FFFFFF;
  border-radius: 8rpx 8rpx 8rpx 8rpx;
  opacity: 1;
}

.windowRow {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 15rpx;
  width: 100%;
}

.modalDlg-title {
  width: 75rpx;
  height: 50rpx;
  font-size: 36rpx;
  font-weight: 400;
  color: #000000;
  line-height: 50rpx;
  text-align: center;
  margin-left: 40%;
}

.back {
  width: 25rpx;
  height: 25rpx;
  text-align: center;
  color: #f0a500;
  font-size: 30rpx;
  margin-bottom: 10%;
}

.modalDlg-title-t {
  width: 100%;
  height: 50rpx;
  font-size: 36rpx;
  font-weight: 400;
  color: #4768F3;
  line-height: 50rpx;
  margin-bottom: 20rpx;
  text-align: center;
}

.modalDlg-xyView {
  width: 100%;
  min-height: 100rpx;
  margin: 40rpx 0rpx;
  line-height: 50rpx;
}

.yes_icon {
  width: 30rpx;
  height: 30rpx;
  margin: 0 10rpx 0 10rpx;
}

.modalDlg-xyView-text {
  height: 30rpx;
  font-size: 30rpx;
  font-weight: 400;
  color: #353535;
}

.modalDlg-xyView .xy {
  color: #256BF5;
}

.wishbnt {
  text-align: center;
  width: 500rpx;
  height: 96rpx;
  border-radius: 10rpx 10rpx 10rpx 10rpx;
  opacity: 1;
  font-size: 36rpx;
  font-weight: 400;
  color: #FFFFFF;
  line-height: 96rpx;
  padding: 0;
  background: linear-gradient(90deg, #458BFF 0%, #08D7AE 100%);
  box-shadow: 0rpx 3rpx 10rpx 1rpx rgba(37, 107, 245, 0.2);
  border-radius: 48rpx 48rpx 48rpx 48rpx;
  opacity: 1;
}

/*弹窗样式结束*/