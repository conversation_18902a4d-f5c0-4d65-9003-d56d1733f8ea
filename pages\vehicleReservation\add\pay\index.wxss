@import '../../../common/common.wxss';
@import '../../../components/pay.wxss';

.backWihte-line {
  width: calc(100% - 80rpx);
  padding: 0 40rpx;
  font-size: 26rpx;
  line-height: 40rpx;
  font-weight: 400;
}

.backWihte-line-title {
  padding-top: 26rpx;
  padding-bottom: 26rpx;
  width: 200rpx;
  height: 37rpx;
  color: #353535;
}

.backWihte-line-value {
  padding-top: 26rpx;
  padding-bottom: 26rpx;
  flex: 1;
  color: #000000;
}

.paymentListBox {
  width: calc(100% - 40rpx);
}

.sucBox {
  width: 100%;
  height: 100hv;
}

.successStatus-icon {
  width: 94rpx;
  height: 94rpx;
  margin: 163rpx auto 30rpx auto;
}

.sucBox-text {
  text-align: center;
  height: 37rpx;
  font-size: 26rpx;
  font-weight: bold;
  color: #353535;
  line-height: 37rpx;
  margin-bottom: 76rpx;
}