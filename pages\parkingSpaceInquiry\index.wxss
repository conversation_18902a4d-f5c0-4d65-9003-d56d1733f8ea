/* pages/parkingSpaceInquiry/index.wxss */
@import '../common/common.wxss';
@import '../common/ccxqlistCss.wxss';

page {
  height: 100vh;
}

.mapDiv {
  width: 100%;
  height: 100%;
}

.mapDivShow {
  width: 100%;
  height: calc(100% - 10rpx);
}

/* 搜索b */
.view-search {
  position: absolute;
  top: 126rpx;
  left: 25rpx;
  width: calc(100% - 50rpx);
  min-height: 96rpx;
  line-height: 96rpx;
  opacity: 1;
  background: #FFFFFF;
  box-shadow: 0rpx 10rpx 0rpx 1rpx rgba(228, 234, 248, 0.16);
  border-radius: 20rpx 20rpx 20rpx 20rpx;
  opacity: 1;
}

.view-search-d {
  height: 71rpx;
  margin: 0 0 10rpx 20rpx;
  width: calc(100% - 20rpx);
}

.flex-wrp {
  display: flex;
}

.view-search-text {
  flex: 1;
  color: #888888;
}

.view-search-left {
  width: 54rpx;
  height: 500rpx;
  background: #FFFFFF;
  box-shadow: 0rpx 10rpx 20rpx 0rpx rgba(173, 190, 222, 0.5);
  border-radius: 37rpx;
  position: absolute;
  right: 30rpx;
  padding: 10rpx;
}

.view-search-left-btn {
  width: 54rpx;
  height: 67rpx;
  flex: 1;
}

.view-search-left-dw {
  position: absolute;
  right: 30rpx;
  width: 74rpx;
  height: 74rpx;
  background: #FFFFFF;
  box-shadow: 0rpx 10rpx 20rpx 0rpx rgba(173, 190, 222, 0.5);
  border-radius: 14rpx;
}

.flex-wrp {
  display: flex;
}

.view-search-select {
  margin: 7rpx 0;
}

.view-search-select.selected {
  background: #6799FF;
  border: 1rpx solid #6883F5;
  color: #FFFFFF;
}

/* 搜索e */

.listDiv {
  width: 100%;
  min-height: 278rpx;
  background: #F6F9FF;
  position: fixed;
  bottom: 0;
}

.moveBtn {
  width: 80rpx;
  height: 4rpx;
  background: #A3A3A3;
  border-radius: 100rpx 100rpx 100rpx 100rpx;
  margin: 20rpx auto;
}

.tool {
  width: calc(100% - 20rpx);
  padding: 30rpx 0 27rpx 0;
  height: 40rpx;
  display: flex;
  flex-direction: row;
}

.tool-tip {
  flex: 1;
  text-align: right;
  height: 54rpx;
  font-size: 28rpx;
  font-weight: 400;
  color: #353535;
  line-height: 54rpx;
  padding-right: 40rpx;
}

.tool-tip image {
  width: 28rpx;
  height: 41rpx;
}

.tool-px-xx {
  width: 100rpx;
  height: 220rpx;
  padding: 0 30rpx;
  background: #FFFFFF;
  box-shadow: 0rpx 0rpx 2rpx 1rpx rgba(0, 0, 0, 0.16);
  border-radius: 10rpx 10rpx 10rpx 10rpx;
  opacity: 1;
  display: flex;
  flex-direction: column;
  position: absolute;
  left: 20rpx;
  z-index: 1;
}

.tool-px-xx-d {
  width: 100%;
  height: 100%;
  font-size: 24rpx;

  font-weight: bold;
  color: #353535;
  line-height: 70rpx;
}

.tool-px {
  width: 130rpx;
  height: 54rpx;
  line-height: 30rpx;
  margin: 0 28rpx;
  display: flex;
  flex-direction: row;
}

.tool-px-t {
  width: 100rpx;
  height: 100%;
  padding-right: 10rpx;
  font-size: 24rpx;

  font-weight: bold;
  color: #353535;
  line-height: 54rpx;
  text-align: center;
}

.tool-px-icon.up {
  margin-top: 10rpx;
  border-color: transparent transparent #353535;
}

.tool-text {
  min-width: 228rpx;
  width: calc(100% - 366rpx);
  height: 54rpx;
  font-size: 24rpx;

  font-weight: 400;
  color: #353535;
  line-height: 54rpx;
  text-align: right;
}

.tool-btn {
  width: 176rpx;
  height: 48rpx;
  background: #FFFFFF;
  border-radius: 28rpx 28rpx 28rpx 28rpx;
  opacity: 1;
  font-size: 24rpx;

  font-weight: 400;
  color: #98ACFF;
  line-height: 48rpx;
  text-align: center;
  margin-left: 12rpx;
}

.scroll-view_H {
  height: 400rpx;
}

.scroll-view_H_Open {
  height: 860rpx;
}

view {
  box-sizing: inherit !important;
}

.page-section-gap {
  box-sizing: border-box;
  padding: 0 30rpx;
}

.page-body-button {
  margin-bottom: 30rpx;
}

.customCallout {
  height: 48rpx;
  line-height: 48rpx;
  background-color: #ffffff;
  border-radius: 12px;
  box-shadow: 0 0 3px 1px rgba(0, 0, 0, 0.2);
  align-items: center;
}

.icon {
  width: 40rpx;
  height: 40rpx;
  padding-left: 5rpx;
}

.content {
  font-size: 26rpx;
  font-weight: 400;
  color: #353535;
  text-align: center;
  line-height: 37rpx;
  height: 37rpx;
  min-width: 180rpx;
  border-radius: 12px;
  z-index: 1;
}


/* .customCallout::after {
  content: '';
  display: block;
  border: 7.5px solid #ffffff;
  width: 0;
  height: 0;
  position: absolute;
  left: calc(50% - 7.5px);
  filter: drop-shadow(0 1px 1px rgba(0, 0, 0, 0.2));
  top: calc(100% - 10.6px);
  transform:rotate(45deg);
  z-index: -1;
} */
.list-tool {
  margin: 0 50rpx 15rpx 50rpx;
  width: calc(100% - 100rpx);
  display: flex;
  flex-direction: row;
  font-size: 22rpx;
  font-weight: bold;
  color: #353535;
  text-align: center;
}

.list-tool .selected {
  min-width: 131rpx;
  height: 38rpx;
  background: #6799FF;
  color: #F1F8FF;
}

.list-tool-scroll {
  height: 80rpx;
  width: 100%;
  white-space: nowrap;
}

.list-tool-select {
  width: 131rpx;
  height: 38rpx;
  background: #F4F4F6;
  border-radius: 20rpx 20rpx 20rpx 20rpx;
  line-height: 38rpx;
  margin-right: 16rpx;
  margin-top: 5rpx;
  font-weight: 400;
  font-size: 24rpx;
  color: #888888;
}
.s-view-search>.Icon-search {
  margin: auto 10rpx auto 15rpx;
}
.s-view-search>.view-search-d  {
  border-bottom: 1rpx solid #DFDFDF;
  line-height: 71rpx;
}
.listDiv-d{
  margin-bottom: 15rpx;
}