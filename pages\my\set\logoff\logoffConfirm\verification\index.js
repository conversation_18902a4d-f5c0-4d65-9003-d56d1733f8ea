var util = require('../../../../../../utils/util');
var api = require('../../../../../../config/api.js');
var app = getApp();
Page({
  data: {
    showVerification:true,
    showSuccess:false,
    isClick:true,
  },
  iKnow(){
    wx.navigateBack({
      delta: 4
    })
  },  
  logoff(e){
    let that = this;
    util.showLoading('正在注销，请稍候')
    util.request(api.logoff, {
      "verificationId": e.detail.verificationId,
      "code": e.detail.code
    }, 'POST').then(function (res) {
      if (res.code == '0') {
        try {
          wx.setStorageSync('token', res.result.token)
        } catch (e) {
          console.log(e);
        }
        wx.hideLoading();
        that.setData({
          showVerification: false,
          isClick:false,
          showSuccess: true
        })
      } else {
        wx.hideLoading();
        util.showToast(res.message);
        that.setData({
          isClick:true
        })
      }
    })
  },
  back: function () {
    wx.switchTab({
      url: '/pages/my/my'
    })
  }
})