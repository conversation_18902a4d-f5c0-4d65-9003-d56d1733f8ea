@import '../components/recordDetail.wxss';
@import '../common/common.wxss';
@import '../components/pay.wxss';

.title-value-box-line-title {
  padding-left: 40rpx;
}

.title-value-box-line-title {
  padding-left: 40rpx;
}

.title-value-box-line-value {
  padding-right: 40rpx;
}

.paddingView-top {
  padding-top: 30rpx;
}

.cwxqzt {
  background: #FFFFFF;
  opacity: 1;
  width: 346rpx;
  height: 190rpx;
  border-radius: 20rpx;
  margin: 20rpx 0;
  margin-left: 20rpx;
  color: #A3A3A3;
  text-align: center;
  display: flex;
  justify-content: center;
}

.cwxqzt-img {
  width: 346rpx;
  height: 190rpx;
  border-radius: 20rpx;
}

.cwxqzt-text {
  margin: auto 0rpx;
  height: 25rpx;
  width: 100%;
  line-height: 25rpx;
  font-size: 26rpx;
}
.xny{
  color: #2fb34c;
  background-color: #d8fadb;
  border: 1rpx solid #2fb34c;
  border-radius: 10rpx;
  padding: 0 10rpx;
  margin-right: 10rpx;
}
.lj{
  text-decoration: line-through;
  margin-right: 10rpx;
}
.zh{
  font-weight: bold;
  color: #141111;
}
.box_convention_button{
  padding: 20rpx 0 0 0;
}