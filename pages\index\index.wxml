<!--index.wxml-->
<view class="saveOutView">
  <!-- <cu-custom bgColor="default-bg" contentTitle="畅行桂林"></cu-custom> -->
  <image
    src="{{imgUrl}}/sy/img_bg.png"
    mode="widthFix"
    class="navigationPng"
    style="width: 100%; height: 747rpx"
    mode="scaleToFill"
  ></image>

  <scroll-view scroll-y="true" class="scrollViewSa" bindscrolltolower="bindReachBottom">
    <view class="swiper">
      <swiper
        indicator-dots="{{indicatorDots}}"
        autoplay="{{autoplay}}"
        interval="{{interval}}"
        duration="{{duration}}"
        indicator-color="#D2D2D2"
        indicator-active-color="#fff"
      >
        <block wx:for="{{background}}" wx:key="index">
          <swiper-item data-item="{{item}}" bind:tap="onSwiperTap">
            <view class="swiper-item">
              <image src="{{item.fileUrl}}" style="position: absolute; width: 100%; height: 100%"></image>
            </view>
          </swiper-item>
        </block>
      </swiper>
    </view>

    <view class="float-box">
      <view class="center-two">
        <view wx:if="{{carList.length<=0||showPay}}" class="columnView">
          <view class="rowView">
            <view class="center-two-up-left wdl"> 请输入车牌号 </view>
            <view class="center-two-up-right">
              <view wx:if="{{loginStatus=='TEMP_LOGIN'}}" class="convention_bottom_btn_medium" catch:tap="goLoginPage">
                登录/注册
              </view>
              <view wx:if="{{carList.length}}" class="center-right-bottom">
                <view
                  class="qf-listDiv-right-three btn-pay"
                  style="margin-top: 10rpx; height: 44rpx; line-height: 44rpx"
                  catch:tap="setPayShow"
                >
                  当前车辆
                </view>
                <image
                  src="/image/button_banding.png"
                  mode="scaleToFill"
                  style="width: 155rpx; height: 54rpx; margin-top: 10rpx"
                  catch:tap="goCarPage"
                ></image>
              </view>
            </view>
          </view>
          <view style="overflow: hidden; clear: both; height: 100%" class="palet">
            <view class="con-query">
              <view class="plate-input-body">
                <view class="plate-input-content">
                  <view class="{{inputOnFocusIndex=='0'?'plate-nums-foc':'plate-nums-first'}}">
                    <text catchtap="inputClick_licensePlate" class="plate-num-text" data-id="0"
                      >{{inputPlates.index0}}</text
                    >
                  </view>
                  <view class="{{inputOnFocusIndex=='1'?'plate-nums-foc':'plate-nums-first'}}">
                    <text catchtap="inputClick_licensePlate" class="plate-num-text" data-id="1"
                      >{{inputPlates.index1}}</text
                    >
                  </view>
                  <view class="{{inputOnFocusIndex=='2'?'plate-nums-foc':'plate-nums-first'}}">
                    <text catchtap="inputClick_licensePlate" class="plate-num-text" data-id="2"
                      >{{inputPlates.index2}}</text
                    >
                  </view>
                  <view class="{{inputOnFocusIndex=='3'?'plate-nums-foc':'plate-nums-first'}}">
                    <text catchtap="inputClick_licensePlate" class="plate-num-text" data-id="3"
                      >{{inputPlates.index3}}</text
                    >
                  </view>
                  <view class="{{inputOnFocusIndex=='4'?'plate-nums-foc':'plate-nums-first'}}">
                    <text catchtap="inputClick_licensePlate" class="plate-num-text" data-id="4"
                      >{{inputPlates.index4}}</text
                    >
                  </view>
                  <view class="{{inputOnFocusIndex=='5'?'plate-nums-foc':'plate-nums-first'}}">
                    <text catchtap="inputClick_licensePlate" class="plate-num-text" data-id="5"
                      >{{inputPlates.index5}}</text
                    >
                  </view>
                  <view class="{{inputOnFocusIndex=='6'?'plate-nums-foc':'plate-nums-first'}}">
                    <text catchtap="inputClick_licensePlate" class="plate-num-text" data-id="6"
                      >{{inputPlates.index6}}</text
                    >
                  </view>
                  <view class="{{inputOnFocusIndex=='7'?'plate-nums-foc':'plate-nums-first'}}">
                    <text
                      wx:if="{{!showNewPower}}"
                      catchtap="inputClick_licensePlate"
                      class="plate-num-text"
                      data-id="7"
                      >{{inputPlates.index7}}</text
                    >
                    <text
                      wx:if="{{showNewPower}}"
                      catchtap="inputClick_licensePlate"
                      class="plate-num-text"
                      style="color: #c0c0c0"
                      data-id="7"
                      >新</text
                    >
                  </view>
                </view>
              </view>
              <view class="dx">
                <image
                  class="yes_icon"
                  wx:if="{{!allowXY}}"
                  src="../../image/yes-icon.png"
                  bindtap="bindChange"
                ></image>
                <image
                  class="yes_icon"
                  wx:if="{{allowXY}}"
                  src="../../image/yes-icon-select.png"
                  bindtap="bindChange"
                ></image>
                <view class="text">黄牌车</view>
              </view>
              <button class="convention_button {{isClick?'':'noAllowClick'}}" bindtap="goFindPayMoneyPage">
                输入车牌缴费
              </button>
            </view>
            <view class="keyboard" wx:if="{{isKeyboard}}">
              <view class="kb_top">
                <text catchtap="tapSpecBtna" data-index="1" class="complete">完成</text>
              </view>
              <view style="width: 100%; text-align: center" wx:if="{{isNumberKB}}">
                <view style="width: 99%; display: flex; text-align: center; margin: 0 auto">
                  <view
                    catchtap="tapKeyboard"
                    class="td td_nor"
                    data-index="{{idx}}"
                    data-val="{{itemName}}"
                    hoverClass="board_bg"
                    hoverStartTime="0"
                    hoverStayTime="80"
                    wx:if="{{idx<=9}}"
                    wx:for="{{keyboard1}}"
                    wx:for-index="idx"
                    wx:for-item="itemName"
                    wx:key="itemName"
                  >
                    {{itemName}}
                  </view>
                </view>
                <view style="display: flex; text-align: center; width: 90%; margin: 0 auto">
                  <view
                    catchtap="tapKeyboard"
                    class="td td_nor"
                    data-index="{{idx}}"
                    data-val="{{itemName}}"
                    hoverClass="board_bg"
                    hoverStartTime="0"
                    hoverStayTime="80"
                    wx:if="{{idx<=18&&idx>9}}"
                    wx:for="{{keyboard1}}"
                    wx:for-index="idx"
                    wx:for-item="itemName"
                    wx:key="itemName"
                  >
                    {{itemName}}
                  </view>
                </view>
                <view style="display: flex; text-align: center; width: 70%; margin: 0 auto">
                  <view
                    catchtap="tapKeyboard"
                    class="td td_nor"
                    data-index="{{idx}}"
                    data-val="{{itemName}}"
                    hoverClass="board_bg"
                    hoverStartTime="0"
                    hoverStayTime="80"
                    wx:if="{{idx<=25&&idx>18}}"
                    wx:for="{{keyboard1}}"
                    wx:for-index="idx"
                    wx:for-item="itemName"
                    wx:key="itemName"
                  >
                    {{itemName}}
                  </view>
                </view>
                <view style="display: flex; width: 50%; margin: 0 auto; text-align: center">
                  <view
                    catchtap="tapKeyboard"
                    class="td td_nor"
                    data-index="{{idx}}"
                    data-val="{{itemName}}"
                    hoverClass="board_bg"
                    hoverStartTime="0"
                    hoverStayTime="80"
                    wx:if="{{idx>25}}"
                    wx:for="{{keyboard1}}"
                    wx:for-index="idx"
                    wx:for-item="itemName"
                    wx:key="itemName"
                  >
                    {{itemName}}
                  </view>
                </view>
                <view
                  bindtap="tapSpecBtn"
                  class="del-first"
                  data-index="0"
                  hoverClass="del-hover"
                  hoverStartTime="0"
                  hoverStayTime="80"
                >
                  <text>删除</text>
                </view>
              </view>
              <view style="width: 100%; text-align: center" wx:if="{{!isNumberKB}}">
                <view style="width: 99%; display: flex; text-align: center; margin: 0 auto">
                  <view
                    class="td td_num board_bg"
                    wx:if="{{!tapNum&&idx<=9}}"
                    wx:for="{{keyboardNumber}}"
                    wx:for-index="idx"
                    wx:for-item="itemName"
                    wx:key="itemName"
                  >
                    {{itemName}}
                  </view>
                </view>
                <view style="width: 99%; display: flex; text-align: center; margin: 0 auto">
                  <view
                    catchtap="tapKeyboard"
                    class="td td_num"
                    data-index="{{idx}}"
                    data-val="{{itemName}}"
                    hoverClass="board_bg"
                    hoverStartTime="0"
                    hoverStayTime="80"
                    wx:if="{{tapNum&&idx<=9}}"
                    wx:for="{{keyboardNumber}}"
                    wx:for-index="idx"
                    wx:for-item="itemName"
                    wx:key="itemName"
                  >
                    {{itemName}}
                  </view>
                </view>
                <view style="width: 99%; display: flex; text-align: center; margin: 0 auto">
                  <view
                    catchtap="tapKeyboard"
                    class="td td_num"
                    data-index="{{idx}}"
                    data-val="{{itemName}}"
                    hoverClass="board_bg"
                    hoverStartTime="0"
                    hoverStayTime="80"
                    wx:if="{{idx>9&&idx<=17}}"
                    wx:for="{{keyboardNumber}}"
                    wx:for-index="idx"
                    wx:for-item="itemName"
                    wx:key="itemName"
                  >
                    {{itemName}}
                  </view>
                  <view
                    catchtap="tapKeyboard"
                    class="td td_num"
                    data-index="{{idx}}"
                    data-val="{{itemName}}"
                    hoverClass="board_bg"
                    hoverStartTime="0"
                    hoverStayTime="80"
                    wx:if="{{tapNum&&18<=idx&&idx<=19}}"
                    wx:for="{{keyboardNumber}}"
                    wx:for-index="idx"
                    wx:for-item="itemName"
                    wx:key="itemName"
                  >
                    {{itemName}}
                  </view>
                  <view
                    class="td td_num board_bg"
                    wx:if="{{!tapNum&&18<=idx&&idx<=19}}"
                    wx:for="{{keyboardNumber}}"
                    wx:for-index="idx"
                    wx:for-item="itemName"
                    wx:key="itemName"
                  >
                    {{itemName}}
                  </view>
                </view>
                <view style="width: 99%; display: flex; text-align: center; margin: 0 auto">
                  <view
                    catchtap="tapKeyboard"
                    class="td td_num"
                    data-index="{{idx}}"
                    data-val="{{itemName}}"
                    hoverClass="board_bg"
                    hoverStartTime="0"
                    hoverStayTime="80"
                    wx:if="{{idx>19&&idx<=28}}"
                    wx:for="{{keyboardNumber}}"
                    wx:for-index="idx"
                    wx:for-item="itemName"
                    wx:key="itemName"
                  >
                    {{itemName}}
                  </view>
                  <view
                    catchtap="tapKeyboard"
                    class="td td_num"
                    data-index="{{idx}}"
                    data-val="{{itemName}}"
                    hoverClass="board_bg"
                    hoverStartTime="0"
                    hoverStayTime="80"
                    wx:if="{{tapNum&&29==idx}}"
                    wx:for="{{keyboardNumber}}"
                    wx:for-index="idx"
                    wx:for-item="itemName"
                    wx:key="itemName"
                  >
                    {{itemName}}
                  </view>
                  <view
                    class="td td_num board_bg"
                    wx:if="{{!tapNum&&29==idx}}"
                    wx:for="{{keyboardNumber}}"
                    wx:for-index="idx"
                    wx:for-item="itemName"
                    wx:key="itemName"
                  >
                    {{itemName}}
                  </view>
                </view>
                <view style="width: 69%; display: flex; text-align: left; margin-left: 5rpx">
                  <view
                    catchtap="tapKeyboard"
                    class="td td_num"
                    data-index="{{idx}}"
                    data-val="{{itemName}}"
                    hoverClass="board_bg"
                    hoverStartTime="0"
                    hoverStayTime="80"
                    wx:if="{{idx>29}}"
                    wx:for="{{keyboardNumber}}"
                    wx:for-index="idx"
                    wx:for-item="itemName"
                    wx:key="itemName"
                  >
                    {{itemName}}
                  </view>
                </view>
                <view
                  bindtap="tapSpecBtn"
                  class="del-first"
                  data-index="0"
                  hoverClass="del-hover"
                  hoverStartTime="0"
                  hoverStayTime="80"
                >
                  <text>删除</text>
                </view>
              </view>
            </view>
          </view>
        </view>

        <view class="columnView" wx:else>
          <view class="center-two-up">
            <picker
              bindchange="bindPickerChange"
              value="{{index}}"
              range="{{carList}}"
              range-key="plateNo"
              mode="selector"
            >
              <view class="center-two-up-left haveCar"> {{carList[index]["plateNo"]}} </view>
            </picker>
            <view class="center-two-up-right">
              <view class="center-two-up-right-text">
                {{isParking?'正在停车': currentCost <= 0 ? '未查询到正在停车订单' :'当前停车费 '+currentCost+' 元'}}
              </view>
              <view class="center-right-bottom">
                <view class="qf-listDiv-right-three btn-pay" catch:tap="setPayShow">车牌缴费</view>
                <view
                  wx:if="{{isParking}}"
                  style="margin-left: 10rpx"
                  class="qf-listDiv-right-three"
                  catch:tap="viewParking"
                  >查看</view
                >
                <image
                  wx:else
                  src="/image/button_{{ currentCost <= 0 ? 'lijibujiao':'zhifu'}}.png"
                  mode="scaleToFill"
                  class="btn-zhifu"
                  bind:tap="payment"
                ></image>
              </view>
            </view>
          </view>
        </view>

        <view class="center-one">
          <block wx:for="{{moduleList}}" wx:key="index">
            <view id="{{item.imgName}}" class="center-one-v" bindtap="goToPage" data-route="{{item.route}}">
              <view class="center-one-v-img">
                <image src="{{imgUrl}}/sy/{{item.imgName}}.png" style="border-radius: 30rpx"></image>
                <view class="cu-tag badge" wx:if="{{numData[item.imgName]>0}}"
                  >{{numData[item.imgName]>99?'99+':numData[item.imgName]}}</view
                >
              </view>
              <view class="center-one-v-text"> {{item.name}}</view>
            </view>
          </block>
        </view>
      </view>

      <view class="common-title backgroundImg">附近车场</view>
      <view class="list-tool">
        <view id="DISCOUNT" class="list-tool-select {{orderBy == 'DISCOUNT' ? 'selected' : ''}} " bindtap="selectItem"
          >优惠车场</view
        >
        <view id="DISTANCE" class="list-tool-select {{orderBy == 'DISTANCE' ? 'selected' : ''}} " bindtap="selectItem"
          >距离最近</view
        >
        <view id="SURPLUS" class="list-tool-select {{orderBy == 'SURPLUS' ? 'selected' : ''}} " bindtap="selectItem"
          >余位最多</view
        >

        <image
          src="/image/goMap.png"
          mode="scaleToFill"
          style="width: 155rpx; height: 54rpx"
          id="parkingSpaceInquiry"
          data-route="/pages/parkingSpaceInquiry/index?isV=false"
          bindtap="goToPage"
        ></image>
      </view>

      <block wx:for="{{pslist}}" wx:key="index">
        <view class="listDiv-d" bindtap="goxq" id="{{item.id}}" data-type="{{item.type}}">
          <view class="listDiv-d-left">
            <view class="rowView">
              <view class="listDiv-search-name">{{item.name}}</view>
              <view class="listDiv-search-dc-by" wx:if="{{item.monthly}}">可包月</view>
            </view>
            <view class="listDiv-search-dc">
              <view class="listDiv-search-yw margin10" wx:if="{{item.dataSource=='HIK'}}">余位 {{item.surplus}}/</view>
              <view class="listDiv-search-dc-zw margin10" wx:if="{{item.dataSource=='HIK'}}">{{item.total}}</view>
              <view class="listDiv-search-dc-tip margin10" wx:if="{{item.freeTimeDesc!=''}}">
                {{item.freeTimeDesc}}
              </view>
              <view class="listDiv-search-dc-yhj margin10" catchtap="goxq" wx:if="{{item.discount}}">优惠券可用</view>
              <view wx:if="{{item.isHolidayFree}}" class="holiday-free-tag margin10"> {{item.holidayDesc}} </view>
            </view>
          </view>
          <view class="listDiv-d-right">
            <view
              class="listDiv-d-dhBtn"
              catchtap="openMapApp"
              data-lat="{{item.latitude}}"
              data-long="{{item.longitude}}"
              data-name="{{item.name}}"
              data-address="{{item.address}}"
            >
              <image src="../../image/dh.png"></image>
              <text>导航</text>
            </view>
            <view class="listDiv-search-dw">{{item.distance}}km</view>
          </view>
        </view>
      </block>
      <view wx:if="{{showNullTip}}" class="njgtip">附近没有停车场</view>
      <view wx:if="{{showNullMoreTip}}" class="njgtip">没有更多信息了</view>
    </view>
  </scroll-view>

  <view wx:if="{{ad&&ad.fileUrl}}" class="ad-container" hidden="{{hiddenAd}}" catchtouchmove="preventMove">
    <view class="ad-bg"></view>
    <view class="ad" style="padding-top:{{CustomBar}}px">
      <image class="ad-img" src="{{ad.fileUrl}}" mode="widthFix" bind:tap="onAdClick"></image>
      <view style="width: 100%" bind:tap="onAdCanceel">
        <view class="glicon-guanbi"></view>
      </view>
    </view>
  </view>
</view>

<twoBtnDialog
  showModal="{{showAddCarTip}}"
  title="提示"
  leftBtnText="取消"
  rightBtnText="去添加"
  tipText="请添加车牌号"
  bind:rightGo="getRightGo"
  bind:leftGo="getLeftGo"
></twoBtnDialog>

<addTempCar isShow="{{showAddTempCar}}" bind:addTempCar="queryOrders" bind:refresh="refresh"></addTempCar>

<!-- 盛才AI助手悬浮按钮 -->
<!-- <view class="ai-float-btn" catchtap="openAIDialog">
  <image src="/image/logo.png" class="ai-float-logo" />
</view>
<shengcaiAI show="{{showAIDialog}}" bind:close="closeAIDialog" /> -->
