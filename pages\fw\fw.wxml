<!--pages/fw/fw.wxml-->
<view class="saveOutView">
  <cu-custom bgColor="default-bg" contentTitle="服务"></cu-custom>
  <image
    src="{{imgUrl}}/sy/img_bg_2.png"
    mode="widthFix"
    class="navigationPng"
    style="width: 100%; height: 727rpx"
  ></image>
  <view class="swiper" style="margin-top: 66rpx">
    <swiper indicator-dots="{{indicatorDots}}" autoplay="{{autoplay}}" interval="{{interval}}" duration="{{duration}}">
      <block wx:for="{{background}}" wx:key="index">
        <swiper-item data-item="{{item}}" bind:tap="onSwiperTap">
          <view class="swiper-item">
            <image
              src="{{item.fileUrl}}"
              style="position: absolute; width: 100%; height: 100%; border-radius: 30rpx"
            ></image>
          </view>
        </swiper-item>
      </block>
    </swiper>
  </view>

  <view class="common-title">便捷服务</view>
  <view class="center-one">
    <view
      class="center-one-v"
      bindtap="goToPage"
      data-route="/pages/parkingSpaceInquiry/index?isV=false"
      data-type="parkingSpaceInquiry"
      data-check="false"
    >
      <view class="center-one-v-img">
        <image src="{{imgUrl}}/sy/button_cheweichaxun.png"></image>
      </view>
      <view class="center-one-v-text"> 找停车场</view>
    </view>
    <!-- <view class="center-one-v " bindtap="goToPage" data-route="/pages/parkingSpaceInquiry/index?isV=true&type=reservation" data-type="" data-check="false">
      <view class="center-one-v-img">
        <image src="{{imgUrl}}/sy/button_cheweiyuding.png"></image>
      </view>
      <view class="center-one-v-text"> 车位预订</view>
    </view> -->
    <view
      class="center-one-v"
      bindtap="goToPage"
      data-route="/pages/orderQuery/index"
      data-type=""
      data-check="true"
      data-tiptype="1"
    >
      <view class="center-one-v-img">
        <image src="{{imgUrl}}/sy/button_dingdanchaxun.png"></image>
      </view>
      <view class="center-one-v-text"> 订单查询</view>
    </view>
    <view
      class="center-one-v"
      bindtap="goToPage"
      data-route="/pages/parkingSpaceInquiry/fwMap/index"
      data-type="TOILET"
      data-check="false"
    >
      <view class="center-one-v-img">
        <image src="{{imgUrl}}/sy/button_wc.png"></image>
      </view>
      <view class="center-one-v-text"> 找公厕</view>
    </view>
    <view
      class="center-one-v"
      bindtap="goToPage"
      data-route="/pages/parkingSpaceInquiry/fwMap/index"
      data-type="CHARGE"
      data-check="false"
    >
      <view class="center-one-v-img">
        <image src="{{imgUrl}}/sy/button_chogndianzhaung.png"></image>
      </view>
      <view class="center-one-v-text"> 找充电桩</view>
    </view>
    <view
      class="center-one-v"
      bindtap="goToPage"
      data-route="/pages/parkingSpaceInquiry/fwMap/index"
      data-type="FILLING_STATION"
      data-check="false"
    >
      <view class="center-one-v-img">
        <image src="{{imgUrl}}/sy/button_jiayouzhan.png"></image>
      </view>
      <view class="center-one-v-text"> 找加油站</view>
    </view>
    <view
      class="center-one-v"
      bindtap="goToPage"
      data-route="/pages/parkingSpaceInquiry/fwMap/index"
      data-type="SCENIC_AREA"
      data-check="false"
    >
      <view class="center-one-v-img">
        <image src="{{imgUrl}}/sy/buttoin_attractions.png"></image>
      </view>
      <view class="center-one-v-text">找景点</view>
    </view>
    <view
      class="center-one-v"
      bindtap="goToPage"
      data-route="/pages/monthlyPackage/add/find/index?isFind=false"
      data-type=""
      data-check="false"
    >
      <view class="center-one-v-img">
        <image src="{{imgUrl}}/sy/icon_yuekadingdan.png"></image>
      </view>
      <view class="center-one-v-text"> 车场包月</view>
    </view>
    <view
      class="center-one-v"
      id="recharge"
      bindtap="goToPage"
      data-route="/pages/recharge/index"
      data-type="recharge"
      data-check="true"
      data-tiptype="3"
    >
      <view class="center-one-v-img">
        <image src="{{imgUrl}}/sy/button_xianshangchongzhi.png"></image>
      </view>
      <view class="center-one-v-text"> 线上充值</view>
    </view>
    <view
      class="center-one-v"
      bindtap="goToPage"
      data-route="/pages/customerServiceHelp/index"
      data-type=""
      data-check="false"
    >
      <view class="center-one-v-img">
        <image src="{{imgUrl}}/sy/button_kefu.png"></image>
      </view>
      <view class="center-one-v-text"> 客服帮助</view>
    </view>
  </view>
</view>
<loginDialog showModal="{{showLoginDialog}}" bind:refresh="goPage"></loginDialog>
