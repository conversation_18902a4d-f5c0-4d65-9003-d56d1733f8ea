@import '../../components/recordDetail.wxss';
@import '../../common/common.wxss';
@import '../index.wxss';

.details-box-title-value {
  color: #4768F3;
}

.title-value-box-btn {
  color: #4768F3;
  background: #F1F8FF;
  border: 1rpx solid #98ACFF;
}

.details-box-title-time {
  flex: 1;
  height: 48rpx;
  font-size: 34rpx;

  font-weight: bold;
  color: #353535;
  line-height: 54rpx;
}

.details-box-title {
  padding: 80rpx 60rpx 0 60rpx;
}

.info-bt-text {
  flex: 1;
  height: 45rpx;
  font-size: 28rpx;
  font-weight: bold;
  color: #353535;
  line-height: 54rpx;
  margin-bottom: 10rpx;
}

.details-box.p {
  padding: 30rpx;
  width: calc(100% - 100rpx);
  min-height: 50rpx;
  margin-bottom: 30rpx;
}

.details-box .stateBox {
  margin: 10rpx;
}

.details-box>.t {
  min-height: 56rpx;
  font-size: 34rpx;
  font-weight: bold;
  color: #353535;
  line-height: 56rpx;
}

.details-box>.pl {
  padding-left: 10rpx;
}

.details-box>.n {
  height: 56rpx;
  font-size: 34rpx;
  font-weight: 400;
  color: #353535;
  line-height: 56rpx;
}

.details-box .z {
  width: 150rpx;
}

.details-box .ft {
  height: 54rpx;
  font-size: 28rpx;
  font-weight: 400;
  color: #7E7E7E;
  line-height: 54rpx;
  text-align: left;
}

.details-box .green {
  color: #41E0AC;
}

.details-box .red {
  color: #DA5937;
}

.details-box .black {
  color: #353535;
}
.details-box .flex1{
  flex: 1;
  text-align: right;
}
.line{
  border-top: 1rpx solid #DFDFDF;
  margin-top: 10rpx ;
  padding-top: 20rpx;
}
.nullImg{
  margin: 60rpx 150rpx  10rpx 150rpx;
}