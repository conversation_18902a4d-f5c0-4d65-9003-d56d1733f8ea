.index-hd {
  padding: 40px 40px 20px;
  text-align: center;
}
.index-bd {
  /* padding: 0 15px 20px; */
  padding: 0 15px 20px;
}
.index-ft {
  padding-bottom: 10px;
  text-align: center;
}
.index-logo {
  /* width: 43px;
  height: 43px; */
  width: 43px;
  height: 43px;
}
.index-desc {
  margin-top: 10px;
  color: var(--weui-FG-1);
  /* font-size: 14px; */
  font-size: 14px;
}


.navigator-box {
  opacity: 0;
  position: relative;
  background-color: var(--weui-BG-2);
  line-height: 1.41176471;
  /* font-size: 32px; */
  font-size: 17px;

  transform: translateY(-50%);
  transition: .3s;
}
.navigator-box-show {
  opacity: 1;
  transform: translateY(0);
}
.navigator {
  position: relative;
  display: flex;
  align-items: center;
  background: #FFFFFF;
  height: 90rpx;
  line-height: 90rpx;
  padding: 0  30rpx;
}
.navigator:before {
  content: " ";
  position: absolute;
  /* left: 15px; */
  left: 15px;
  top: 0;
  /* right: 15px; */
  right: 15px;
  height: 1px;
  /* border-top: 1px solid var(--weui-FG-3); */
  border-top: 1px solid var(--weui-FG-3);
  color: var(--weui-FG-3);
}
.navigator:first-child:before {
  display: none;
}
.navigator-text {
  flex: 1;
}
.navigator-arrow {
  padding-right: 13px;
  position: relative;
}
.navigator-arrow:after {
  content: " ";
  position: absolute;
  border-color: #353535;
  height: 15rpx;
  width: 15rpx;
  border-width: 4rpx 4rpx 0 0;
  border-style: solid;
  transform: matrix(0.71, 0.71, -0.71, 0.71, 0, 0);
  border-top-width: 2px;
  border-right-width: 2px;
  border-bottom-width: 0px;
  border-left-width: 0px;
  border-top-style: solid;
  border-right-style: solid;
  border-bottom-style: solid;
  border-left-style: solid;
}


.kind-list-item {
  /* margin: 10px 0; */
  margin: 10px 0;
  background-color: var(--weui-BG-2);
  border-radius: 2px;
  overflow: hidden;
}
.kind-list-item:first-child {
  margin-top: 0;
}
.kind-list-text{
  flex: 1;
}
.kind-list-img {
  /* width: 30px;
  height: 30px; */
  width: 30px;
  height: 30px;
}
.kind-list-item-hd {
  /* padding: 15px; */
  padding: 15px;
  display: flex;
  align-items: center;
  transition: opacity .3s;
}
.kind-list-item-hd-show {
  opacity: .2;
}
.kind-list-item-bd {
  height: 0;
  overflow: hidden;
}
.kind-list-item-bd-show {
  height: auto;
}