const api = require('../../config/api')
var util = require('../../utils/util')
var commonApi = require('../../utils/commonApi.js')

// pages/fw/fw.js
var app = getApp()
Page({
  data: {
    goPageRoute_login: '', //登陆后跳转的URL
    imgUrl: api.imgUrl,
    // 广告栏配置 s
    background: [],
    indicatorDots: true,
    vertical: false,
    autoplay: true,
    interval: 5000,
    duration: 500,
    goPageRoute: '',
    showLoginDialog: false,
  },
  onShow: function () {
    this.getAdvert()
  },
  goToPage: function (e) {
    var currentTarget = e.currentTarget
    var that = this
    var dataset = currentTarget.dataset
    var route = dataset.route
    var check = dataset.check
    var type = dataset.type
    route = route + '?type=' + type
    if (check != 'false' && app.globalData.userInfo.loginStatus != 'LOGGED_IN') {
      var tiptype = dataset.tiptype
      console.log('登陆跳转设置的route' + route)
      if (type == 'recharge') {
        //充值的接口带有参数所以不通过直接传跳转的URL来实现登陆后跳转
        that.setData({
          goPageRoute_login: route,
        })
        wx.navigateTo({
          url: '/pages/common/loginTip/loginTip?tiptype=' + tiptype,
        })
      } else {
        wx.navigateTo({
          url: '/pages/common/loginTip/loginTip?tiptype=' + tiptype + '&&route=' + route,
        })
      }
    } else {
      that.toPageL(route)
    }
  },
  goPageMethod: function () {
    //登陆成功后跳转到点击登陆前选择的页面。注意方法名不能修改
    wx.redirectTo({
      url: this.data.goPageRoute_login,
    })
  },
  toPageL: function (route) {
    wx.navigateTo({
      url: route,
    })
  },
  goPage: function () {
    wx.navigateTo({
      url: this.toPageL(this.data.goPageRoute),
    })
  },
  async getAdvert() {
    try {
      const { data, id } = await commonApi.fetchAd(6)
      this.setData({
        background: data || [],
      })
      this.swiperId = id
    } catch (error) {
      console.log(error)
    }
  },
  onSwiperTap(e) {
    const { item } = e.currentTarget.dataset
    commonApi.handleAdClick(item, this.swiperId)
  },
})
