<!--pages/monthlyPackage/add/index.wxml-->
<cu-custom bgColor="white-bg" contentTitle="我的月卡" isBack="true"></cu-custom>
<view class="saveOutView backgroundView" style="background-image: url('{{imgUrl}}by-top.png');">
  <view class="topTitle rowView">
    <image src="../../image/vip.png" style="width: 48rpx; height: 48rpx; margin-right: 10rpx; margin-top: 15rpx"></image
    >月卡尊享用户
  </view>
  <view class="rowView">
    <view class="topTitle-fu">更优惠，更便捷</view>
    <button class="add" bindtap="addCard">购买月卡</button>
  </view>
  <view class="circle-i-orange" catchtap="showApplication">月卡注意事项</view>
  <scroll-view scroll-y="true" style="height: 1rpx; flex: 1; background: #ffffff">
    <block wx:for="{{list}}" wx:key="index" scroll-y="true">
      <view class="carListInfoBox columnView">
        <text class="carListInfo-one">{{item.parkName}}</text>
        <text class="carListInfo-two">{{item.plateNo}}</text>
        <text class="carListInfo-two">到期时间{{item.endTime}}</text>
        <view class="rowView">
          <view class="stateBox a{{item.bagState}}">{{item.bagStateText}}</view>
          <view class="stateBox w" wx:if="{{item.bagMyself==0}}">非本人办理</view>
          <view
            class="btn w {{item.bagMyself==1?'f':''}}"
            bindtap="goAddPage"
            data-info="{{item}}"
            data-isr="{{true}}"
            wx:if="{{item.renewable==1}}"
            >立即续费</view
          >
          <view
            class="btn w {{item.bagMyself==1?'f':''}}"
            bindtap="goAddPage"
            data-info="{{item}}"
            data-isr="{{false}}"
            wx:if="{{item.rebagable==1}}"
            >再次办理</view
          >
          <view
            class="btn {{item.renewable==0&&item.rebagable==0?'f':''}}"
            bindtap="goXq"
            data-c="{{item.certCode}}"
            data-b="{{item.bagMyself}}"
            >详情</view
          >
        </view>
      </view>
    </block>
  </scroll-view>
</view>
<view class="model" wx:if="{{showModal}}"></view>
<monthlySubscriptionRules
  isShow="{{isShowRules}}"
  rules="{{rules}}"
  bind:selectRule="selectRule"
></monthlySubscriptionRules>

<customModal
  isShow="{{isShowSlotModal}}"
  title="月卡注意事项"
  showFooter="{{false}}"
  bind:close="closeModal"
  data-type="slot"
>
  <!-- 自定义内容插槽 -->
  <view slot="content" class="custom-content">
    <monthly-rules />
  </view>
  <!-- 自定义底部插槽 -->
  <view slot="footer" class="custom-footer">
    <button class="custom-btn" bindtap="closeModal" data-type="slot">我知道了</button>
  </view>
</customModal>
