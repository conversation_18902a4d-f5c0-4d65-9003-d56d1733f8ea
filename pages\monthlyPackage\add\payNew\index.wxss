@import '../../../common/common.wxss';
@import '../../../components/pay.wxss';

.info-one-tilte-up {
  width: 100%;
  min-height: calc(100% - 30rpx);
  background-repeat: no-repeat;
  background-size: 100% 100%;
}

.info-one-tilte {
  width: calc(100% - 80rpx);
  min-height: 65rpx;
  font-size: 34rpx;
  font-weight: bold;
  color: #f1f8ff;
  line-height: 65rpx;
  padding: 10rpx 40rpx 0 40rpx;
}

.info-one-tilteF {
  width: calc(100% - 80rpx);
  min-height: 37rpx;
  font-size: 26rpx;
  font-weight: 400;
  color: #f1f8ff;
  line-height: 37rpx;
  margin-bottom: 10rpx;
  padding: 0 40rpx;
}

.info-one-carTool {
  width: calc(100% - 86rpx);
  display: flex;
  flex-direction: row;
  height: 42rpx;
  background: #f1f8ff;
  border-radius: 10rpx 10rpx 10rpx 10rpx;
  opacity: 1;
  padding: 21rpx 38rpx 17rpx 12rpx;
  margin: 15rpx 18rpx;
}

.info-one-carTool-text {
  height: 42rpx;
  font-size: 30rpx;
  font-weight: bold;
  color: #353535;
  line-height: 42rpx;
  flex: 1;
  margin: auto 0;
}

.info-one-carTool-btn {
  height: 30rpx;
  width: 60rpx;
  font-size: 22rpx;
  font-weight: 400;
  color: #888888;
  line-height: 30rpx;
  margin: auto 0;
}

.icon-jt-left {
  width: 17rpx;
  height: 17rpx;
}

.info-row {
  position: relative;
  display: flex;
  align-items: center;
  padding-left: 55rpx;
  height: 72rpx;
}

.info-row .icon-sjx-left-tilte {
  position: absolute;
  left: 24rpx;
  top: 30rpx;
}

.info-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #353535;
}

.circle-i-orange {
  width: 140rpx;
  height: 40rpx;
  margin-left: 20rpx;
}

.custom-footer {
  width: 100%;
}

.custom-btn {
  width: 100% !important;
  color: #4768f3;
  padding: 20rpx 0;
}

.text-bold {
  font-weight: bold;
}

.custom-content {
  font-size: 28rpx;
  font-weight: 400;
  color: #606266;
  line-height: 32rpx;
}

.bottomDetails-item {
  margin-bottom: 14rpx;
}

.bottomDetails-item:last-of-type {
  margin-bottom: 0;
}
