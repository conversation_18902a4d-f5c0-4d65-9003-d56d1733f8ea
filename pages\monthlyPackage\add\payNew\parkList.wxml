<view class="saveOutView">
  <cu-custom bgColor="white-bg" contentTitle="适用停车场" isBack="true"></cu-custom>
  <scroll-view scroll-y="true" scroll-y="true" class="scrollViewSa">
    <block wx:for="{{list}}" wx:key="index">
      <view class="box columnView" >
          <view class="one">{{item.parkName}}</view>
          <view class="two">{{item.parkAddr}}</view>
      </view>
    </block>
  </scroll-view>
</view>