@import '../common/common.wxss';

.container {
  min-height: 100vh;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
}

/* 加载状态 */
.loading-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 100rpx 40rpx;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.loading-icon {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #e5e5e5;
  border-top: 4rpx solid #4768f3;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 30rpx;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

/* 结果状态 */
.no-arrears-container,
.error-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 100rpx 40rpx;
}

.result-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  background: white;
  border-radius: 20rpx;
  padding: 80rpx 40rpx 60rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 600rpx;
}

.result-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 40rpx;
}

.result-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.result-message {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 60rpx;
  text-align: center;
}

/* 按钮组 */
.button-group {
  display: flex;
  flex-direction: column;
  width: 100%;
  gap: 20rpx;
}

.btn {
  width: 100%;
  height: 88rpx;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: bold;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-primary {
  background: linear-gradient(135deg, #4768f3 0%, #6b8aff 100%);
  color: white;
}

.btn-primary:active {
  background: linear-gradient(135deg, #3a5ae0 0%, #5a7aec 100%);
}

.btn-secondary {
  background: white;
  color: #4768f3;
  border: 2rpx solid #4768f3;
}

.btn-secondary:active {
  background: #f8f9ff;
}

/* 调试信息样式 */
.debug-info {
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 20rpx;
  margin: 20rpx 0;
  border-left: 4rpx solid #4768f3;
}

.debug-title {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 10rpx;
  font-weight: bold;
}

.debug-text {
  font-size: 22rpx;
  color: #888;
  line-height: 1.4;
  word-break: break-all;
}

/* 响应式调整 */
@media (max-width: 375px) {
  .result-content {
    padding: 60rpx 30rpx 40rpx;
  }

  .result-icon {
    width: 100rpx;
    height: 100rpx;
  }

  .result-title {
    font-size: 32rpx;
  }

  .result-message {
    font-size: 26rpx;
  }

  .debug-info {
    padding: 15rpx;
    margin: 15rpx 0;
  }

  .debug-title {
    font-size: 22rpx;
  }

  .debug-text {
    font-size: 20rpx;
  }
}
