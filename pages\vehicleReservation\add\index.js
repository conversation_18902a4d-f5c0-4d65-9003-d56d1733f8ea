var util = require('../../../utils/util')
var api = require('../../../config/api.js')
const app = getApp()
Page({
  /**
   * 页面的初始数据
   */
  data: {
    plateNo: '',
    showPlateNoBox: false, //显示车牌选择
    imgurl: app.imgurl + '/ccxqmn.png',
    carList: [],
    info: {
      id: 1,
      parkName: '',
      parkingAddress: '',
      leftParkingSpaceNum: 0,
      description: '',
      freeTime: 0,
      payRuleDesc: '',
    },
    pageHeight: 0,
    entryTimeDesc: '',
    inTimeLimitText: '',
    refundRuleText: '',
    deposit: 0,
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    const eventChannel = this.getOpenerEventChannel()
    eventChannel.on('setData', res => {
      this.setData({
        info: res.data,
      })
      this.getReservationRule()
    })
  },
  getReservationRule() {
    var that = this
    util.showLoading('正在加载…')
    let parkId = this.data.info.parkId
    util.request(api.getReservationRule + '/' + parkId, {}, 'GET').then(function (res) {
      wx.hideLoading()
      let { code, result } = res || {}
      let { inTimeLimitText, entryTimeDesc, deposit, refundRuleText } = result || {}
      if (code == '0') {
        that.setData({
          inTimeLimitText,
          entryTimeDesc,
          deposit,
          refundRuleText,
        })
        that.getCarList()
      }
    })
  },
  getCarList() {
    var that = this
    util.showLoading('正在加载…')
    util.request(api.getCanReserveCarList + this.data.info.parkId, {}, 'GET').then(function (res) {
      wx.hideLoading()
      if (res.code == '0') {
        var carList = res.result
        if (carList.length < 1) {
          //若当前无车牌，则在预约页面弹出绑定车牌提示
          wx.showModal({
            title: '提示',
            content: '您还没有添加车辆信息',
            confirmText: '去添加',
            confirmColor: '#4768F3',
            success(res) {
              if (res.confirm) {
                wx.navigateTo({
                  url: '/pages/my/myCar/index',
                })
              } else if (res.cancel) {
                wx.navigateBack({
                  delta: 1,
                })
              }
            },
          })
        } else {
          that.setData({
            plateNo: carList[0]['plateNo'],
            carList: carList,
          })
        }
      }
    })
  },
  reservation: function () {
    var that = this
    util.showLoading('正在操作…')
    var data = that.data
    var plateNo = data.plateNo
    var info = data.info
    var parkId = info.parkId
    let param = {
      parkId: parkId,
      plateNo: plateNo,
    }
    util.request(api.checkReservation, param, 'GET').then(function (res) {
      wx.hideLoading()
      let { code, result, message } = res || {}
      if (code == '0') {
        wx.navigateTo({
          url:
            '/pages/vehicleReservation/add/pay/index?id=' +
            result.id +
            '&parkName=' +
            info.parkName +
            '&plateNo=' +
            plateNo +
            '&entryTimeDesc=' +
            data.entryTimeDesc +
            '&deposit=' +
            data.deposit +
            '&parkId=' +
            parkId,
        })
      } else {
        wx.showModal({
          title: '预约失败',
          content: message,
          confirmText: '我知道了',
          showCancel: false,
          confirmColor: '#000000',
          success(res) {},
        })
      }
    })
  },
  reservationSuccess: function (time) {
    wx.showModal({
      title: '预约成功',
      content: '已为您预留好停车位，请于' + time + '之前入场。逾期将取消预约',
      cancelText: '我知道了',
      confirmText: '查看订单',
      success(res) {
        if (res.confirm) {
          console.log('用户点击确定')
          wx.navigateTo({
            url: '/pages/vehicleReservation/index',
          })
        } else if (res.cancel) {
          console.log('用户点击取消')
        }
      },
    })
  },
  showBox: function () {
    this.setData({
      showPlateNoBox: true,
    })
  },
  selectItem: function (e) {
    var detail = e.detail
    this.setData({
      plateNo: detail.plateNo,
    })
  },
})
