.ai-mask {
  position: fixed;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  z-index: 9999;
}
.ai-dialog {
  position: fixed;
  right: 0;
  left: 0;
  top: 10vh;
  bottom: 0;
  margin: auto;
  width: 90vw;
  max-width: 700rpx;
  height: 80vh;
  background: #fff;
  border-radius: 24rpx;
  z-index: 10000;
  display: flex;
  flex-direction: column;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.18);
}
.ai-header {
  display: flex;
  align-items: center;
  padding: 24rpx;
  border-top-left-radius: 24rpx;
  border-top-right-radius: 24rpx;
}
.ai-logo {
  width: 56rpx;
  height: 56rpx;
  border-radius: 50%;
  margin-right: 16rpx;
}
.ai-title {
  flex: 1;
  font-size: 32rpx;
  font-weight: bold;
  color: #256bf5;
}
.ai-close {
  width: 36rpx;
  height: 36rpx;
}
.ai-content {
  flex: 1;
  height: 0;
  padding: 16rpx 24rpx;
  overflow-y: auto;
  background: #ffff;
  width: 100%;
  box-sizing: border-box;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}
.ai-content::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}
.ai-msg {
  margin-bottom: 18rpx;
  display: flex;
}
.ai-msg.user {
  justify-content: flex-end;
}
.ai-msg.user text {
  background: #0166ff;
  color: #fff;
  border-radius: 30rpx;
  padding: 24rpx;
  font-size: 28rpx;
}
.ai-msg.ai {
  justify-content: flex-start;
}
.ai-msg.ai text {
  color: #333;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  border-radius: 16rpx;
  padding: 24rpx;
  background: #f5f5f5;
}

/* Towxml相关样式 */
.ai-msg.ai towxml {
  font-size: 28rpx;
  border-radius: 16rpx;
  padding: 0 24rpx 24rpx 24rpx;
  background: #f5f5f5;
  overflow: hidden;
}

.h2w {
  font-size: 28rpx;
  font-weight: 400;
}

.h2w-light {
  background-color: transparent;
}

.h2w__main {
  padding-top: 0;
  margin: 0;
}

.ai-quick-options {
  padding: 10rpx 24rpx;
  background: #fff;
  border-top: 1rpx solid #f0f0f0;
}
.quick-options-scroll {
  width: 100%;
  white-space: nowrap;
}
.quick-option-items {
  display: flex;
  padding: 6rpx 0;
}
.quick-option-item {
  display: inline-block;
  padding: 12rpx 24rpx;
  margin-right: 16rpx;
  background: #f0f7ff;
  color: #256bf5;
  font-size: 24rpx;
  border-radius: 32rpx;
  white-space: nowrap;
  border: 1rpx solid #e0e9f7;
}
.ai-input-bar {
  padding: 16rpx 24rpx 24rpx 24rpx;
  background: #fff;
}
.ai-textarea-container {
  position: relative;
  width: 100%;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15);
}
.ai-textarea {
  width: 100%;
  max-height: 200rpx;
  padding: 20rpx 60rpx 20rpx 24rpx;
  font-size: 28rpx;
  box-sizing: border-box;
}
.ai-speak {
  text-align: center;
  height: 72rpx;
}

.ai-loading {
  display: flex;
  justify-content: center;
  margin: 20rpx 0;
}
.loading-spinner {
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid rgba(37, 107, 245, 0.2);
  border-radius: 50%;
  border-top-color: #256bf5;
  animation: spin 1s linear infinite;
  position: relative;
  display: inline-block;
  margin-left: 10rpx;
  vertical-align: middle;
}
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.ai-icon-container {
  position: absolute;
  right: 20rpx;
  bottom: 15rpx;
  display: flex;
  align-items: center;
  z-index: 2;
  gap: 10rpx;
}

/* 语音输入按钮样式 */
.ai-voice-icon {
  width: 48rpx;
  height: 48rpx;
}

.ai-voice-icon2 {
  width: 42rpx;
  height: 42rpx;
}

/* 语音波浪动画样式 */
.voice-wave-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 16rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 10;
}

.voice-wave-text {
  font-size: 28rpx;
  color: #256bf5;
  margin-bottom: 20rpx;
}

.voice-wave-animation {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 80rpx;
}

.voice-wave {
  display: inline-block;
  width: 8rpx;
  margin: 0 5rpx;
  background-color: #256bf5;
  border-radius: 4rpx;
  animation: voiceWave 0.5s ease infinite alternate;
}

@keyframes voiceWave {
  0% {
    transform: scaleY(0.5);
  }
  100% {
    transform: scaleY(1.5);
  }
}
