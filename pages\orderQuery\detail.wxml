<cu-custom bgColor="white-bg" contentTitle="{{titleName}}" isBack="true"></cu-custom>
<view wx:if="{{activeLeaveState!==0}}" class="free-box">
  <view class="free-box__title"> 免费时长内 </view>
  <view class="free-box__sub"> 免费时长内，当前无需缴费 </view>
</view>

<view class="box">
  <view class="row">
    <view class="label">停车位置：</view>
    <view class="value">{{info.parkName}}</view>
  </view>
  <view class="row">
    <view class="label">车牌号码：</view>
    <view class="value">{{info.plateNo}}</view>
  </view>
  <view class="row">
    <view class="label">入场时间：</view>
    <view class="value">{{info.enterTime}}</view>
  </view>
  <view class="row" wx:if="{{oderState!=1}}">
    <view class="label">离场时间：</view>
    <view class="value">{{info.arrearsTime}}</view>
  </view>
  <view class="row" wx:if="{{oderState!=1}}">
    <view class="label">停车时长：</view>
    <view class="value">{{info.parkPeriodTime}}</view>
  </view>
  <view class="row" wx:if="{{oderState!=1}}">
    <view class="label">账单金额：</view>
    <view class="value">￥{{info.totalFee}}</view>
  </view>
  <view class="row" wx:if="{{oderState!=1}}">
    <view class="label">优惠金额：</view>
    <view class="value">￥{{info.deductMoney}}</view>
  </view>
  <view class="row">
    <view class="label">{{oderState==1?'停车状态':"缴费状态"}}：</view>
    <view class="value">{{stateNames[oderState]}}</view>
  </view>
</view>

<block wx:if="{{oderState===1}}">
  <button
    wx:if="{{activeLeaveState!==0}}"
    class="convention_button {{activeLeaveState===1&&'btn-disabled'}}"
    disabled="{{activeLeaveState===1}}"
    bind:tap="onLeave"
  >
    离场
  </button>
  <button wx:else class="convention_button" bind:tap="onPay">缴费</button>
</block>
<button wx:if="{{oderState===2}}" class="convention_button" bind:tap="onPay">缴费</button>
