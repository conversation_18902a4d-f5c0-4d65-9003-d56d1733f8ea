
@import '/pages/common/common.wxss';
page {
  background-color: #F6F7FB;
}
.container{
  width: 100%;
}
.common-title{
  margin-top: 30rpx;
}

.menu-list{
  height: 150rpx;
}

.center-one {
  flex-wrap: wrap;
  display: flex;
  width: calc(100% - 100rpx);
  margin: 20rpx 50rpx;
  background: #FFFFFF;
  box-shadow: 0rpx 10rpx 20rpx 0rpx rgba(228, 234, 248, 0.5);
  border-radius: 20rpx;
}

.center-one-v {
  margin-left: 24rpx;
  flex-direction: column;
  display: flex;
  align-items: center;
  margin: 24rpx 33rpx;
  flex-basis: auto;
}
.center-one-v-img {
  margin-top: 0;
  width: 80rpx;
  height: 80rpx;
  justify-content: center;
}

.center-one-v-img image,
.center-two-v-img image {
  width: 100%;
  height: 100%;
}

.center-one-v-text {
  font-weight: 400;
  line-height: 26rpx;
  margin-top: 0;
  width: 100%;
  color: #353535;
  text-align: center;
  min-width: 86rpx;
  height: 26rpx;
  font-size: 22rpx;
  
  font-weight: 500;
}
