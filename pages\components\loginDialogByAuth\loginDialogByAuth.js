var util = require('../../../utils/util')
var api = require('../../../config/api')
var app = getApp()

Component({
  properties: {
    isShow: {
      type: Boolean,
      default: false,
    },
    authPid: {
      type: String,
    },
  },
  data: {
    showLoseTip: false, //显示校验失败的提示
    allowXY: false,
  },
  methods: {
    //点击显示底部弹出
    changeRange: function () {
      this.showModal()
    },
    //底部弹出框
    showModal: function () {
      // 背景遮罩层
      var animation = wx.createAnimation({
        duration: 50,
        timingFunction: 'linear',
        delay: 0,
      })
      //this.animation = animation
      animation.translateY(50).step()
      this.setData({
        animationData: animation.export(),
        isShow: true,
      })
      setTimeout(
        function () {
          animation.translateY(0).step()
          this.setData({
            animationData: animation.export(),
          })
        }.bind(this),
        50
      )
    },

    //点击背景面任意一处时，弹出框隐藏
    hideModal: function (e) {
      //弹出框消失动画
      var animation = wx.createAnimation({
        duration: 10,
        timingFunction: 'linear',
        delay: 0,
      })
      //this.animation = animation
      animation.translateY(10).step()
      this.setData({
        animationData: animation.export(),
      })
      setTimeout(
        function () {
          animation.translateY(0).step()
          this.setData({
            animationData: animation.export(),
            isShow: false,
          })
        }.bind(this),
        10
      )
    },

    allow() {
      if (this.data.allowXY) {
        this.setData({
          allowXY: false,
        })
      } else {
        this.setData({
          allowXY: true,
        })
      }
    },

    viewAgreement: function (e) {
      var id = e.currentTarget.id
      wx.navigateTo({
        url: '/pages/common/viewAgreement/index?id=' + id,
        fail: function (res) {
          console.log(res)
        },
      })
    },

    sqbd() {
      util.showToast('请阅读并同意服务协议、隐私政策协议及登录政策')
    },

    getPhoneNumber: function (e) {
      var that = this
      util.showLoading('正在加载…')
      if (e.detail.errMsg == 'getPhoneNumber:ok') {
        util
          .request(
            api.login,
            {
              code: e.detail.code,
            },
            'POST'
          )
          .then(async function (res) {
            if (res.code == '0') {
              //成功
              try {
                var result = res.result
                console.log('🚀 ~ that.data.authPid:', that.data.authPid)
                console.log('🚀 ~ result.token:', result.token)
                await util.request(
                  api.cxglAuthBindToken,
                  { authPid: that.data.authPid, cxglLoginToken: result.token },
                  'post'
                )
                app.globalData.userInfo = result
                wx.setStorageSync('token', result.token)
                that.triggerEvent('refresh', {}) //通知调用页面刷新
                that.hideModal()
              } catch (error) {
                console.log('🚀 ~ error:', error)
                util.showToast('绑定token信息失败')
              } finally {
                wx.hideLoading()
              }
            } else {
              wx.hideLoading()
              util.showToast(res.message)
            }
          })
      } else {
        this.setData({
          showModal: false,
        })
        wx.hideLoading()
        util.showToast('申请获取手机号失败！')
      }
    },

    login: function () {
      this.hideModal()
      this.triggerEvent('verificationCode')
    },
  },
})
