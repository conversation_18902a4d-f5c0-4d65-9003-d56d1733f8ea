/* pages/components/twoBtnDialog/twoBtnDialog.wxss */
/* 弹窗样式 */
@import "../common.wxss";

.modalDlgTwo {
  position: fixed;
  top: 35%;
  left: 0;
  right: 0;
  z-index: 99999999;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 560rpx;
  min-height: 364rpx;
  background: #FFFFFF;
  border-radius: 8rpx 8rpx 8rpx 8rpx;
}

.modalDlgTwo-title {
  width: 100%;
  height: 50rpx;
  font-size: 36rpx;
  
  font-weight: 400;
  color: #000000;
  line-height: 50rpx;
  text-align: center;
  margin-bottom: 19rpx;
  margin-top: 40rpx;
}

.modalDlgTwo-title-t {
  width: 440rpx;
  min-height: 139rpx;
  font-size: 30rpx;
  
  font-weight: 400;
  color: #353535;
  margin-bottom: 20rpx;
  text-align: center;
}

.modalDlgTwo-border {
  height: 1rpx;
  width: 100%;
  background: #E5E5E5;
}

.modalDlgTwo-xyView {
  width: 100%;
  min-height: 99rpx;
  display: flex;
  flex-direction: row;
}

.modalDlgTwo-xyView-text {
  width: 50%;
  height: 100rpx;
  text-align: center;
  line-height: 100rpx;
}

.modalDlgTwo .blue {
  color: #4768F3;
}

/*弹窗样式结束*/