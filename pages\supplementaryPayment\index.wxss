@import '../common/common.wxss';
@import '../components/pay.wxss';

page {
  background: #f6f7fb;
}

.qf-listDiv {
  width: 100%;
  min-height: 180rpx;
  background: #ffffff;
  opacity: 1;
  margin-bottom: 21rpx;
  flex-direction: row;
  display: flex;
}

.qf-listDiv-left {
  width: calc(100% - 187rpx);
  flex-direction: column;
  display: flex;
  border-right: 1rpx solid #dfdfdf;
  margin-top: 24rpx;
  margin-bottom: 20rpx;
}

.qf-listDiv-left view {
  width: calc(100% - 55rpx);
  padding: 0 20rpx 0 35rpx;
}

.qf-listDiv-left-one {
  min-height: 42rpx;
  font-size: 30rpx;

  font-weight: bold;
  color: #353535;
  line-height: 42rpx;
  margin-bottom: 6rpx;
}

.qf-listDiv-left-two {
  min-height: 37rpx;
  font-size: 26rpx;

  font-weight: 400;
  color: #7e7e7e;
  line-height: 37rpx;
  margin-bottom: 14rpx;
}

.qf-listDiv-left-three {
  min-height: 37rpx;
  font-size: 26rpx;

  font-weight: 400;
  color: #41e0ac;
  line-height: 37rpx;
}

.qf-listDiv-right {
  width: 178rpx;
  flex-direction: column;
  display: flex;
}

.qf-listDiv-right-one {
  width: 100%;
  height: 25rpx;
  font-size: 18rpx;

  font-weight: 400;
  color: #7e7e7e;
  line-height: 25rpx;
  text-align: center;
  margin-top: 15rpx;
}

.qf-listDiv-right-two {
  text-align: center;
  margin-top: 2rpx;
  width: 100%;
  min-height: 48rpx;
  font-size: 34rpx;

  font-weight: bold;
  color: #e94f4f;
  line-height: 54rpx;
}

.qf-listDiv-right-three {
  font-size: 30rpx;

  font-weight: 400;
  color: #ffffff;
  line-height: 56rpx;
  text-align: center;
  margin: auto;
  width: 130rpx;
  height: 56rpx;
  background: linear-gradient(90deg, #ff5745 0%, #ffae00 100%);
  border-radius: 52rpx 52rpx 52rpx 52rpx;
  opacity: 1;
  margin-top: 0;
}

.left {
  margin: auto 40rpx auto 45rpx;
  width: 32rpx;
  height: 100%;
  display: flex;
  align-items: center;
}

.left image {
  width: 32rpx;
  height: 32rpx;
}

.footer {
  width: 100%;
  height: 180rpx;
  background-color: #ffffff;
  box-sizing: border-box;
  font-size: 26rpx;
  font-weight: 400;
}

.footer .totalBox {
  flex: 1;
  font-size: 26rpx;
  font-weight: 400;
  color: #7e7e7e;
  margin: auto;
}

.footer .text {
  min-height: 40rpx;
  width: 100%;
  line-height: 40rpx;
}

.footer .checkbox {
  margin: auto 40rpx;
  width: 100rpx;
  height: 37rpx;
  font-size: 26rpx;
  color: #353535;
  display: flex;
  align-items: center;
}

.footer .checkbox image {
  margin-top: 2rpx;
  margin-right: 10rpx;
  width: 32rpx;
  height: 32rpx;
}

.footer .btn {
  width: 156rpx;
  height: 64rpx;
  background: linear-gradient(90deg, #458bff 0%, #08d7ae 100%);
  box-shadow: 0rpx 3rpx 10rpx 1rpx rgba(37, 107, 245, 0.2);
  border-radius: 40rpx;
  text-align: center;
  font-size: 30rpx;
  color: #ffffff;
  line-height: 64rpx;
  margin: auto 30rpx;
}
.box_convention_button {
  padding: 20rpx 0 0 0;
}
.cu-tag.badge {
  right: 58rpx;
  top: auto;
}
.cwxqzt {
  margin-top: 10rpx;
}
.btn-disabled {
  opacity: 0.6;
}
