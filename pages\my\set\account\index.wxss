/* pages/my/set/account/index.wxss */
@import '../../../common/common.wxss';

page {
  background: #F5F7F9;
}
.row{
  position: relative;
}
.row::after {
  content: '';
  position: absolute;
  top: 40rpx;
  right: 0;
  height: 15rpx;
  width: 15rpx;
  border-width: 4rpx 4rpx 0 0;
  border-color: #C7C7CC;
  border-style: solid;
  transform: matrix(0.71, 0.71, -0.71, 0.71, 0, 0);
  border-top-width: 2px;
  border-right-width: 2px;
  border-bottom-width: 0px;
  border-left-width: 0px;
  border-top-color: rgb(199, 199, 204);
  border-right-color: rgb(199, 199, 204);
  border-bottom-color: rgb(199, 199, 204);
  border-left-color: rgb(199, 199, 204);
  border-top-style: solid;
  border-right-style: solid;
  border-bottom-style: solid;
  border-left-style: solid;
}
.common_listBg_text{
  text-align: right;
  flex:1;
  padding-right:30rpx;
}

