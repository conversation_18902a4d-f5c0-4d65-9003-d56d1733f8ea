// 引入插件
const plugin = requirePlugin('WechatSI')
// 获取全局唯一语音识别管理器
const manager = plugin.getRecordRecognitionManager()

Component({
  properties: {
    show: {
      type: Boolean,
      value: false,
    },
  },
  data: {
    messages: [{ role: 'ai', text: '您好，我是畅行桂林AI助手，有什么可以帮您？' }],
    inputValue: '',
    sessionId: null,
    quickOptions: [{ prompt: '怎么缴纳停车费' }, { prompt: '桂林路边停车收费标准' }],
    isRequesting: false,
    requestTask: null,
    scrollToView: 'scroll-bottom',
    isStartRequesting: false, //开始建立请求
    lastData: '',
    isRecording: false, // 是否正在录音
    recordManager: null, // 录音管理器
    recordingTime: 0, // 录音时长
    recordTimer: null, // 录音计时器
    isVoice: true,
    recordState: 0, // 0: 未录音, 1: 录音中, 3: 录音结束
    isRecognizing: false, // 是否正在识别语音
    voiceWaves: [], // 语音波浪元素数组
    bufferCache: null, // 用于缓存不完整的数据块
  },
  lifetimes: {
    attached() {
      this.initSI()
      // 确保初始化时缓存为空
      this.setData({
        bufferCache: null,
      })
    },
    detached() {
      // 组件销毁时清理计时器
      if (this.data.recordTimer) {
        clearInterval(this.data.recordTimer)
      }
    },
  },
  methods: {
    closeAI() {
      this.triggerEvent('close')
    },
    // 清空对话历史
    clearMessages() {
      wx.showModal({
        title: '提示',
        content: '确定要清空对话历史吗？',
        success: res => {
          if (res.confirm) {
            this.setData(
              {
                messages: [{ role: 'ai', text: '您好，我是畅行桂林AI助手，有什么可以帮您？' }],
                sessionId: null,
                bufferCache: null,
              },
              () => {
                this.scrollToBottom()
              }
            )
          }
        },
      })
    },
    // 防止滑动穿透
    preventTouchMove() {
      return false
    },
    onInput(e) {
      this.setData({ inputValue: e.detail.value })
    },
    // 处理快捷选项点击
    handleQuickOption(e) {
      const { prompt } = e.currentTarget.dataset
      this.setData({ inputValue: prompt }, () => {
        this.sendMsg()
      })
    },
    // 停止请求
    stopRequest() {
      console.log('尝试停止请求', this.data.requestTask, this.data.isRequesting)
      if (this.data.requestTask && this.data.isRequesting) {
        try {
          // 先将isRequesting设为false，防止后续数据处理
          this.setData({
            isRequesting: false,
          })

          // 尝试中止请求
          this.data.requestTask.abort()
          console.log('请求已中止')

          // 更新最后一条AI消息
          const messages = this.data.messages
          const lastAiMsgIndex = messages.length - 1
          if (lastAiMsgIndex >= 0 && messages[lastAiMsgIndex].role === 'ai') {
            this.updateAIMessage(lastAiMsgIndex, messages[lastAiMsgIndex].text + '\n\n[请求已取消]')
          }

          // 清空请求任务引用
          this.setData({
            requestTask: null,
            bufferCache: null, // 清除缓存
          })
        } catch (error) {
          console.error('中止请求失败:', error)

          // 即使中止失败，也重置状态
          this.setData({
            isRequesting: false,
            requestTask: null,
            bufferCache: null, // 清除缓存
          })
        }
      }
    },
    // 处理发送/停止按钮点击
    handleSendOrStop() {
      console.log('点击发送/停止按钮', this.data.isRequesting)
      if (this.data.isRequesting) {
        console.log('正在请求中，尝试停止请求')
        this.stopRequest()
      } else {
        console.log('开始发送消息')
        this.sendMsg()
      }
    },
    // 滚动到底部
    scrollToBottom() {
      this.setData({
        scrollToView: 'scroll-bottom',
      })
    },

    // 插件初始化
    initSI() {
      const that = this
      // 有新的识别内容返回，则会调用此事件
      manager.onRecognize = function (res) {
        console.log(res)
      }
      // 正常开始录音识别时会调用此事件
      manager.onStart = function (res) {
        console.log('成功开始录音识别', res)
        // 开始录音时-抖动一下手机
        wx.vibrateShort({ type: 'medium' })
        that.setData({
          isRecognizing: true,
        })
      }
      // 识别错误事件
      manager.onError = function (res) {
        console.error('error msg', res)
        const tips = {
          '-30003': '说话时间间隔太短，无法识别语音',
          '-30004': '没有听清，请再说一次~',
          '-30011': '上个录音正在识别中，请稍后尝试',
        }
        const retcode = res?.retcode.toString()
        retcode &&
          wx.showToast({
            title: tips[`${retcode}`],
            icon: 'none',
            duration: 2000,
          })

        // 重置识别状态
        that.setData({
          isRecognizing: false,
          recordState: 0,
        })
      }
      //识别结束事件
      manager.onStop = function (res) {
        console.log('..............结束录音', res)
        console.log('录音临时文件地址 -->', res.tempFilePath)
        console.log('录音总时长 -->', res.duration, 'ms')
        console.log('文件大小 --> ', res.fileSize, 'B')
        console.log('语音内容 --> ', res.result)

        // 重置识别状态
        that.setData({
          isRecognizing: false,
          recordState: 0,
        })

        if (res.result === '') {
          wx.showToast({
            title: '没有听清，请再说一次~',
            icon: 'none',
            duration: 2000,
          })
          return
        }

        // 将识别到的文字设置为输入值，并直接发送
        that.setData(
          {
            inputValue: res.result,
          },
          () => {
            // 调用发送消息方法
            that.sendMsg()
          }
        )
      }
    },
    // 手指触摸动作-开始录制语音
    touchStart() {
      // 生成20个波浪元素，每个元素有不同的动画延迟和高度
      const voiceWaves = []
      for (let i = 0; i < 20; i++) {
        const delay = (i % 5) * 0.1 // 0, 0.1, 0.2, 0.3, 0.4秒的延迟
        const height = 30 + (i % 3) * 5 // 30, 35, 40像素的高度
        voiceWaves.push({
          style: `animation-delay: ${delay}s; height: ${height}rpx;`,
        })
      }

      this.setData({
        recordState: 1,
        isRecording: true,
        voiceWaves: voiceWaves,
      })
      // 语音识别开始
      manager.start()
    },
    // 手指触摸动作-结束录制
    touchEnd() {
      this.setData({
        recordState: 0,
        isRecording: false,
      })
      // 语音识别结束
      manager.stop()
    },

    // 切换语音输入
    toggleVoiceInput() {
      this.setData({
        isVoice: !this.data.isVoice,
      })
    },

    sendMsg() {
      const val = this.data.inputValue.trim()
      if (!val || this.data.isRequesting) return

      const msgs = this.data.messages.concat([{ role: 'user', text: val }])
      this.setData(
        {
          messages: msgs,
          inputValue: '',
          isRequesting: true,
          isStartRequesting: true,
        },
        () => {
          this.scrollToBottom()
        }
      )

      // 添加等待中的消息
      const aiMsgIndex = this.data.messages.length
      this.setData(
        {
          messages: this.data.messages.concat([{ role: 'ai', text: '' }]),
        },
        () => {
          this.scrollToBottom()
        }
      )

      // 创建请求
      let currentAIResponse = ''

      // 先保存请求任务引用到组件实例上
      let requestTask = null

      try {
        requestTask = wx.request({
          url: 'https://ai.glrcw.com/chat',
          method: 'POST',
          timeout: 3 * 60 * 1000, // 设置3分钟超时
          header: {
            'content-type': 'application/json',
            Connection: 'keep-alive',
          },
          data: {
            sessionId: this.data.sessionId,
            prompt: val,
          },
          enableChunked: true, // 启用分块传输
          responseType: 'text', // 明确指定响应类型为文本
          fail: err => {
            console.error('AI请求失败', err)
            // 区分主动取消和其他失败情况
            if (err.errMsg === 'request:fail abort') {
              console.log('请求被用户取消')
            } else {
              // 只有在仍然处于请求状态时才更新消息
              if (this.data.isRequesting) {
                this.updateAIMessage(aiMsgIndex, '抱歉，连接服务器失败，请稍后再试。')
              }
            }
            // 清除缓存
            this.setData({
              bufferCache: null,
            })
          },
          complete: () => {
            console.log('请求完成')
            // 重置请求状态
            this.setData({
              isRequesting: false,
              requestTask: null,
              isStartRequesting: false,
              bufferCache: null, // 清除缓存
            })
          },
        })

        console.log('请求任务创建成功', requestTask)

        // 处理分块接收
        requestTask.onChunkReceived(chunk => {
          // 收到第一个数据块时，更新loading状态
          if (this.data.isStartRequesting) {
            this.setData(
              {
                isStartRequesting: false,
              },
              () => {
                this.scrollToBottom()
              }
            )
          }

          // 如果已经不在请求状态，则不处理新数据
          if (!this.data.isRequesting) {
            console.log('请求已停止，忽略新收到的数据块')
            return
          }
          // 解析数据
          let chunkText = ''
          if (typeof chunk.data === 'string') {
            chunkText = chunk.data
          } else if (chunk.data instanceof ArrayBuffer) {
            // 处理 ArrayBuffer 数据
            const newUint8Array = new Uint8Array(chunk.data)
            let uint8Array = newUint8Array

            // 如果有缓存的不完整数据，合并处理
            if (this.data.bufferCache) {
              // 创建一个新的合并后的 Uint8Array
              const totalLength = this.data.bufferCache.length + newUint8Array.length
              uint8Array = new Uint8Array(totalLength)

              // 复制缓存的数据
              uint8Array.set(this.data.bufferCache, 0)
              // 复制新接收的数据
              uint8Array.set(newUint8Array, this.data.bufferCache.length)

              console.log('合并缓存数据，总长度:', totalLength)
            }

            let result = ''
            let decodeSuccess = false

            try {
              // 尝试解码 UTF-8 数据
              const encodedString = String.fromCharCode.apply(null, uint8Array)
              result = decodeURIComponent(escape(encodedString))
              decodeSuccess = true

              // 解码成功，清除缓存
              if (this.data.bufferCache) {
                this.setData({ bufferCache: null })
                console.log('解码成功，清除缓存')
              }
            } catch (error) {
              console.error('UTF-8 解码失败，可能是不完整的数据块:', error)

              // 解码失败，缓存当前数据块
              this.setData({ bufferCache: uint8Array })
              console.log('缓存不完整数据块，长度:', uint8Array.length)

              // 不处理这个不完整的块，等待下一个块到达后合并处理
              return
            }

            if (!decodeSuccess) {
              // 如果解码仍然失败，尝试直接转换（不推荐，但作为最后的降级方案）
              result = ''
              for (let i = 0; i < uint8Array.length; i++) {
                result += String.fromCharCode(uint8Array[i])
              }
            }

            chunkText = result
          }
          if (chunkText && this.data.isRequesting) {
            // 只有在仍然处于请求状态时才处理数据
            this.processChunkData([chunkText], aiMsgIndex, currentAIResponse)
            // 更新当前响应
            currentAIResponse = this.data.messages[aiMsgIndex].text
            // 确保每次接收到新数据后滚动到底部
            this.scrollToBottom()
          }
        })

        // 立即保存请求任务引用到data中
        this.setData({
          requestTask: requestTask,
        })
      } catch (error) {
        console.error('创建请求失败:', error)
        this.updateAIMessage(aiMsgIndex, '抱歉，创建请求失败，请稍后再试。')
        this.setData({
          isRequesting: false,
          requestTask: null,
        })
      }
    },

    // 处理块数据
    processChunkData(resTexts, aiMsgIndex, currentText) {
      // 如果已经不在请求状态，则不处理新数据
      if (!this.data.isRequesting) {
        console.log('请求已停止，不再处理新数据')
        return currentText
      }

      let sessionId = this.data.sessionId
      let newText = currentText || ''

      // 检查是否为数组
      if (Array.isArray(resTexts)) {
        // 使用正则表达式匹配每个事件块
        for (const text of resTexts) {
          // 再次检查是否仍在请求状态
          if (!this.data.isRequesting) {
            console.log('处理数据时检测到请求已停止')
            return newText
          }

          const eventRegex = /id:\d+[\s\S]*?data:([\s\S]*?)(?=(?:id:\d+)|$)/g
          let match

          // 如果文本包含"data:"格式，使用正则提取
          if (text.includes('data:')) {
            while ((match = eventRegex.exec(text)) !== null) {
              // 再次检查是否仍在请求状态
              if (!this.data.isRequesting) {
                console.log('处理数据块时检测到请求已停止')
                return newText
              }

              const dataContent = match[1].trim()
              try {
                const jsonData = JSON.parse(dataContent)
                if (jsonData.output && jsonData.output.text) {
                  // 处理文本
                  newText += jsonData.output.text

                  // 保存会话ID
                  if (jsonData.output.session_id) {
                    sessionId = jsonData.output.session_id
                  }
                }
              } catch (jsonError) {
                console.error('JSON解析错误:', jsonError, dataContent)
              }
            }
          } else {
            // 直接尝试解析为JSON
            try {
              const jsonData = JSON.parse(text)
              if (jsonData.output && jsonData.output.text) {
                newText += jsonData.output.text

                if (jsonData.output.session_id) {
                  sessionId = jsonData.output.session_id
                }
              }
            } catch (jsonError) {
              console.log('非JSON格式', jsonError)
            }
          }
        }
      }

      // 更新会话ID
      if (sessionId !== this.data.sessionId) {
        this.setData({ sessionId })
      }

      // 更新AI消息
      if (newText && this.data.isRequesting) {
        this.updateAIMessage(aiMsgIndex, newText)
      }

      return newText
    },

    // 更新AI消息
    updateAIMessage(index, text) {
      const messages = this.data.messages
      if (index < messages.length) {
        // 获取app实例
        const app = getApp()

        // 使用towxml解析markdown文本
        const result = app.towxml(text, 'markdown')

        messages[index] = {
          role: 'ai',
          text: text,
          nodes: result,
        }

        this.setData(
          {
            messages: messages,
          },
          () => {
            this.scrollToBottom()
          }
        )
      }
    },
  },
  observers: {
    'show': function (show) {
      if (show) {
        // 当对话框显示时，滚动到底部
        setTimeout(() => {
          this.scrollToBottom()
        }, 100)
      }
    },
    'messages': function () {
      // 当消息列表变化时，滚动到底部
      this.scrollToBottom()
    },
  },
})
