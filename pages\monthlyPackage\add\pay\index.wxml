<view class="saveOutView">
  <cu-custom bgColor="white-bg" contentTitle="包月办理" isBack="true"></cu-custom>
  <scroll-view scroll-y="true" class="scrollViewSa">
    <view class="backWihte columnView">
      <view class="info-bt">
        <view class="icon-sjx-left-tilte"></view>
        <view class="info-bt-text">包月车场</view>
      </view>
      <view class="info-one-tilte-up" style="background-image: url('{{imgUrl}}/pay-title.png');">
        <view class="info-one-tilte">{{parkName}}</view>
        <view class="info-one-tilteF">{{parkingAddress}}</view>
        <view class="info-one-carTool-text" wx:if="{{isRenewal}}">包月车牌：{{plateNo}}</view>
        <view class="info-one-carTool " bindtap="showBox" wx:else>
          <view class="info-one-carTool-text">包月车牌：{{plateNo}}</view>
          <view class="info-one-carTool-btn icon-jt-left-bj">切换</view>
          <view class="icon-jt-left"></view>
        </view>
      </view>
    </view>
    <view class="backWihte columnView">
      <view class="info-bt">
        <view class="icon-sjx-left-tilte"></view>
        <view class="info-bt-text">包月套餐</view>
      </view>
      <view>
        <view class="infoListBox">
          <block wx:for="{{packageList}}" wx:key="index">
            <view bindtap="select" data-info="{{item}}" id="{{index}}" class="infoBox {{selectIndex == index ? 'choose':''}}">
              <view class="infoBox-ysb" wx:if="{{item.sfyh}}">限时优惠</view>
              <view class="infoBox-text" style="{{item.sfyh?'': 'margin-top: 22rpx'}}">{{item.pkgDesc}}</view>
              <view class="infoBox-text">{{item.price}}元</view>
            </view>
          </block>
        </view>
      </view>
    </view>
    <view class="backWihte columnView">
      <view class="info-bt">
        <view class="icon-sjx-left-tilte"></view>
        <view class="info-bt-text">支付方式</view>
      </view>
      <view class="paymentListBox">
        <block wx:for="{{payList}}" wx:key="index">
          <view id="{{item.payType}}" class="infoBox  {{selectPay == item.payType ? 'choosePay':''}}" bindtap="selectPayMode" wx:if="{{item.payType=='BALANCE_PAY'}}">
            <view class="infoBox-text" style="margin-top: 14rpx;">余额</view>
            <view class="infoBox-text" id="ye" data-money="{{item.iconUrl}}">{{item.iconUrl}}元</view>
          </view>
          <view id="{{item.payType}}" class="infoBox {{selectPay == item.payType ? 'choosePay':''}}" style="display: flex; flex-direction: column;" bindtap="selectPayMode" wx:else>
            <image class="img" src="{{item.iconUrl}}" style="width: 48rpx;height: 48rpx;position: static;margin: 14rpx auto 0;"></image>
            <view class="infoBox-text">微信支付</view>
          </view>
        </block>
      </view>
    </view>
    <view class=" infoBox-money columnView">
      <view class="infoBox-pay" style="margin-left: 52rpx;">应付金额:</view>
      <view class="infoBox-pay" style="color: #FF2D2D;">{{packageList[selectIndex].price}}元</view>
    </view>
    <view class='box_convention_button'>
      <button class="convention_button" bindtap="pay">支付</button>
    </view>
  </scroll-view>
  <bottomListDialog catchtouchmove="preventTouchMove" bind:selectItem="selectItem" isShow="{{showPlateNoBox}}" list="{{carList}}" rangekey="plateNo" disablekey="bagable" disableText="已包月，需办理请前往续费" value="{{plateNo}}"></bottomListDialog>
</view>