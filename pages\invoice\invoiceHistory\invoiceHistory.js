var api = require('../../../config/api.js')
var util = require('../../../utils/util')
Page({
  /**
   * 页面的初始数据
   */
  data: {
    email: '',
    isShowPreviewBox: false,
    currentPage: 1,
    showNullMoreTip: false,
    list: [],
    pageSize: 20, //分页大小
  },
  onLoad: function (options) {},
  onShow: function () {
    this.setData({
      currentPage: 1,
      showNullMoreTip: false,
      list: [],
    })
    this.getList()
  },
  getList: function () {
    var that = this
    util.showLoading('正在加载…')
    var currentPage = that.data.currentPage
    util
      .request(
        api.getInvoiceRecordList,
        {
          'payBusiness': 'ARREARS',
          'pageSize': that.data.pageSize,
          'currentPage': currentPage,
        },
        'GET'
      )
      .then(function (res) {
        if (res.code == '0') {
          wx.hideLoading()
          if (res.total == 0) {
            if (currentPage == 1) {
              //没有数据
              that.setData({
                list: [],
                showNullMoreTip: false,
              })
            } else {
              //没有更多数据
              that.setData({
                showNullMoreTip: true,
              })
            }
          } else {
            var records = res.result.records
            that.setData({
              list: that.data.list.concat(records),
              showNullMoreTip: false,
            })
            if (res.pages == currentPage) {
              //没有数据
              that.setData({
                showNullMoreTip: true,
              })
            }
          }
          //加载下一页
          that.setData({
            currentPage: currentPage + 1,
          })
        } else {
          wx.hideLoading()
          util.showToast(res.message)
        }
      })
  },
  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {
    this.setData({
      pageSize: 20,
      currentPage: 1,
      list: [],
    })
    this.getList()
    wx.stopPullDownRefresh()
  },
  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {
    if (this.data.showNullMoreTip) {
      util.showToast('没有更多数据了')
    } else {
      this.getList()
    }
  },
  goXq: function (e) {
    var currentTarget = e.currentTarget
    var info = currentTarget.dataset.info
    wx.navigateTo({
      url: '/pages/invoice/invoiceHistory/invoiceDetail',
      success: res => {
        res.eventChannel.emit('setData', {
          data: info,
        })
      },
    })
  },
  closePreviewBox() {
    this.setData({
      isShowPreviewBox: false,
    })
  },
  reissueInvoice(e) {
    var currentTarget = e.currentTarget
    var id = currentTarget.dataset.id
    this.setData({
      isShowPreviewBox: true,
      invoiceId: id,
    })
  },
  bindKeyInput: function (e) {
    this.setData({
      email: e.detail.value,
    })
  },
  onSubmit(e) {
    //重发发票
    var that = this
    var data = that.data
    util.showLoading('正在加载…')

    util
      .request(
        api.resendInvoice,
        {
          'email': data.email,
          'invoiceId': data.invoiceId,
        },
        'POST'
      )
      .then(function (res) {
        if (res.code == '0') {
          wx.hideLoading()
          util.showToast('提交成功！')
          var clock = setTimeout(function () {
            wx.redirectTo({
              url: '/pages/invoice/invoiceResult/invoiceResult',
            })
            clearInterval(clock)
          }, 500)
        } else {
          wx.hideLoading()
          util.showToast(res.message)
        }
      })
  },
  applyForReopening: function (e) {
    wx.showModal({
      title: '提示',
      content: '重开发票需重新选择订单进行开票',
      confirmText: '去开票',
      cancelText: '取消',
      confirmColor: '#4768F3',
      success(res) {
        if (res.confirm) {
          wx.redirectTo({
            url: '/pages/invoice/orderList/orderList?type=' + e.currentTarget.dataset.type,
          })
        }
      },
    })
  },
})
