var util = require('../../utils/util')
var api = require('../../config/api.js')
var app = getApp()
var commonApi = require('../../utils/commonApi')
Page({
  data: {
    total: 0,
    //欠费补缴
    currentOrderType: 'stopping',
    batchType: '',
    list: [],
    selectedList: [],
    bills: [], //发票申请订单编号
    invoiceAmout: 0,
    showNullTip: false,
    pageSize: 100, //产品要求是100条
    currentPage: 1,
    showNullMoreTip: false,
    //正在停车
    finded: false,
    plateNo: '',
    plateColor: '',
    uniqueId: '',
    tradeNo: '',
    payList: [],
    balanceText: '', //当前余额
    selectPay: '', //支付方式
    discountCoupon: '无可用优惠券',
    couponCode: '', //优惠劵ID

    activeLeaveState: 0, //离场按钮状态，0隐藏，1禁用，2可用
    imageState: 0, //0隐藏，1可看小图，2可点击看大图
  },
  onLoad(options) {
    this.setData({
      plateNo: options.plateNo,
      plateColor: options.plateColor,
    })
  },
  onShow() {
    util.showLoading('正在加载…')
    let pages = getCurrentPages()
    let currPage = pages[pages.length - 1]
    this.setData({
      //未登录查临时缴费没有优惠劵。暂时保留优惠劵字段。12.6
      couponCode: currPage?.data?.couponCode,
      discountCoupon: currPage?.data?.discountCoupon,
      uniqueId: currPage?.data?.uniqueId,
    })
    if (this.data.currentOrderType == 'stopping') {
      this.stoppingAction()
      wx.hideLoading()
    } else {
      this.setData({
        finded: false,
        currentPage: 1,
        batchType: '',
        list: [],
        selectedList: [],
        invoiceAmout: 0,
        showNullMoreTip: false,
      })
      this.getList()
      this.getPayMethod('')
    }
  },
  getTempCarArrearTotal() {
    let that = this
    util.showLoading('正在加载…')
    util
      .request(
        api.getTempCarArrearTotal,
        {
          plateColor: that.data.plateColor,
          plateNo: that.data.plateNo,
        },
        'GET'
      )
      .then(function (res) {
        wx.hideLoading()
        let { code, result } = res || {}
        if (code == '0') {
          that.setData({
            total: result,
          })
        } else {
          util.showToast(res.message)
        }
      })
      .catch(err => {
        wx.hideLoading()
        util.showToast('服务异常，请稍后重试')
      })
  },
  changeOrderType(e) {
    let type = e.currentTarget.dataset.type
    if (type == 'stopping') {
      this.setData({
        total: 0,
        finded: false,
        currentOrderType: type,
      })
      this.stoppingAction()
    } else {
      this.setData({
        total: 0,
        finded: false,
        currentPage: 1,
        currentOrderType: type,
        batchType: '',
        list: [],
        selectedList: [],
        invoiceAmout: 0,
        showNullMoreTip: false,
      })
      this.getList()
      this.getPayMethod('')
    }
  },
  /* 加载数据 */
  getList: function () {
    var that = this
    util.showLoading('正在加载…')
    var currentPage = that.data.currentPage
    util
      .request(
        api.getTempCarArrears,
        {
          plateColor: that.data.plateColor,
          plateNo: that.data.plateNo,
          currentPage: currentPage,
          pageSize: 20,
        },
        'GET'
      )
      .then(function (res) {
        if (res.code == '0') {
          wx.hideLoading()
          var total = res.result.total
          if (total == 0) {
            that.setData({
              total: total,
              finded: false,
              list: [],
              showNullTip: true,
            })
          } else {
            var records = res.result.records
            that.setData({
              total: total,
              finded: true,
              list: that.data.list.concat(records),
              showNullTip: false,
            })
            if (res.result.pages == currentPage) {
              //没有数据
              that.setData({
                showNullMoreTip: true,
              })
            }
          }
          //加载下一页
          that.setData({
            currentPage: currentPage + 1,
          })
        } else {
          wx.hideLoading()
          util.showToast(res.message)
        }
      })
      .finally(() => {
        wx.hideLoading()
      })
  },
  getStopping() {
    var that = this
    util
      .request(
        api.getArrearsByPlate,
        {
          plateColor: that.data.plateColor,
          plateNo: that.data.plateNo,
        },
        'GET'
      )
      .then(function (res) {
        var info = res.result
        if (res.code == '0') {
          that.setData({
            uniqueId: info.uniqueId,
            finded: true,
            info: info,
            money: info.totalCost,
          })
          that.getPayMethod(info.parkId)
        } else {
          that.setData({
            finded: false, //code不为0的时候代表没查到
          })
          wx.hideLoading()
          util.showToast(res.message)
        }
      })
  },
  selectDiscountCoupon() {
    var data = this.data
    wx.navigateTo({
      url:
        '/pages/parkingPayment/discountCoupon/index?couponCode=' +
        data.couponCode +
        '&uniqueId=' +
        data.uniqueId +
        '&parkCode=' +
        data.info.parkCode +
        '&isVisitor=false',
    })
  },
  pay() {
    util.showLoading('正在加载…')
    var that = this
    var data = that.data
    var selectPay = data.selectPay
    that.setData({
      isClick: false, //在点击一次后，点击状态变为关闭，默认为开启
    })
    var clock = setTimeout(
      () => {
        //定义一个延时操作setTimeout
        that.setData({
          isClick: true,
        })
        clearInterval(clock)
      },
      3000 //在3秒后，点击状态恢复为默认开启状态
    )
    if (selectPay === 'BALANCE_PAY') {
      if (data.balanceText < data.info.totalFee) {
        wx.hideLoading()
        util.showToast('余额不足，请充值')
        return
      }
    }
    var useCoupon = 0
    if (data.couponCode != '') {
      useCoupon = 1
    }
    util
      .request(
        api.pay,
        {
          'id': data.info.id,
          'payType': selectPay,
          'coupons': [data.couponCode],
          'useCoupon': useCoupon,
          'plateNo': data.plateNo,
        },
        'POST'
      )
      .then(function (res) {
        var infos = res.result
        if (res.code == '0') {
          that.setData({
            tradeNo: infos.tradeNo,
          })
          wx.hideLoading()
          if (infos.payStatus == 'PAID') {
            //说明是无需付款可以直接返回成功
            wx.redirectTo({
              //直接跳转到成功界面
              url: '/pages/common/payState/index?type=tcjf&id=' + that.data.tradeNo,
            })
          } else {
            that.payWeiPay_dd(infos.timestamp, infos.nonceStr, infos.packageVal, infos.paySign, infos.signType)
          }
        } else {
          wx.hideLoading()
          util.showToast(res.message)
        }
      })
  },
  selectPayMode(e) {
    var currentTarget = e.currentTarget
    this.setData({
      selectPay: currentTarget.id,
    })
  },
  getPayMethod(parkId) {
    // 判断用户是否已登录
    if (app.globalData?.userInfo?.loginStatus === 'LOGGED_IN') {
      // 参考supplementaryPayment的getPayMethod实现
      const that = this
      util
        .request(api.getRechargeInfo, null, 'GET')
        .then(function (res) {
          if (res.code !== '0') return
          const balance = Number(res.result?.balance) || 0
          const payList = [
            { iconUrl: res.result?.balance, name: '余额', payType: 'BALANCE_PAY' },
            {
              iconUrl: 'https://app.okguilin.com/image/payment/wechat_pay.png',
              name: '微信支付',
              payType: 'WECHAT_PAY',
            },
          ]
          that.setData({
            payList,
            selectPay: balance > 0 ? 'BALANCE_PAY' : 'WECHAT_PAY',
            balanceText: res.result?.balance || 0,
          })
          wx.hideLoading()
        })
        .finally(() => {
          wx.hideLoading()
        })
    } else {
      //未登录时使用原来的方法
      util.showLoading('正在加载…')
      var that = this
      util
        .request(
          api.getPayMethodByParkId,
          {
            payBusiness: 'ARREARS',
          },
          'GET'
        )
        .then(function (res) {
          var list = res.result
          if (res.code == '0') {
            console.log('获取支付方式成功')
            that.setData({
              payList: list,
            })
            if (list.length > 0) {
              var payType = list[0]['payType']
              console.log('获取支付方式成功---默认支付类型' + payType)
              that.setData({
                selectPay: payType,
              })
              if (payType === 'BALANCE_PAY') {
                that.setData({
                  balanceText: list[0]['iconUrl'], //用来对比余额
                })
              }
            }
            wx.hideLoading()
          } else {
            console.log('获取支付方式失败' + res.message)
            wx.hideLoading()
          }
        })
        .finally(() => {
          wx.hideLoading()
        })
    }
  },
  payWeiPay_dd: function (timeStamp, nonceStr, packageWord, paySign, signType) {
    var that = this
    wx.requestPayment({
      //吊起微信支付
      timeStamp: timeStamp,
      nonceStr: nonceStr,
      package: packageWord,
      signType: signType,
      paySign: paySign,
      success(res) {
        wx.hideLoading()
        console.log('支付成功')
      },
      fail(res) {
        wx.hideLoading()
        console.log('支付失败')
      },
      'complete': function (res) {
        console.log('支付完成')
        if (res.errMsg == 'requestPayment:ok') {
          wx.redirectTo({
            url: '/pages/common/payState/index?type=tcjf&id=' + that.data.tradeNo,
          })
        } else {
          wx.showModal({
            title: '提示',
            content: '支付失败',
          })
        }
        return
      },
    })
  },
  /* 切换批量选择类型 全部 */
  batchSelected(e) {
    let type = e.currentTarget.dataset.type
    if (type == this.data.batchType) {
      type = ''
    }
    this.setData({
      batchType: type,
    })
    let list = this.data.list
    if (type == 'all') {
      var arry = []
      for (let i = 0; i < list.length; i++) {
        list[i]['checkstate'] = true
        arry.push(list[i])
      }
      let invoiceAmout = this.calculateAmout(list)
      this.setData({
        selectedList: arry,
        invoiceAmout: invoiceAmout,
      })

      // 批量选择后自动选择支付方式
      this.autoSelectPayMethod(arry)
    } else if (type == '') {
      for (let i = 0; i < list.length; i++) {
        list[i]['checkstate'] = false
      }
      this.setData({
        selectedList: [],
        invoiceAmout: 0,
      })

      // 清空选择后自动选择支付方式
      this.autoSelectPayMethod([])
    }
    this.setData({
      list: list,
    })
  },
  gotoInvoiceTitle() {
    var data = this.data
    var selectedList = data.selectedList
    if (selectedList.length < 1) {
      util.showToast('请勾选订单')
    } else {
      //批量支付
      util.showLoading('正在加载…')
      var that = this
      var ids = []
      for (var i = 0; i < selectedList.length; i++) {
        ids.push(selectedList[i]['arrearsId'])
      }
      util
        .request(
          api.addBatchPay,
          {
            'ids': ids,
            'payType': data.selectPay,
          },
          'POST'
        )
        .then(function (res) {
          if (res.code == '0') {
            var infos = res.result
            that.setData({
              tradeNo: infos.tradeNo,
            })
            that.payWeiPay(infos.timestamp, infos.nonceStr, infos.packageVal, infos.paySign, infos.signType)
          } else {
            wx.hideLoading()
            wx.showModal({
              title: '提示',
              content: res.message || '生成支付订单失败',
            })
          }
        })
    }
  },
  payWeiPay: function (timeStamp, nonceStr, packageWord, paySign, signType) {
    var that = this
    wx.requestPayment({
      //吊起微信支付
      timeStamp: timeStamp,
      nonceStr: nonceStr,
      package: packageWord,
      signType: signType,
      paySign: paySign,
      success(res) {
        wx.hideLoading()
        console.log('支付成功')
      },
      fail(res) {
        wx.hideLoading()
        console.log('支付失败')
      },
      'complete': function (res) {
        console.log('支付完成')
        if (res.errMsg == 'requestPayment:ok') {
          wx.redirectTo({
            //直接跳转到成功界面
            url: '/pages/common/payState/index?type=lstcjfqfbj&id=' + that.data.tradeNo,
          })
        } else {
          wx.showModal({
            title: '提示',
            content: '支付失败',
          })
        }
        return
      },
    })
  },
  /* 当item被选择时 */
  changeCheckState(e) {
    var checkState, info
    var dataset = e.currentTarget.dataset
    let selectedList = this.data.selectedList
    info = dataset.info
    checkState = dataset.checkstate
    if (checkState == undefined) {
      checkState = false
    }

    if (!checkState) {
      checkState = true
      selectedList.push(info)
    } else {
      checkState = false
      selectedList = this.findDataInSelectedList(selectedList, info)
    }
    let invoiceAmout = this.calculateAmout(selectedList)
    console.log(invoiceAmout)

    this.setData({
      selectedList: selectedList,
      invoiceAmout: invoiceAmout,
    })
    var index = e.currentTarget.dataset.index
    this.setData({
      ['list[' + index + '].checkstate']: checkState,
    })

    // 根据选中金额自动切换支付方式
    this.autoSelectPayMethod(selectedList)
  },

  // 根据选中金额自动选择支付方式
  autoSelectPayMethod(selectedList) {
    // 获取实际金额数值（不带千分位逗号）
    let amountStr = this.calculateAmout(selectedList).replace(/,/g, '')
    let amount = parseFloat(amountStr) || 0
    let balance = parseFloat(this.data.balanceText) || 0

    // 如果余额大于等于补缴金额，选择余额支付，否则选择微信支付
    if (balance >= amount && amount > 0) {
      this.setData({
        selectPay: 'BALANCE_PAY',
      })
    } else if (amount > 0) {
      this.setData({
        selectPay: 'WECHAT_PAY',
      })
    }
  },

  findDataInSelectedList(list, data) {
    if (!list) {
      return list
    }
    list.splice(
      list.findIndex(item => item.id === data.id),
      1
    )
    return list
  },
  /* 根据list计算金额 */
  calculateAmout(list) {
    if (!list) {
      return '0.00'
    }
    let amount = list.reduce((value, item) => value + parseFloat(item['totalFee']), 0) //求和
    return (+amount).toFixed(2).replace(/^-?\d+/g, item => item.replace(/(?=(?!\b)(\d{3})+$)/g, ','))
  },
  bindReachBottom: function () {
    console.log('上拉加载....')
    var that = this
    var data = that.data
    if (data.showNullMoreTip) {
      util.showToast('没有更多数据了')
    } else {
      that.getList()
    }
  },
  goxq(e) {
    var currentTarget = e.currentTarget
    var id = currentTarget.id
    wx.navigateTo({
      url: '/pages/supplementaryPayment/details/index?uniqueId=' + id, //'/pages/parkingPayment/index?uniqueId=' + id 待付订单的支付页面需要显示离场时间，为了不动原先逻辑，新增一个单独的界面
    })
  },
  // 点击事件
  previewSqs(event) {
    if (this.data.imageState !== 2) {
      return
    }
    //10.11新增图片预览功能
    // 拿到图片的地址url
    let currentUrl = event.currentTarget.dataset.src
    // 微信预览图片的方法
    wx.previewImage({
      urls: [currentUrl], // 预览的地址url
    })
  },
  async fetchParkState() {
    const data = await commonApi.getParkState(this.data.plateNo)
    this.parkCode = data.parkCode
    this.uniqueId = data.uniqueId
    this.setData({ activeLeaveState: data.activeLeaveState })
  },
  async onLeave() {
    try {
      const result = await commonApi.carLeave(this.parkCode, this.uniqueId)
      if (result) {
        this.fetchParkState()
      }
    } catch (error) {}
  },

  stoppingAction() {
    this.getStopping()
    this.getTempCarArrearTotal()
    //获取离场状态
    // this.fetchParkState()
    //获取图片显示状态
    const imageState = commonApi.getImageState(this.data.plateNo)
    this.setData({ imageState })
  },
})
