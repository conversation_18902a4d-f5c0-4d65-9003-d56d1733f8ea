var api = require('../../../../config/api.js');
var util = require('../../../../utils/util');
Page({

  /**
   * 页面的初始数据
   */
  data: {
    list:[],
    groupId:''
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.setData({
      groupId:options.groupId
    })
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    this.getList()
  },
  getList() {
    util.showLoading('正在加载…')
    var that = this;
    util.request(api.getMonthParks, {groupId:that.data.groupId}, 'GET').then(function (res) {
      if (res.code == '0') {
        var result = res.result;
        that.setData({
          list: result,
        });
        wx.hideLoading();
      } else {
        wx.hideLoading();
        util.showToast(res.message)
      }
    })
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})