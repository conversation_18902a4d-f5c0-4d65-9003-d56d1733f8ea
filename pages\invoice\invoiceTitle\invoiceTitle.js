var api = require('../../../config/api.js')
var util = require('../../../utils/util')
const app = getApp()
Page({
  data: {
    allowXY: false,
    pageHeight: 0,
    invoiceTitleType: '1',
    isShowPreviewBox: false,
    payBusiness: '',
    amount: 0,
    count: 0,
    bills: [], //发票申请订单编号
  },
  onLoad(options) {
    let height = app.globalData.windowHeight - app.globalData.CustomBar + 48 // 48是tabbar的高度
    var bills = []
    var that = this
    wx.getStorage({
      key: 'selectedList',
      success(res) {
        bills = res.data
        that.setData({
          bills: bills,
        })
        that.getInfo()
      },
    })
    this.setData({
      payBusiness: options.payBusiness,
      pageHeight: height,
    })
  },
  getInfo() {
    var that = this
    util.showLoading('正在加载…')
    util
      .request(
        api.proFormaInvoice,
        {
          bills: that.data.bills,
          payBusiness: that.data.payBusiness, //ARREARS表示停车订单，MONTHLY_PACKAGE表示包月套餐，RESERVATION_SPACE表示预约车位套餐
        },
        'POST'
      )
      .then(function (res) {
        if (res.code == '0') {
          wx.hideLoading()
          var result = res.result
          that.setData({
            amount: result.amount,
            count: result.count,
          })
          wx.setStorage({
            key: 'invoiceInfoVos',
            data: result.invoiceInfoVos,
          })
        }
      })
  },
  selectedTitleType(e) {
    let type = e.currentTarget.dataset.type
    this.setData({
      invoiceTitleType: type,
    })
  },
  gotoDetail() {
    wx.navigateTo({
      url: '/pages/invoice/invoiceDetail/invoiceDetail',
    })
  },
  preview() {},
  closePreviewBox() {
    this.setData({
      isShowPreviewBox: false,
    })
  },
  placeholderFun() {
    // 占位
  },
  formSubmit(e) {
    var data = this.data
    var param = e.detail.value
    if (param.invoiceTitle == '') {
      if (data.invoiceTitleType == '1') {
        util.showToast('请输入公司名称')
      } else {
        util.showToast('请输入抬头名称')
      }
    } else if ((data.invoiceTitleType == '1') & (param.taxpayerId == '')) {
      util.showToast('请输入公司税号')
    } else if (param.email == '') {
      util.showToast('请输入电子邮箱')
    } else if (!data.allowXY) {
      util.showToast('请阅读并同意平台服务协议及隐私协议')
    } else {
      this.setData({
        param: param,
        isShowPreviewBox: true,
      })
    }
  },
  onSubmit(e) {
    var that = this
    var data = that.data
    var param = data.param

    util.showLoading('正在加载…')
    util
      .request(
        api.addInvoice,
        {
          'bills': data.bills,
          'email': param.email,
          'invoiceTitle': param.invoiceTitle,
          'invoiceTitleType': data.invoiceTitleType,
          'payBusiness': data.payBusiness,
          'phone': param.phone,
          'remark': param.remark,
          'taxpayerAccount': param.taxpayerAccount,
          'taxpayerAddr': param.taxpayerAddr,
          'taxpayerBank': param.taxpayerBank,
          'taxpayerId': param.taxpayerId,
          'taxpayerTel': param.taxpayerTel,
        },
        'POST'
      )
      .then(function (res) {
        if (res.code == '0') {
          wx.hideLoading()
          util.showToast('提交成功！')
          var clock = setTimeout(function () {
            wx.redirectTo({
              url: '/pages/invoice/invoiceResult/invoiceResult',
            })
            clearInterval(clock)
          }, 500)
        } else {
          wx.hideLoading()
          util.showToast(res.message)
        }
      })
  },
  allow() {
    if (this.data.allowXY) {
      this.setData({
        allowXY: false,
      })
    } else {
      this.setData({
        allowXY: true,
      })
    }
  },
  viewAgreement: function (e) {
    var id = e.currentTarget.id
    wx.navigateTo({
      url: '/pages/common/viewAgreement/index?id=' + id,
      fail: function (res) {
        console.log(res)
      },
    })
  },
})
