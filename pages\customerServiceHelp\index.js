var app = getApp();
Page({
  data: {
    goPageRoute:'',//登录后需要跳转到的页面
    showLoginDialog: false,
    list: [{
        name: "反馈记录",
        id: "feedback",
        url: "/pages/customerServiceHelp/feedback/index?title=反馈记录&type=feedback",
        check: 'true',
        tiptype:'6'
      },
      {
        name: "停车订单申诉",
        id: "parkingBillAppeal",
        url: "/pages/customerServiceHelp/feedback/index?title=停车订单申诉&type=parkingBillAppeal",
        check: 'true',
        tiptype:'7'
      },
      {
        name: "问题咨询",
        id: "ProblemConsultation",
        url: "/pages/customerServiceHelp/subpage/problemstatement",
        check: 'true',
        tiptype:'8'
      }, {
        name: "联系方式",
        id: "CUSTOMERSERVICE",
        url: "/pages/customerServiceHelp/subpage/problemstatement",
        check: 'false',
        tiptype:''
      },
      {
        name: "在线客服",
        id: "OnlineService",
        url: "/pages/common/viewAgreement/index?id=customerServiceUrl",
        check: 'false',
        tiptype:''
      }
    ]
  },
  goModulePage(e) {
    var currentTarget = e.currentTarget;
    var id = currentTarget.id;
    var route = e.currentTarget.dataset.route + "?type=" + id;
    if ((e.currentTarget.dataset.check != "false") && app.globalData.userInfo.loginStatus != "LOGGED_IN") { //校验是否已登陆
      var tiptype = e.currentTarget.dataset.tiptype;
      this.setData({
        goPageRoute:route
      })
      console.log("登陆跳转设置的route"+route)  
      wx.navigateTo({//因为需要跳转的页面带有参数。所以就没有直接传需要跳转页面URL。
        url: "/pages/common/loginTip/loginTip?tiptype=" + tiptype 
      })
    } else {
      wx.navigateTo({
        url: route,
      })
    }
  },
  goPageMethod: function () {//登陆成功后跳转到点击登陆前选择的页面。
    wx.redirectTo({
      url: this.data.goPageRoute,
    })
  }
})