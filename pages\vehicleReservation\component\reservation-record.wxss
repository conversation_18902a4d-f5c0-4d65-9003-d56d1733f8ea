@import '/pages/components/recordDetail.wxss';

/* 块状 */
.details-box.reservation {
  margin: 20rpx;
  padding: 40rpx 50rpx 40rpx 50rpx;
  width: calc(100% - 40rpx);
  min-height: auto;
  border-radius: 5rpx;
  box-sizing: border-box;
  font-size: 26rpx;
  color: #353535;
}

/* 块状内容 */
.title-value-box {
  padding: 0;
  width: 100%;
}

/* 单行 */
.details-box.reservation .title-value-box-line {
  padding: 0;
  margin-bottom: 16rpx;
  width: 100%;
  height: 37rpx;
  border-width: 0;
}

.one {
  font-size: 34rpx;
  line-height: 37rpx;
  color: #353535;
  font-weight: bold;
}

.two {
  font-size: 34rpx;
  line-height: 37rpx;
  color: #353535;
  font-weight: 400;
}



/* 按钮 */
.title-value-box-btn {
  height: 56rpx;
  background: rgba(37, 107, 245, 0.1);
  border: 1rpx solid rgba(37, 107, 245, 0.2);
  font-size: 26rpx;
  color: #256BF5;
  line-height: 56rpx;
  margin-left: 0;
  width: 150rpx;
}

/* 左边标题 */
.title-value-box-line-title {
  width: 200rpx;
}

/* 右边内容 */
.title-value-box-line-value {
  width: calc(100% - 200rpx);
  text-align: right;
}

/* 底部提示语 */
.details-box.reservation .bottomTip {
  margin-top: 12rpx;
  width: 100%;
  text-align: center;
  height: 30rpx;
  font-size: 22rpx;
  color: #256BF5;
  line-height: 30rpx;
}

/* 预约状态，EFFECTIVE - 生效中,COMPLETED - 已完成,EXPIRED - 已失效, CANCELED - 已取消 */
.stateBox {
  background: rgba(126, 126, 126, 0.2);
  width: 100rpx;
  height: 40rpx;
  text-align: center;
  font-size: 26rpx;
  font-weight: 400;
  color: #7E7E7E;
  border-radius: 5rpx 5rpx 5rpx 5rpx;
}

.EFFECTIVE {
  color: #24BB8A;
  background: #D9F9EE;
}

.COMPLETED {
  color: #256BF5;
  background: rgba(37, 107, 245, 0.1);
}

.m0 {
  margin: 0 0 0 10rpx;
}

.m1 {
  margin-left: auto;
}

.rowView {
  flex-direction: row;
  display: flex;
  width: 100%;
}

.o {
  background: linear-gradient(90deg, #FF5745 0%, #FFAE00 100%);
  border: none;
  color: #FFFFFF;
}