const app = getApp()
Page({
  data: {
    pageHeight: 0,
  },
  onLoad(options) {
    let height = app.globalData.windowHeight - app.globalData.CustomBar + 48 // 48是tabbar的高度
    this.setData({
      pageHeight: height,
    })
  },
  goInvoicingHistory() {
    wx.redirectTo({
      url: '/pages/invoice/invoiceHistory/invoiceHistory',
    })
  },
  toInvoiceHome() {
    wx.navigateBack({
      delta: 2,
    })
  },
})
