<!--pages/supplementaryPayment/index.wxml-->
<cu-custom bgColor="white-bg" contentTitle="待付订单" isBack="{{isBack}}"></cu-custom>
<view wx:if="{{qflist.length==0}}">
  <image src="../../image/qfjf-null.png" class="nullImg"></image>
  <view class="nullTip">您还没有待付订单</view>
</view>
<!-- <scroll-view scroll-y="true">
  <block wx:for="{{qflist}}" wx:key="index" scroll-y="true">
    <view class="qf-listDiv">
      <view class="qf-listDiv-left">
        <view class="qf-listDiv-left-one">{{item.parkName}}</view>
        <view class="qf-listDiv-left-three">共{{item.parkPeriodTime}}</view>
      </view>
      <view class="qf-listDiv-right">
        <view class="qf-listDiv-right-one">需缴费</view>
        <view class="qf-listDiv-right-two">￥{{item.totalFee}}</view>
        <view class="qf-listDiv-right-three" bindtap="goxq" id="{{item.uniqueId}}">缴费</view>
      </view>
    </view>
  </block>
</scroll-view> -->
<scroll-view scroll-y="true">
  <block wx:for="{{qflist}}" wx:key="id">
    <view
      class="qf-listDiv"
      bindtap="changeCheckState"
      data-info="{{item}}"
      data-checkstate="{{item.checkstate}}"
      data-index="{{index}}"
    >
      <view class="left" hidden="{{mode == 'other'}}">
        <image src="/image/{{item.checkstate ? 'yes-icon-select' : 'yes-icon'}}.png" />
      </view>
      <view class="qf-listDiv-left">
        <view class="qf-listDiv-left-one">{{item.parkName}}</view>
        <view class="qf-listDiv-left-three">共{{item.parkPeriodTime}}</view>
      </view>
      <view class="qf-listDiv-right">
        <view class="qf-listDiv-right-one">需缴费</view>
        <view class="qf-listDiv-right-two">￥{{item.totalFee}}</view>
        <view class="qf-listDiv-right-three" catchtap="goxq" id="{{item.uniqueId}}">缴费</view>
      </view>
    </view>
  </block>
</scroll-view>
<view class="backWihte columnView" style="margin-top: 10rpx; height: 242rpx">
  <view class="info-bt">
    <view class="icon-sjx-left-tilte"></view>
    <view class="info-bt-text">支付方式</view>
  </view>
  <view class="paymentListBox">
    <block wx:for="{{payList}}" wx:key="index">
      <view
        id="{{item.payType}}"
        class="infoBox  {{selectPay == item.payType ? 'choosePay':''}}"
        bindtap="selectPayMode"
        wx:if="{{item.payType=='BALANCE_PAY'}}"
      >
        <view class="infoBox-text" style="margin-top: 14rpx">余额</view>
        <view class="infoBox-text" id="ye" data-money="{{item.iconUrl}}">{{item.iconUrl}}元</view>
      </view>
      <view
        id="{{item.payType}}"
        class="infoBox {{selectPay == item.payType ? 'choosePay':''}}"
        style="display: flex; flex-direction: column"
        bindtap="selectPayMode"
        wx:else
      >
        <image
          class="img"
          src="{{item.iconUrl}}"
          style="width: 48rpx; height: 48rpx; position: static; margin: 14rpx auto 0"
        ></image>
        <view class="infoBox-text">微信支付</view>
      </view>
    </block>
  </view>
</view>
<view class="footer rowView" hidden="{{currentInvoiceState == 'other'}}">
  <view class="checkbox" bindtap="batchSelected" data-type="all">
    <image src="/image/{{selectedList.length == qflist.length ? 'yes-icon-select' : 'yes-icon'}}.png" />全选
  </view>
  <view class="totalBox columnView">
    <view class="text">账单个数：{{selectedList.length}}个</view>
    <view class="text">补缴金额：￥{{invoiceAmout}}</view>
  </view>
  <view class="btn" bindtap="gotoInvoiceTitle">支付</view>
</view>
<loginDialog showModal="{{showLoginDialog}}" bind:refresh="goPage"></loginDialog>
