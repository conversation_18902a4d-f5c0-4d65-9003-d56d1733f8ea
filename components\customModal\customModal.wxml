<!--components/customModal/customModal.wxml-->
<view class="model" catchtouchmove="preventTouchMove" wx:if="{{isShow}}"></view>
<view
  class="custom-modal"
  catchtouchmove="preventTouchMove"
  wx:if="{{isShow}}"
  style="width: {{width}}; {{customStyle}}"
>
  <!-- 标题区域 -->
  <view class="custom-modal-header">
    <text class="custom-modal-title">{{title}}</text>
    <view class="custom-modal-close" wx:if="{{showClose}}" bindtap="closeModal">×</view>
  </view>

  <!-- 内容区域 -->
  <view class="custom-modal-content">
    <!-- 如果传入了content属性，则显示文本内容 -->
    <text wx:if="{{content}}" class="custom-modal-text">{{content}}</text>
    <!-- 插槽，用于自定义内容 -->
    <slot name="content"></slot>
  </view>

  <!-- 底部按钮区域 -->
  <view class="custom-modal-footer" wx:if="{{showFooter}}">
    <view class="custom-modal-btn cancel" bindtap="onCancel">{{cancelText}}</view>
    <view class="custom-modal-btn confirm" bindtap="onConfirm">{{confirmText}}</view>
  </view>

  <!-- 自定义底部插槽 -->
  <slot name="footer"></slot>
</view>
