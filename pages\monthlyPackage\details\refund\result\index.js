var util = require('../../../../../utils/util');
var api = require('../../../../../config/api');
Page({
  data: {
    id: "",
    refundFee: '',
    refundTime: '',
    refundChannel: '',
    parkName: '',
    plateNo: '',
    backFee: '',
    type: '',
  },
  onLoad(options) {
    var type = options.type;
    var id = options.id;
    this.setData({
      id: id,
      type:type,
    })
    if(type=='tk'){
      this.setValue(options.parkName, options.backFee,options.plateNo,options.refundTime,options.refundFee,options.refundChannel)
    }else{
      this.getInfo(id);
    }
  },
  onShow() {

  },
  getInfo:function(id){
    var that = this;
    util.request(api.getMonthlyPayDetail + "/" + id, {}, 'GET').then(function (res) {
      var options = res.result;
      if (res.code == '0') {
        that.setValue(options.parkName, options.payAmount,options.plateNo,options.payTime,options.payAmount,options.payTypeDesc,options.durationDesc,options.payTime)
        wx.hideLoading()
      } else {
        wx.hideLoading()
        util.showToast(res.message);
      }
    });
  },
  setValue:function(parkName, backFee,plateNo,refundTime,refundFee,refundChannel,durationDesc,payTime){
    this.setData({
      backFee: backFee,
      refundFee: refundFee,
      refundTime: refundTime,
      refundChannel: refundChannel,
      parkName: parkName,
      plateNo: plateNo,
      durationDesc:durationDesc,
      payTime:payTime
    })
  },
  back() {
    wx.navigateBack({
      delta: 2
    })
  },
  goMyPage() {
    wx.redirectTo({
      url:  "/pages/monthlyPackage/index"
    })
  }
})