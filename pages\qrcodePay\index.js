var util = require('../../utils/util')
var api = require('../../config/api.js')
var app = getApp()

Page({
  data: {
    parkCode: '', // 停车场编码
    loading: true,
    hasArrears: false, // 是否有欠费
    arrearsData: null, // 欠费数据
    noArrearsMessage: '恭喜您，当前停车场暂无欠费订单！',
    querySuccess: null, // 查询是否成功完成 null=未开始, true=成功, false=失败
    errorMessage: '', // 错误信息
  },

  onLoad(options) {
    console.log('扫码参数:', options)

    // 处理二维码扫码参数
    let parkCode = util.getURLParameters(decodeURIComponent(options.q)).parkCode

    if (!parkCode) {
      util.showToast('缺少停车场参数，请重新扫码')
      setTimeout(() => {
        wx.switchTab({
          url: '/pages/index/index',
        })
      }, 2000)
      return
    }

    this.setData({ parkCode })

    // 检查登录状态并查询欠费
    this.checkLoginAndQuery()
  },

  onShow() {
    // 如果是从登录页面返回，重新查询
    if (app.globalData.isLoginBack_mycar) {
      app.globalData.isLoginBack_mycar = false
      this.checkLoginAndQuery()
    }
  },

  // 检查登录状态并查询欠费
  checkLoginAndQuery() {
    // 检查用户状态
    this.checkUserStatus()
  },

  // 检查用户状态
  checkUserStatus() {
    util
      .request(api.getUserStatus, '', 'GET')
      .then(res => {
        if (res.code == '0') {
          const info = res.result
          if (info.userStatus == 'REGISTERED_BOUND') {
            // 已登录且绑定车牌，查询欠费
            this.queryArrears()
          } else {
            // 已登录未绑定车牌，跳转待付订单页面
            util.showToast('未绑定车牌，即将跳转待付订单页面...')
            setTimeout(() => {
              wx.navigateTo({
                url: '/pages/supplementaryPayment/index',
              })
            }, 1000)
          }
        } else if (res.code === '2007') {
          // 未登录
          this.goToLogin()
        } else {
          // 状态检查失败，提示用户
          console.log('🚀 ~ checkUserStatus ~ res:', res)
          util.showToast('获取用户信息失败，请稍后重试')
        }
      })
      .catch(err => {
        console.log('🚀 ~ checkUserStatus ~ err:', err)
        util.showToast('获取用户信息失败，请稍后重试')
      })
  },

  // 查询欠费订单
  queryArrears() {
    util.showLoading('正在查询欠费信息...')

    util
      .request(
        api.getArrearsList,
        {
          currentPage: 1,
          pageSize: 9999,
        },
        'GET'
      )
      .then(res => {
        wx.hideLoading()
        console.log('查询欠费')
        if (res.code == '0') {
          const records = res.result.records || []

          // 筛选当前停车场的未支付订单
          const currentParkArrears = records.filter(
            item => item.parkCode === this.data.parkCode && item.payStatus === 'UNPAID'
          )

          if (currentParkArrears.length > 0) {
            // 有欠费，取第一条记录跳转支付
            const arrearsItem = currentParkArrears[0]
            this.setData({
              loading: false,
              hasArrears: true,
              arrearsData: arrearsItem,
              querySuccess: true,
            })

            // 跳转到支付页面
            wx.redirectTo({
              url: `/pages/supplementaryPayment/details/index?uniqueId=${arrearsItem.id}`,
            })
          } else {
            // 无欠费，查找停车场名称
            this.getParkName()
          }
        } else {
          util.showToast(res.message || '查询失败')
          this.setData({
            loading: false,
            hasArrears: false,
            querySuccess: false,
            errorMessage: res.message || '查询失败，请重试',
          })
        }
      })
      .catch(error => {
        wx.hideLoading()
        this.setData({
          loading: false,
          hasArrears: false,
          querySuccess: false,
          errorMessage: '网络异常，请检查网络连接后重试',
        })
        console.error('查询欠费失败:', error)
      })
  },

  // 获取停车场名称
  getParkName() {
    // 尝试从已有的欠费记录中找到停车场名称
    util
      .request(
        api.getArrearsList,
        {
          currentPage: 1,
          pageSize: 100,
        },
        'GET'
      )
      .then(res => {
        if (res.code == '0') {
          const records = res.result.records || []
          const parkRecord = records.find(item => item.parkCode === this.data.parkCode)

          let message = '恭喜您，当前停车场暂无欠费订单！'
          if (parkRecord && parkRecord.parkName) {
            message = `恭喜您，在【${parkRecord.parkName}】暂无欠费订单！`
          }

          this.setData({
            loading: false,
            hasArrears: false,
            querySuccess: true,
            noArrearsMessage: message,
          })
        } else {
          this.setData({
            loading: false,
            hasArrears: false,
            querySuccess: true,
          })
        }
      })
      .catch(() => {
        this.setData({
          loading: false,
          hasArrears: false,
          querySuccess: true,
        })
      })
  },

  // 跳转到登录页面
  goToLogin() {
    this.setData({
      loading: false,
      querySuccess: null,
      errorMessage: '',
    })

    // 设置返回标记
    app.globalData.isLoginBack_mycar = true

    wx.navigateTo({
      url:
        '/pages/common/loginTip/loginTip?route=' +
        encodeURIComponent(`/pages/qrcodePay/index?parkCode=${this.data.parkCode}`),
    })
  },

  // 返回首页
  goHome() {
    wx.switchTab({
      url: '/pages/index/index',
    })
  },

  // 重新查询
  retryQuery() {
    this.setData({
      loading: true,
      querySuccess: null,
      errorMessage: '',
      hasArrears: false,
    })
    this.checkLoginAndQuery()
  },

  // 页面方法，供登录页面调用
  goPageMethod() {
    this.checkLoginAndQuery()
  },
})
