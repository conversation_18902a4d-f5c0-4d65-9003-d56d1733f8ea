var util = require('../../../utils/util');
var api = require('../../../config/api.js');
Page({
  data: {
    info:{},
    id: "",//预约订单ID
    resInfo: {},
    showSucPage: false,
  },
  onLoad(options) {
    this.setData({
      id:options.id
    })
    this.getRecordData()
  },
  getRecordData: function () {
    var that = this;
    util.showLoading('正在加载…')
    util.request(api.getReservationInfo+"/"+that.data.id, "", 'GET').then(function (res) {
      wx.hideLoading();
      let {
        code,
        result
      } = res || {}
      if (code == '0') {
        that.setData({
          info:result
        })
      } else {
        util.showToast(res.message);
      }
    })

  },
  srue() {
    var that = this;
    util.showLoading('正在操作…')
    util.request(api.cancelReservation + "/" + that.data.id, {}, 'DELETE').then(function (res) {
      wx.hideLoading();
      let {
        code,
        result,
        message
      } = res || {}
      if (code == '0') {
        wx.hideLoading()
        that.setData({
          resInfo: result,
          showSucPage: true
        })
      } else {
        wx.hideLoading()
        util.showToast(message);
      }
    })
  },
  goBack() {
    var pages = getCurrentPages();
    var prevPage = pages[pages.length - 2]; //上一个页面
    prevPage.setData({
      btnType: "Completed"
    })
    wx.navigateBack();
  }
})