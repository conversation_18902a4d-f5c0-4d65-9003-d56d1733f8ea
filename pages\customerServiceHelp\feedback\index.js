var util = require('../../../utils/util')
var api = require('../../../config/api.js')
Page({
  data: {
    title: '',
    type: '',
    list: [],
    currentPage: 1,
    pageSize: 50,
    statusClass: {
      UNPAID: 'red',
      PAID: 'green',
      NO_PAYMENT: 'blue',
      PAYING: 'blue',
    }, //停车订单(UNPAID表示待支付，PAYING表示支付中，PAID表示已完成)
  },
  onLoad(options) {
    this.setData({
      title: options.title,
      type: options.type,
    })
    this.getLis()
  },
  onShow() {},
  goXq: function (e) {
    var id = e.currentTarget.id
    var type = this.data.type
    var dataset = e.currentTarget.dataset
    var recordBizType = dataset.recordtype
    if (recordBizType == 2) {
      //停车申诉订单详情
      type = 'parkingAppealOrderDetails'
    }
    var url = '/pages/customerServiceHelp/subpage/problemstatement?type=' + type + '&id=' + id
    if (type == 'parkingBillAppeal') {
      var parkName = dataset.name
      var plateNo = dataset.p
      var time = dataset.time
      var timeDesc = dataset.timed
      var amount = dataset.amount
      var status = dataset.status
      var uniqueId = dataset.uniqueid
      var parkCode = dataset.parkcode
      var endTime = dataset.endtime
      url =
        '/pages/customerServiceHelp/subpage/parkingOrderAppeal?type=' +
        type +
        '&id=' +
        id +
        '&parkName=' +
        parkName +
        '&plateNo=' +
        plateNo +
        '&time=' +
        time +
        '&timeDesc=' +
        timeDesc +
        '&amount=' +
        amount +
        '&status=' +
        status +
        '&uniqueId=' +
        uniqueId +
        '&parkCode=' +
        parkCode +
        '&endTime=' +
        endTime
    }
    wx.navigateTo({
      url: url,
    })
  },
  getLis: function () {
    var type = this.data.type
    if (type == 'feedback') {
      this.getFeedbackList() //获取反馈记录
    } else {
      //parkingBillAppeal
      this.getParkingBillAppealList() //查停车订单申诉
    }
  },
  getParkingBillAppealList: function () {
    var that = this
    util.showLoading('正在加载…')
    var currentPage = that.data.currentPage
    util
      .request(
        api.getAppealList,
        {
          'pageSize': that.data.pageSize,
          'currentPage': currentPage,
        },
        'GET'
      )
      .then(function (res) {
        if (res.code == '0') {
          wx.hideLoading()
          var records = res.result.records
          if (records.lenght == 0) {
            if (currentPage == 1) {
              that.setData({
                list: [],
              })
            }
          } else {
            that.setData({
              list: that.data.list.concat(records),
            })
          }
          that.setData({
            currentPage: currentPage + 1,
          })
        } else {
          wx.hideLoading()
          util.showToast(res.message)
        }
      })
  },
  getFeedbackList: function () {
    var that = this
    util.showLoading('正在加载…')
    var currentPage = that.data.currentPage
    util
      .request(
        api.getCustomerServiceAssistList,
        {
          'pageSize': that.data.pageSize,
          'currentPage': currentPage,
        },
        'GET'
      )
      .then(function (res) {
        if (res.code == '0') {
          wx.hideLoading()
          if (res.result.total == 0) {
            if (currentPage == 1) {
              that.setData({
                list: [],
              })
            }
          } else {
            var records = res.result.records
            that.setData({
              list: that.data.list.concat(records),
            })
          }
          that.setData({
            currentPage: currentPage + 1,
          })
        } else {
          wx.hideLoading()
          util.showToast(res.message)
        }
      })
  },
  bindReachBottom: function () {
    console.log('上拉加载....')
    var that = this
    var data = that.data
    if (data.showNullMoreTip) {
      util.showToast('没有更多数据了')
    } else {
      that.getLis()
    }
  },
})
