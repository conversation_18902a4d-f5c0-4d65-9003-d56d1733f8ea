var util = require('../../..//utils/util');
var api = require('../../../config/api.js');
Page({
  data: {
    pageSize:100,
    currentPage:1,
    list:[]
  },
  onLoad(options) {

  },
  onShow() {
    this.getList();
  },
  getList: function () {
    var that = this;
    util.showLoading('正在加载…')
    var currentPage = that.data.currentPage;
    util.request(api.gettRechargeBargains, {
      "pageSize": that.data.pageSize,
      "currentPage": currentPage,
    }, 'GET').then(function (res) {
      if (res.code == '0') {
        wx.hideLoading();
        if (res.result.total == 0) {
          if (currentPage == 1) {
            that.setData({
              list: [],
            });
          } 
        } else {
          var records = res.result.records;
          that.setData({
            list: that.data.list.concat(records)
          });
        }
        that.setData({
          currentPage: currentPage + 1,
        });
      } else {
        wx.hideLoading();
        util.showToast(res.message)
      }
    });
  },
  bindReachBottom: function () {
    console.log("上拉加载....");
    var that = this;
    var data = that.data;
    if (data.showNullMoreTip) {
      util.showToast('没有更多数据了')
    } else {
      that.getList();
    }
  }
})