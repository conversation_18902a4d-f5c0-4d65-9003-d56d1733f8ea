@import "../../common/common.wxss";
@import "../invoiceTitle/invoiceTitle.wxss";

.listDiv {
  background: #FFFFFF;
  box-shadow: 0rpx 10rpx 0rpx 1rpx rgba(228, 234, 248, 0.16);
  opacity: 1;
  margin: 20rpx 20rpx 0 20rpx;
  padding: 15rpx 30rpx;
}

.listDiv-name {
  flex: 1;
  font-size: 30rpx;
  font-weight: bold;
  color: #353535;
  line-height: 56rpx;
  height: 56rpx;
}

.icon-jt-left {
  margin: auto 20rpx;
}

.listDiv-time {
  flex: 1;
  height: 56rpx;
  font-size: 28rpx;
  font-weight: 400;
  color: #7E7E7E;
  line-height: 56rpx;
}

.listDiv-amount {
  min-width: 91rpx;
  height: 56rpx;
  font-size: 34rpx;
  font-weight: bold;
  color: #353535;
  line-height: 56rpx;
}

.line {
  border-top: 1rpx solid #DFDFDF;
}

.btn {
  width: 160rpx;
  height: 56rpx;
  background: #E8F0FE;
  opacity: 1;
  border: 1rpx solid rgba(37, 107, 245, 0.2);
  font-size: 26rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #256BF5;
  line-height: 56rpx;
  border-radius: 30rpx;
  margin-top: 20rpx;
  text-align: center;
}
.m{
  margin-top: 20rpx;
}
.preview-box .container {
  height: 549rpx;
}