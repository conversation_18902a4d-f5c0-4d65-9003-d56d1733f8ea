// pages/common/loginTip/loginTip.js
var api = require('../../../config/api.js')
var util = require('../../../utils/util')
var app = getApp()
Page({
  /**
   * 页面的初始数据
   */
  data: {
    allowXY: false, //是否勾选了协议的按钮
    type: 1, //1代付订单、订单查询、全部订单2优惠劵3余额充值、充值记录、线上充值4我的积分5我的等级6客服帮助-反馈提示7、客服帮助-停车订单申诉8、客服帮助-问题咨询
    route: '', //登陆后跳转链接
    isShowLongin: false,
    isShowAuth: false,
    imgUrl: api.imgUrl,
    title: {
      0: '登录提示',
      1: '登录提示',
      2: '优惠卡卷',
      3: '充值记录',
      4: '我的积分',
      5: '我的等级',
      6: '反馈记录',
      7: '停车订单申诉',
      8: '问题咨询',
    },
    tip: {
      0: '欢迎使用畅行桂林智慧停车系统',
      1: '因订单属于个人账户下信息，为保护您的信息安全，请登录后查看',
      2: '因优惠卡券属于个人账户下信息，为保护您的信息安全，请登录后查看',
      3: '因余额及充值属于个人账户下信息，为保护您的信息安全，请登录后查看',
      4: '因积分属于个人账户下信息，为保护您的信息安全，请登录后查看',
      5: '因等级属于个人账户下信息，为保护您的信息安全，请登录后查看',
      6: '因反馈记录属于个人账户下信息，为保护您的信息安全，请登录后查看',
      7: '因停车订单申诉属于个人账户下信息，为保护您的信息安全，请登录后查看',
      8: '因问题咨询属于个人账户下信息，为保护您的信息安全，请登录后查看',
    },
    authPid: '',
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.setData({
      route: options.route === undefined ? '' : options.route,
      type: options.tiptype === undefined ? '' : options.tiptype,
    })
    if (!wx.getStorageSync('token')) {
      util.fetchToken()
      util.fetchTokenSC()
    }
  },

  refresh: function () {
    console.log('🚀 ~ this.data.route:', this.data.route)
    if (this.data.route != '') {
      //从跳转页面的url中获取不到参数，因此在处理携带参数的跳转链接使用调用页面的方法，页面自行进行跳转
      wx.redirectTo({
        url: decodeURIComponent(this.data.route),
      })
    } else {
      const pages = getCurrentPages()
      const prevPage = pages[pages.length - 2]
      prevPage.goPageMethod()
    }
  },
  login: function () {
    this.setData({
      isShowLongin: true,
    })
  },
  sqbd() {
    util.showToast('请阅读并同意服务协议、隐私政策协议及登录政策')
  },
  getPhoneNumber: function (e) {
    var that = this
    util.showLoading('正在加载…')
    if (e.detail.errMsg == 'getPhoneNumber:ok') {
      util
        .request(
          api.login,
          {
            code: e.detail.code,
          },
          'POST'
        )
        .then(function (res) {
          if (res.code == '0') {
            //成功
            var result = res.result
            app.globalData.userInfo = result
            wx.setStorageSync('token', result.token)
            wx.hideLoading()
            that.refresh()
          } else {
            wx.hideLoading()
            util.showToast(res.message)
          }
        })
    } else {
      this.setData({
        showModal: false,
      })
      wx.hideLoading()
      util.showToast('申请获取手机号失败！')
    }
  },
  viewAgreement: function (e) {
    var id = e.currentTarget.id
    wx.navigateTo({
      url: '/pages/common/viewAgreement/index?id=' + id,
      fail: function (res) {
        console.log(res)
      },
    })
  },
  allow() {
    if (this.data.allowXY) {
      this.setData({
        allowXY: false,
      })
    } else {
      this.setData({
        allowXY: true,
      })
    }
  },
  goBack() {
    wx.navigateBack()
  },

  onIdentityLogin() {
    if (!this.data.allowXY) {
      this.sqbd()
      return
    }

    const bizSeq = util.generateSeq()
    const that = this

    wx.ncidas({
      orgID: '00000158',
      businessID: '0001',
      bizSeq,
      type: 0,
      async success(res) {
        console.log('🚀 ~ ncidas:', res)
        const idCardAuthData = res.idCardAuthData
        if (!idCardAuthData) {
          util.showToast(res.resultDesc)
          return
        }
        const { result } = await util.request(api.cxglAuthRequest, { bizSeq, idCardAuthData }, 'post')
        if (result?.cxglLoginToken) {
          //有token信息，直接登录
          wx.setStorageSync('token', result.cxglLoginToken)
          that.refresh()
        } else {
          //无token信息，执行登录流程，然后绑定token
          that.setData({ authPid: result.authPid, isShowAuth: true })
        }
      },
      fail(err) {
        console.log('wx.ncidas--fail:', err)
      },
    })
  },

  onVerificationCode() {
    this.setData({
      isShowLongin: true,
    })
  },
})
