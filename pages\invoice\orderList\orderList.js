var api = require('../../../config/api.js');
var util = require('../../../utils/util');
const app = getApp()
Page({
  data: {
    orderTypes: [{
      name: '停车订单',
      id: 'ARREARS'
    }
    // , {
    //   name: '预约订单',
    //   id: 'RESERVATION_SPACE'
    // }
    , {
      name: '包月订单',
      id: 'MONTHLY_PACKAGE'
    }],
    currentOrderType: '',
    batchType: '',
    list: [],
    selectedList: [],
    bills: [], //发票申请订单编号
    invoiceAmout: 0,
    showNullTip: false,
    pageSize: 100,//产品要求是100条
    currentPage: 1,
    showNullMoreTip: false,
  },
  onLoad(options) {
    let type = options['type']
    this.setData({
      currentOrderType: type
    })
  },
  onShow(e) {
    this.setData({
      list: [],
      selectedList:[],
      invoiceAmout:0,
      currentPage: 1,
    })
    this.getList()
  },
  /* 切换订单类型 */
  changeOrderType(e) {
    let type = e.currentTarget.dataset.type;
    this.setData({
      currentPage: 1,
      currentOrderType: type,
      batchType: '',
      list: [],
      selectedList: [],
      invoiceAmout: 0
    })
    this.getList()
  },
  /* 筛选可开票和不可开票订单 */
  changeInvoiceState(e) {
    let state = e.currentTarget.dataset.state;
    this.setData({
      list: []
    })
    this.getList()
  },
  /* 加载数据 */
  getList: function () {
    var that = this;
    util.showLoading('正在加载…')
    var currentPage = that.data.currentPage;
    util.request(api.getInvoiceList, {
      payBusiness: that.data.currentOrderType, //ARREARS表示停车订单，MONTHLY_PACKAGE表示包月套餐，RESERVATION_SPACE表示预约车位套餐
      pageSize: that.data.pageSize,
      currentPage: currentPage,
    }, 'GET').then(function (res) {
      if (res.code == '0') {
        wx.hideLoading();
        if (res.result.total == 0) {
          that.setData({
            list: [],
            showNullTip: true
          });
        } else {
          var records = res.result.records;
          that.setData({
            list: that.data.list.concat(records),
            showNullTip: false
          });
          if (res.result.pages == currentPage) {
            //没有数据
            that.setData({
              showNullMoreTip: true
            });
          }
        }
        //加载下一页
        that.setData({
          currentPage: currentPage + 1,
        });
      } else {
        wx.hideLoading();
        util.showToast(res.message)
      }
    });
  },
  /* 切换批量选择类型 本月和全部 */
  batchSelected(e) {
    let type = e.currentTarget.dataset.type;
    if (type == this.data.batchType) {
      type = ''
    }
    this.setData({
      batchType: type
    })
    let list = this.data.list;
    if (type == 'all') {
      var arry = [];
      for (let i = 0; i < list.length; i++) {
        list[i].checkstate = true;
        arry.push(list[i])
      }
      let invoiceAmout = this.calculateAmout(list);
      this.setData({
        selectedList:arry,
        invoiceAmout:invoiceAmout
      })
    } else if (type == '') {
      for (let i = 0; i < list.length; i++) {
        list[i].checkstate = false;
      }
      this.setData({
        selectedList:[],
        invoiceAmout:0
      })
    } else if (type == 'month') {
      //本月全选暂时不做
    }
    this.setData({
      list
    })
  },
  gotoInvoiceTitle() {
    var data = this.data;
    var selectedList = data.selectedList;
    if (selectedList.length < 1) {
      util.showToast("请勾选需要开具发票的订单")
    } else {
      wx.setStorage({
        key: "selectedList",
        data: selectedList
      })
      wx.navigateTo({
        url: '/pages/invoice/invoiceTitle/invoiceTitle?payBusiness=' + data.currentOrderType,
      })
    }
  },
  /* 当item被选择时 */
  changeCheckState(e) {
    var checkState,
      info;
    var dataset = e.currentTarget.dataset;
    let selectedList = this.data.selectedList;
    info = dataset.info;
    checkState = dataset.checkstate;
    if (checkState == undefined) {
      checkState = false;
    }
    if (!checkState) {
      checkState = true;
      selectedList.push(info);
    } else {
      checkState = false;
      selectedList = this.findDataInSelectedList(selectedList, info)
    }
    let invoiceAmout = this.calculateAmout(selectedList);
    console.log(invoiceAmout)
    this.setData({
      selectedList,
      invoiceAmout
    })
    var index = e.currentTarget.dataset.index
    this.setData({
      ['list[' + index + '].checkstate']: checkState
    })
  },
  findDataInSelectedList(list, data) {
    if (!list) {
      return list;
    }
    list.splice(list.findIndex(item => item.billNo === data.billNo), 1)
    return list;
  },
  /* 根据list计算金额 */
  calculateAmout(list) {
    console.log("list："+JSON.stringify(list))
    if (!list) {
      return '0.00';
    }
    let amount = list.reduce((value, item) => value + parseFloat(item['invoiceAmount']), 0); //求和
    console.log("没有四舍五入之前："+amount)
    return (+amount).toFixed(2).replace(/^-?\d+/g, item => item.replace(/(?=(?!\b)(\d{3})+$)/g, ','))
  },
  bindReachBottom: function () {
    console.log("上拉加载....");
    var that = this;
    var data = that.data;
    if (data.showNullMoreTip) {
      util.showToast('没有更多数据了')
    } else {
      that.getList();
    }
  }

})