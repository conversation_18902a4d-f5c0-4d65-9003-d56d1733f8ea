/*模态框*/
/*使屏幕变暗  */
.background_screen {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  background: #000;
  opacity: 0.6;
  overflow: hidden;
  z-index: 1000;
  color: #fff;
}

/*对话框 */
.attr_box {
  background: #FFFFFF;
  opacity: 1;
  /* border-radius: 0px 0px 0px 0px; */
  /* height: 500rpx; */
  height: auto;
  width: 100%;
  overflow: hidden;
  position: fixed;
  bottom: 0;
  left: 0;
  z-index: 2000;
  background: #fff;
  /* background: rgba(66, 66, 66, .6); */
  padding-top: 30rpx;
  padding-bottom: 60rpx;
  box-sizing: border-box;

}


.dialog-box {
  width: 100%;
  height: 100%;
  /* background-color: pink; */
}

.dialog-head {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  height: 60rpx;
  /* background-color: rgb(215, 255, 192); */
}

.dialog-title {
  width: 80%;
  height: 48rpx;
  font-size: 34rpx;

  font-weight: bold;
  color: #353535;
  line-height: 54rpx;
  text-align: left;
}

.close2ImgBox {
  width: 10%;
  height: 100%;
  display: flex;
  align-items: center;
}

.close2Img {
  width: 25rpx;
  height: 25rpx;
}

.dialog-content {
  height: calc(100% - 60rpx);
  box-sizing: border-box;
}

/* 主体内容 */
.select-box {
  /* background-color: rgb(240, 230, 146); */
  display: flex;
  flex-wrap: wrap;
  justify-content: start;
  box-sizing: border-box;
  padding: 10rpx;
  padding: 0 0 30rpx 0rpx;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.select-item {
  width: 750rpx;
  height: 88rpx;
  background: #FFFFFF;
  opacity: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  border-bottom: 1rpx solid #dbdbdb;
}

.selectedItem {
  color: #256BF5;
}

.disable {
  color: #A3A3A3;
}