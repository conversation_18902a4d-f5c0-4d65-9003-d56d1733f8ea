<view class="saveOutView">
  <cu-custom bgColor="white-bg" contentTitle="选择{{name[type]}}订单" isBack="true"></cu-custom>
  <view wx:if="{{ list.length == 0 }}">
    <image src="../../../../image/qfjf-null.png" class="nullImg"></image>
    <view class="nullTip">您还没有{{name[type]}}订单</view>
  </view>
  <scroll-view wx:else scroll-y="true">
    <block wx:for="{{list}}" wx:key="index">
      <view class="listDiv-d" bindtap="select"  style="{{index==0 ?'margin-top:20rpx':''}}">
        <view class="listDiv-d-left">
          <view class="listDiv-search-name ">{{item.parkName}}</view>
          <view class="listDiv-search-name-xq">{{item.plateNo}}</view>
          <view class="listDiv-search-name-xq rowView">
              <view> {{item.timeDesc}}/</view>
              <view class="listDiv-search-dc-tip" wx:if="{{type=='monthlyConsultation'}}">剩余{{item.freeTimeDesc}}天</view>
          </view>
          <view class="state {{statusClass[item.status]}}">{{statusName[item.status]}}</view>
        </view>
      </view>
    </block>
  </scroll-view>
</view>