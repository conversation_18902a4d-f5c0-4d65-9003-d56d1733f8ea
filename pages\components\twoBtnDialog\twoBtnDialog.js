var util = require('../../../utils/util');
var api = require('../../../config/api.js');
Component({
  /**
   * 组件的一些选项
   */
  options: {
    addGlobalClass: true
  },
  /**
   * 组件的属性列表，父组件传值过来要在这设置
   */
  properties: {
    title: {
      type: String,
      default: "提示"
    },
    leftBtnText: {
      type: String,
      default: "取消"
    },
    rightBtnText: {
      type: String,
      default: "去添加"
    },
    tipText: {
      type: String,
      default: "请添加车牌号"
    },
    showModal: {
      type: Boolean,
      default: false,
    }
  },
  /**
   * 组件的初始数据
   */
  data: {
  },
  /**
   * 组件的方法列表
   */
  methods: {
    back() {
      this.setData({
        showModal: false
      })
    },
    allow() {
      if (this.data.allowXY) {
        this.setData({
          allowXY: false
        })
      } else {
        this.setData({
          allowXY: true
        })
      }
    },
    leftBtn() {
      //调用父组件方法
      this.triggerEvent("leftGo")
      this.setData({
        showModal: false
      })
    },
    rightBtn() {
      this.triggerEvent("rightGo")
      this.setData({
        showModal: false
      })
    }
  }
})