<view class='saveOutView'>
  <cu-custom bgColor="white-bg" contentTitle="车辆认证" isBack="true"></cu-custom>

  <scroll-view class="scrollViewSa" scroll-y="true">
    <form catchsubmit="formSubmit">
      <view class="carBox">
        <text style="margin: 0 40rpx 0 30rpx;">认证车牌</text><text>{{carNum}}</text>
      </view>
      <view class="carBox columnView">
        <view class="form-row">
          <view class="form-label ">车主姓名</view>
          <view class="form-value">
            <input id="name" name="name" value="{{name}}" class="input" type="text" placeholder="请输入姓名" bindinput="bindKeyInput" />
          </view>
        </view>
        <view class="form-row">
          <view class="form-label ">手机号码</view>
          <view class="form-value">
            <input id="phone" name="phone" value="{{phone}}" class="input" type="text" placeholder="请输入手机号码" bindinput="bindKeyInput" />
          </view>
        </view>
        <view class="form-row" style="border: none;">
          <view class="form-label ">身份证号</view>
          <view class="form-value">
            <input id="identification" name="identification"  value="{{identification}}" class="input" type="text" placeholder="请输入身份证号" bindinput="bindKeyInput" />
          </view>
        </view>
      </view>
      <view class="carBox p">
        <view class="title">行驶证与车辆照片</view>
        <view class="wordBox">
          <view bindtap='showActionSheet' id='frontFile' style="background-image: url({{frontFile_sl}});background-size:100% 100%;background-repeat:no-repeat;background-position: center;" class="imgBox">
            <image src="../../../../../image/photo.png" class="img"></image>
            <view class="textTip">点击上传</view>
          </view>
          <view class="titleBox">行驶证正面</view>
        </view>
        <view class="wordBox">
          <view bindtap='showActionSheet' id='contraryFile' style="background-image: url({{contraryFile_sl}});background-size:100% 100%;background-repeat:no-repeat;background-position: center;" class="imgBox">
            <image src="../../../../../image/photo.png" class="img"></image>
            <view class="textTip">点击上传</view>
          </view>
          <view class="titleBox">行驶证副面</view>
        </view>
        <view class="wordBox">
          <view bindtap='showActionSheet' id='vehicleFile' style="background-image: url({{vehicleFile_sl}});background-size:100% 100%;background-repeat:no-repeat;background-position: center;" class="imgBox">
            <image src="../../../../../image/photo.png" class="img"></image>
            <view class="textTip">点击上传</view>
          </view>
          <view class="titleBox">车头照片</view>
        </view>
      </view>
      <view class='tip'>要求照片四角对齐，文字清晰，无反光遮挡，如有模糊、太暗，可能会导致认证失败。</view>
      <view class='rowView' style="padding-left: 40rpx;">
        <image class='yes_icon' wx:if="{{!allowXY}}" src="../../../../../image/yes-icon.png" bindtap='allow'></image>
        <image class='yes_icon' wx:if="{{allowXY}}" src="../../../../../image/yes-icon-select.png" bindtap='allow'></image>
        <text class='text'>我同意桂林盛才人力科技有限公司使用我所提交的信息用于核实车辆信息，以此提供该车辆的缴费、进/出场通知服务。<text bindtap="viewAgreement" id='serviceUrl' class="xy">《服务协议》</text>及<text bindtap="viewAgreement" id='privacyUrl' class="xy">《软件注册及隐私政策协议》</text></text>
      </view>
      <view class="box_convention_button">
        <button class="convention_button {{allowXY&&(name!='')&&(phone!='')&&(vehicleFile!='')&&(identification!='')&&(frontFile!='')&&(contraryFile!='')?'':'noClike'}}" formType="submit">提交审核 </button>
      </view>
    </form>
  </scroll-view>
</view>