<!--pages/parkingSpaceInquiry/index.wxml-->
<view style="width: {{swidth}}px; height: {{sheight}}px;">
  <map id="myMap" class="map" style="height: 100%;width: 100%;" latitude="{{latitude}}" longitude="{{longitude}}" scale="{{scale}}" markers="{{markers}}" bindmarkertap="markertap" show-location="true" bindtap='modifyLocation' bindcallouttap="markertap">
    <cover-view slot="callout">
    <block wx:for="{{markers}}" wx:key="this">
      <cover-view class="customCallout rowView" marker-id="{{item.id}}" wx:if="{{item.dataSource=='HIK'&&item.type == 'CHARGE'}}">
        <cover-image class="icon" wx:if="{{item.bubbleIcon}}" src="{{item.bubbleIcon}}"></cover-image>
         <cover-view class="content" ><!--华为充电桩和海康充电桩统称智慧充电桩都是显示气泡加余位、总数 -->
          快充 {{item.surplus}}/{{item.total}}
        </cover-view>
      </cover-view>
    </block>
  </cover-view>
    <view bindtap="goBack" class="zhtc-back" style="top:{{jnTop}}rpx;">
      <image src="../../../image/zhtc-back.png" style="width: 35rpx;height: 35rpx;"></image>
    </view>
    <view class="view-search" style="top:calc(70rpx + {{jnTop}}rpx) ">
      <view class="view-search-d flex-wrp" bindtap="goSearchPage">
        <image class="Icon-search" src="../../../image/merry-search.png"></image>
        <view class="view-search-text flex-wrp">
          {{keyword}}</view>
      </view>
    </view>
    <view class="view-search-left-dw" style="bottom:calc( 60rpx)" bindtap="setCenter">
      <image style="width: 44rpx;height: 44rpx;margin: 15rpx;" src="../../../image/button_dinwei.png"></image>
    </view>
  </map>
</view>
<view class="listDiv columnView" style="height: {{listDivHeight}};">
  <!-- <view style="height: 47rpx;" catchtap="addListHeight">
    <view class="moveBtn"></view>
  </view> -->
  <view class="tool-tip" style="margin-top: 31rpx;">
    <image src="../../../image/iMarker-min.png"></image>起点为当前地图所选位置
  </view>
  <scroll-view scroll-y="true" class="{{listDivHeight  == '501rpx' ? 'scroll-view_H' : 'scroll-view_H_Open'}}" bindscrolltolower="bindReachBottom">
    <block wx:for="{{pslist}}" wx:key="index" scroll-y="true">
      <view class="listDiv-d" id="{{item.id}}" data-type="{{item.type}}" bindtap="goxq" >
        <view class="listDiv-d-left">
          <view class="rowView">
            <view class="listDiv-search-name ">{{item.name}}</view>
          </view>
          <view class="listDiv-search-name-xq">{{item.address}}</view>
          <view class="listDiv-search-dc" wx:if="{{item.dataSource=='HIK'&&filter == 'CHARGE'}}">
            <view class="listDiv-search-dc-num flex-wrp">
              <view class="listDiv-search-yw flex-wrp" >余位 {{item.surplus}}/</view>
              <view class="listDiv-search-dc-zw" >{{item.total}}</view>
            </view>
          </view>
        </view>
        <view class="listDiv-d-right">
          <view class="listDiv-d-dhBtn" catchtap="openMapApp" data-lat="{{item.latitude}}" data-long="{{item.longitude}}" data-name="{{item.name}}" data-address="{{item.address}}">
            <image src="../../../image/dh.png"></image>
            <text>导航</text>
          </view>
          <view class="listDiv-search-dw">{{item.distance}}km</view>
        </view>
      </view>
    </block>
    <view wx:if="{{showNullTip}}" class="njgtip">无符合查询条件数据</view>
    <view wx:if="{{showNullMoreTip}}" class="njgtip">没有更多信息了</view>
  </scroll-view>
</view>