@import "../loginDialog/loginDialog.wxss";
@import "../verificationCode/verificationCode.wxss";

/*模态框*/
/*使屏幕变暗  */
.background_screen {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  background: #000;
  opacity: 0.6;
  overflow: hidden;
  z-index: 1000;
  color: #fff;
}

/*对话框 */
.attr_box {
  background: #FFFFFF;
  opacity: 1;
  /* border-radius: 0px 0px 0px 0px; */
  /* height: 500rpx; */
  height: auto;
  width: 100%;
  overflow: hidden;
  position: fixed;
  bottom: 0;
  left: 0;
  z-index: 2000;
  background: #fff;
  /* background: rgba(66, 66, 66, .6); */
  padding-top: 30rpx;
  padding-bottom: 60rpx;
  box-sizing: border-box;

}


.dialog-box {
  width: 100%;
  height: 1032rpx;
  /* background-color: pink; */
}

.dialog-head {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  height: 60rpx;
  /* background-color: rgb(215, 255, 192); */
}

.dialog-title {
  width: 80%;
  height: 48rpx;
  font-size: 34rpx;
  font-weight: bold;
  color: #353535;
  line-height: 54rpx;
  text-align: center;
}

.close2ImgBox {
  width: 10%;
  height: 100%;
  display: flex;
  align-items: center;
}

.close2Img {
  width: 25rpx;
  height: 25rpx;
}

.dialog-content {
  height: calc(100% - 60rpx);
  box-sizing: border-box;
}

/* 主体内容 */

/* 常规按钮样式 */
.convention_button {
  /* 通用渐变蓝绿大按钮 */
  text-align: center;
  border-bottom: 10px;
  font-size: 34rpx;
  font-weight: 400;
  color: #FFFFFF;
  line-height: 40rpx;
  width: calc(100% - 150rpx) !important;
  height: 80rpx;
  background: linear-gradient(90deg, #458BFF 0%, #08D7AE 100%);
  box-shadow: 0rpx 3rpx 10rpx 1rpx rgba(37, 107, 245, 0.2);
  border-radius: 40rpx 40rpx 40rpx 40rpx;
  margin-bottom: 20rpx;
}

.noClike {
  background: #C0C0C0;
}

.inputa {
  width: calc(100% - 150rpx);
  height: 60rpx;
  font-size: 36rpx;
  font-weight: 400;
  color: #353535;
  line-height: 60rpx;
  border-bottom: #A3A3A3 1rpx solid;
  margin: 60rpx 75rpx;
  text-align: left;
}

.modalDlg-xyView {
  width: calc(100% - 150rpx);
  margin: 40rpx 75rpx;
}

.loseTip {
  height: 40rpx;
  font-size: 28rpx;
  font-weight: 400;
  color: #DA5937;
  line-height: 40rpx;
  text-align: center;
  margin: 51rpx 0 20rpx 0;
}
.convention_button.a{
  width: 100%!important;
}
.a.noClike {
  background: linear-gradient(90deg, #458BFF 0%, #08D7AE 100%);
}
.vBox{
  margin-top: 32rpx;
}