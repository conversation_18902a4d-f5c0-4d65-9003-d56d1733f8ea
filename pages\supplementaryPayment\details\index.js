var util = require('../../../utils/util')
var api = require('../../../config/api.js')
var commonApi = require('../../../utils/commonApi')
var app = getApp()
Page({
  data: {
    tipName: '',
    uniqueId: '',
    tradeNo: '',
    payList: [],
    balanceText: '', //当前余额
    selectPay: '', //支付方式
    discountCoupon: '无可用优惠券',
    couponCode: '', //优惠劵ID
    imageState: 0, //0隐藏，1可看小图，2可点击看大图
  },
  onLoad(options) {
    console.log(options)
    this.setData({ uniqueId: options.uniqueId })
  },
  async onShow() {
    try {
      util.showLoading('正在加载…')
      let pages = getCurrentPages()
      let currPage = pages[pages.length - 1]
      this.setData({
        couponCode: currPage.data.couponCode,
        discountCoupon: currPage.data.discountCoupon,
        uniqueId: currPage.data.uniqueId,
      })
      await this.getInfo()

      //获取图片显示状态
      const imageState = await commonApi.getImageState(this.data.info.plateNo)
      this.setData({ imageState })
    } catch (error) {
      console.log('🚀 ~ onShow ~ error:', error)
    } finally {
      wx.hideLoading()
    }
  },
  async getInfo() {
    var that = this
    var couponCode = that.data.couponCode
    var useCoupon = 0
    if (couponCode != '') {
      useCoupon = 1
    }
    const res = await util.request(
      api.getParkingPaymentInfos,
      {
        useCoupon: useCoupon, //是否用优惠券,0表示否，1表示使用
        id: that.data.uniqueId,
        coupons: couponCode, //优惠券编号列表
      },
      'GET'
    )
    if (res.code == '0') {
      var info = res.result
      that.setData({
        info: info,
        money: info.totalCost,
      })
      that.getPayMethod(info.parkId, info.billMoney)
      that.getCouponsList(that.data.uniqueId, info.parkCode)
      wx.hideLoading()
    } else {
      util.showToast(res.message)
    }
  },
  selectDiscountCoupon() {
    var data = this.data
    wx.navigateTo({
      url:
        '/pages/parkingPayment/discountCoupon/index?couponCode=' +
        data.couponCode +
        '&uniqueId=' +
        data.uniqueId +
        '&parkCode=' +
        data.info.parkCode +
        '&isVisitor=false',
    })
  },
  pay() {
    util.showLoading('正在加载…')
    var that = this
    var data = that.data
    var selectPay = data.selectPay
    that.setData({
      isClick: false, //在点击一次后，点击状态变为关闭，默认为开启
    })
    var clock = setTimeout(
      () => {
        //定义一个延时操作setTimeout
        that.setData({
          isClick: true,
        })
        clearInterval(clock)
      },
      3000 //在3秒后，点击状态恢复为默认开启状态
    )
    if (selectPay === 'BALANCE_PAY') {
      console.log(data.balanceText)
      if (data.balanceText < data.info.totalFee) {
        wx.hideLoading()
        util.showToast('余额不足，请充值')
        return
      }
    }
    var useCoupon = 0
    if (data.couponCode != '') {
      useCoupon = 1
    }
    util
      .request(
        api.pay,
        {
          'id': data.info.id,
          'payType': selectPay,
          'coupons': [data.couponCode],
          'useCoupon': useCoupon,
        },
        'POST'
      )
      .then(function (res) {
        var infos = res.result
        if (res.code == '0') {
          that.setData({
            tradeNo: infos.tradeNo,
          })
          wx.hideLoading()
          if (infos.payStatus == 'PAID') {
            //说明是无需付款可以直接返回成功
            wx.redirectTo({
              //直接跳转到成功界面
              url: '/pages/common/payState/index?type=tcjf&id=' + that.data.tradeNo,
            })
          } else {
            that.payWeiPay(infos.timestamp, infos.nonceStr, infos.packageVal, infos.paySign, infos.signType)
          }
        } else {
          wx.hideLoading()
          util.showToast(res.message)
        }
      })
  },
  selectPayMode(e) {
    var currentTarget = e.currentTarget
    this.setData({
      selectPay: currentTarget.id,
    })
  },
  getPayMethod(parkId, billMoney) {
    //获取支付方式
    util.showLoading('正在加载…')
    const that = this
    util
      .request(
        api.getPayMethodByParkId,
        {
          parkId: parkId,
          payBusiness: 'ARREARS',
        },
        'GET'
      )
      .then(function (res) {
        const list = res.result
        if (res.code == '0') {
          that.setData({ payList: list || [] })
          if (list?.length <= 0) return

          // 优先查找余额支付方式
          const balanceItem = list.find(item => item.payType === 'BALANCE_PAY')
          const otherPayType = list.find(item => item.payType !== 'BALANCE_PAY')

          let defaultPayType
          let balanceText = ''

          // 处理balanceItem存在的情况
          if (balanceItem) {
            balanceText = balanceItem.iconUrl
            // 将浮点数乘以100转为整数后再比较，避免浮点数比较的精度问题
            const balanceInt = Math.round(parseFloat(balanceItem.iconUrl) * 100)
            const billMoneyInt = Math.round(parseFloat(billMoney) * 100)
            defaultPayType = balanceInt >= billMoneyInt || !otherPayType ? 'BALANCE_PAY' : otherPayType.payType
          } else {
            // 若没有余额支付方式，则默认使用其他支付方式（如果存在）
            defaultPayType = otherPayType?.payType || ''
          }

          that.setData({
            balanceText,
            selectPay: defaultPayType,
          })
        } else {
          console.log('获取支付方式失败' + res.message)
        }
      })
      .catch(err => {
        console.log('🚀 ~ getPayMethod ~ err:', err)
      })
      .finally(() => {
        wx.hideLoading()
      })
  },
  payWeiPay: function (timeStamp, nonceStr, packageWord, paySign, signType) {
    var that = this
    wx.requestPayment({
      //吊起微信支付
      timeStamp: timeStamp,
      nonceStr: nonceStr,
      package: packageWord,
      signType: signType,
      paySign: paySign,
      success(res) {
        wx.hideLoading()
        console.log('支付成功')
      },
      fail(res) {
        wx.hideLoading()
        console.log('支付失败')
      },
      'complete': function (res) {
        console.log('支付完成')
        if (res.errMsg == 'requestPayment:ok') {
          wx.redirectTo({
            url: '/pages/common/payState/index?type=tcjf&id=' + that.data.tradeNo,
          })
        } else {
          wx.showModal({
            title: '提示',
            content: '支付失败',
          })
        }
        return
      },
    })
  },
  // 点击事件
  previewSqs(event) {
    if (this.data.imageState !== 2) {
      return
    }

    //10.11新增图片预览功能
    // 拿到图片的地址url
    let currentUrl = event.currentTarget.dataset.src
    // 微信预览图片的方法
    wx.previewImage({
      urls: [currentUrl], // 预览的地址url
    })
  },
  getCouponsList: function (uniqueId, parkCode) {
    var that = this
    var url = api.getCouponsList
    util
      .request(
        url,
        {
          uniqueId: uniqueId,
          parkCode: parkCode,
        },
        'GET'
      )
      .then(function (res) {
        wx.hideLoading()
        if (res.code == '0') {
          if (res.result.length > 0) {
            that.setData({
              discountCoupon: '去选择',
            })
          } else {
            that.setData({
              discountCoupon: '无可用优惠券',
            })
          }
        } else {
          util.showToast(res.message)
        }
      })
      .catch(err => {})
  },
})
