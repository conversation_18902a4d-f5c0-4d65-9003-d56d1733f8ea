var util = require('../../utils/util')
var api = require('../../config/api.js')
var app = getApp()
var commonApi = require('../../utils/commonApi')
Page({
  data: {
    tipName: '',
    uniqueId: '',
    tradeNo: '',
    payList: [],
    balanceText: '', //当前余额
    selectPay: '', //支付方式
    discountCoupon: '无可用优惠券',
    couponCode: '', //优惠劵ID

    imageState: 0, //0隐藏，1可看小图，2可点击看大图
  },
  onLoad(options) {
    this.setData({
      uniqueId: options.uniqueId,
    })
  },
  async onShow() {
    util.showLoading('正在加载…')
    let pages = getCurrentPages()
    let currPage = pages[pages.length - 1]
    this.setData({
      couponCode: currPage.data.couponCode,
      discountCoupon: currPage.data.discountCoupon,
      uniqueId: currPage.data.uniqueId,
    })
    await this.getInfo()
    //获取图片显示状态
    const imageState = commonApi.getImageState(this.data.info.plateNo)
    this.setData({ imageState })
  },
  async getInfo() {
    var that = this
    var couponCode = that.data.couponCode
    var useCoupon = 0
    if (couponCode != '') {
      useCoupon = 1
    }
    try {
      const res = await util.request(
        api.getParkingPaymentInfos,
        {
          useCoupon: useCoupon, //是否用优惠券,0表示否，1表示使用
          id: that.data.uniqueId,
          coupons: couponCode, //优惠券编号列表
        },
        'GET'
      )
      var info = res.result
      if (res.code == '0') {
        that.setData({
          info: info,
          money: info.totalCost,
        })
        that.getPayMethod(info.parkId)
        that.getCouponsList(that.data.uniqueId, info.parkCode)
      } else {
        util.showToast(res.message)
      }
      wx.hideLoading()
    } catch (error) {
      console.log(error)
      wx.hideLoading()
    }
  },
  selectDiscountCoupon() {
    var data = this.data
    wx.navigateTo({
      url:
        '/pages/parkingPayment/discountCoupon/index?couponCode=' +
        data.couponCode +
        '&uniqueId=' +
        data.uniqueId +
        '&parkCode=' +
        data.info.parkCode +
        '&isVisitor=false',
    })
  },
  pay() {
    util.showLoading('正在加载…')
    var that = this
    var data = that.data
    var selectPay = data.selectPay
    that.setData({
      isClick: false, //在点击一次后，点击状态变为关闭，默认为开启
    })
    var clock = setTimeout(
      () => {
        //定义一个延时操作setTimeout
        that.setData({
          isClick: true,
        })
        clearInterval(clock)
      },
      3000 //在3秒后，点击状态恢复为默认开启状态
    )
    if (selectPay === 'BALANCE_PAY') {
      if (data.balanceText < data.info.totalFee) {
        wx.hideLoading()
        util.showToast('余额不足，请充值')
        return
      }
    }
    var useCoupon = 0
    if (data.couponCode != '') {
      useCoupon = 1
    }
    util
      .request(
        api.pay,
        {
          'id': data.info.id,
          'payType': selectPay,
          'coupons': [data.couponCode],
          'useCoupon': useCoupon,
        },
        'POST'
      )
      .then(function (res) {
        var infos = res.result
        if (res.code == '0') {
          that.setData({
            tradeNo: infos.tradeNo,
          })
          wx.hideLoading()
          if (infos.payStatus == 'PAID') {
            //说明是无需付款可以直接返回成功
            wx.redirectTo({
              //直接跳转到成功界面
              url: '/pages/common/payState/index?type=tcjf&id=' + that.data.tradeNo,
            })
          } else {
            that.payWeiPay(infos.timestamp, infos.nonceStr, infos.packageVal, infos.paySign, infos.signType)
          }
        } else {
          wx.hideLoading()
          util.showToast(res.message)
        }
      })
  },
  selectPayMode(e) {
    var currentTarget = e.currentTarget
    this.setData({
      selectPay: currentTarget.id,
    })
  },
  getPayMethod(parkId) {
    //获取支付方式
    util.showLoading('正在加载…')
    var that = this
    util
      .request(
        api.getPayMethodByParkId,
        {
          parkId: parkId,
          payBusiness: 'ARREARS',
        },
        'GET'
      )
      .then(function (res) {
        var list = res.result
        if (res.code == '0') {
          console.log('获取支付方式成功')
          that.setData({
            payList: list,
          })
          if (list.length > 0) {
            var payType = list[0]['payType']
            console.log('获取支付方式成功---默认支付类型' + payType)
            that.setData({
              selectPay: payType,
            })
            if (payType === 'BALANCE_PAY') {
              that.setData({
                balanceText: list[0]['iconUrl'], //用来对比余额
              })
            }
          }
          wx.hideLoading()
        } else {
          console.log('获取支付方式失败' + res.message)
          wx.hideLoading()
        }
      })
  },
  payWeiPay: function (timeStamp, nonceStr, packageWord, paySign, signType) {
    var that = this
    wx.requestPayment({
      //吊起微信支付
      timeStamp: timeStamp,
      nonceStr: nonceStr,
      package: packageWord,
      signType: signType,
      paySign: paySign,
      success(res) {
        wx.hideLoading()
        console.log('支付成功')
      },
      fail(res) {
        wx.hideLoading()
        console.log('支付失败')
      },
      'complete': function (res) {
        console.log('支付完成')
        if (res.errMsg == 'requestPayment:ok') {
          wx.redirectTo({
            url: '/pages/common/payState/index?type=tcjf&id=' + that.data.tradeNo,
          })
        } else {
          wx.showModal({
            title: '提示',
            content: '支付失败',
          })
        }
        return
      },
    })
  },
  // 点击事件
  previewSqs(event) {
    if (this.data.imageState !== 2) {
      return
    }
    //10.11新增图片预览功能
    // 拿到图片的地址url
    let currentUrl = event.currentTarget.dataset.src
    // 微信预览图片的方法
    wx.previewImage({
      urls: [currentUrl], // 预览的地址url
    })
  },
  getCouponsList: function (uniqueId, parkCode) {
    var that = this
    var url = api.getCouponsList
    var data = that.data
    util
      .request(
        url,
        {
          uniqueId: uniqueId,
          parkCode: parkCode,
        },
        'GET'
      )
      .then(function (res) {
        var list = []
        wx.hideLoading()
        if (res.code == '0') {
          if (res.result.length > 0) {
            that.setData({
              discountCoupon: '去选择',
            })
          } else {
            that.setData({
              discountCoupon: '无可用优惠券',
            })
          }
        } else {
          util.showToast(res.message)
        }
      })
      .catch(err => {})
  },
})
