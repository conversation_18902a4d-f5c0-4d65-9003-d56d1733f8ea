var api = require('../../../config/api.js')
var util = require('../../../utils/util')
Page({
  data: {
    isShowLongin: false,
    imgUrl: api.imgUrl,
    status: '',
  },
  onLoad: function (options) {
    var status = options.status
    this.setData({
      status: status,
    })
  },
  goFindPage() {
    var status = this.data.status
    if (status == 'NO_PARKING_LOT') {
      //暂未开放包月业务
      wx.navigateTo({
        url: '/pages/monthlyPackage/add/noData/index',
      })
    } else if (status == 'wdl') {
      //weidengl
      wx.navigateTo({
        url: '/pages/monthlyPackage/add/find/index?isFind=true',
      })
    } else {
      wx.navigateTo({
        url: '/pages/monthlyPackage/add/find/index?isFind=true',
      })
    }
  },
  showApplication(e) {
    // wx.showModal({
    //   title: '使用须知',
    //   content:
    //     '1、您需办理月卡的停车场是畅行桂林合作车场(支持线上月卡续费的车场);\r\n2、申请通过后即可在下方添加月卡，后续续卡/取消包月即可在线上操作。\r\n*取消月卡需提交管理员审核',
    //   showCancel: false,
    //   success(res) {
    //     if (res.confirm) {
    //       console.log('用户点击确定')
    //     } else if (res.cancel) {
    //       console.log('用户点击取消')
    //     }
    //   },
    // })
  },
  refresh() {
    var that = this
    util.request(api.getMonthlyStatus, null, 'GET').then(function (res) {
      //查询月卡状态
      var infos = res.result

      if (res.code == '0') {
        var monthlyStatus = infos.monthlyStatus
        if (monthlyStatus == 'NO_PARKING_LOT' || monthlyStatus == 'UNPURCHASED_MONTHLY') {
          //表示包月活动升级中或未购买月卡
          that.setData({
            status: monthlyStatus,
          })
        } else {
          wx.redirectTo({
            url: '/pages/monthlyPackage/index?status=' + monthlyStatus,
          })
        }
      } else {
        util.showToast(res.message)
      }
    })
  },
  showLogin() {
    wx.navigateTo({
      url: '/pages/common/loginTip/loginTip?tiptype=0',
    })
  },
  goPageMethod() {
    //登陆后跳转到这个方法
    wx.navigateBack()
    this.refresh()
  },
})
