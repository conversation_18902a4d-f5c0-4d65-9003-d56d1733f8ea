.box {
  padding: 60rpx 60rpx 50rpx 60rpx;
  margin: 20rpx;
  width: calc(100% - 20rpx * 2);
  height: 512rpx;
  background: #ffffff;
  box-shadow: 0rpx 10rpx 0rpx 1rpx rgba(228, 234, 248, 0.16);
  border-radius: 20rpx 20rpx 20rpx 20rpx;
  box-sizing: border-box;
}

.box .row {
  display: flex;
  margin-bottom: 16rpx;
  width: 100%;
  height: 37rpx;
}

.box .row .label,
.box .row .value {
  height: 100%;
  font-size: 26rpx;
  font-weight: 400;
  line-height: 37rpx;
}

.box .row .label {
  width: 210rpx;
  color: #353535;
  text-align: left;
}
.box .row .value {
  flex: 1;
  color: #7e7e7e;
  text-align: right;
}
.box .row .value.red {
  color: #da5937;
}

.convention_button {
  margin-top: 100rpx;
  color: #fff !important;
  background: linear-gradient(90deg, #ff5745 0%, #ffae00 100%);
}
.btn-disabled {
  opacity: 0.6;
}

.free-box {
  padding: 40rpx;
  background: linear-gradient(to right, #3f8cf4, #53b0f6);
  color: #fff;
}
.free-box__title {
  font-size: 36rpx;
}
.free-box__sub {
  margin-top: 16rpx;
}
