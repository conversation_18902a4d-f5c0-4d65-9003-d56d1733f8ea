<view class="safe-padding saveOutView">
  <cu-custom bgColor="white-bg" contentTitle="领取优惠券" isBack isCustomBack bind:back="onBack"></cu-custom>
  <view class="name">{{name}}</view>
  <view wx:if="{{ list.length == 0 }}">
    <image src="../../image/qfjf-null.png" class="nullImg"></image>
    <view class="nullTip">无优惠券</view>
  </view>
  <scroll-view wx:else scroll-y class="scrollViewSa">
    <block wx:for="{{list}}" wx:key="index">
      <view class="box columnView">
        <view class="box-t rowView">
          <view class="columnView" style="flex: 1">
            <view class="box-t-l-t">{{item.type}}</view>
            <view class="box-t-l-b">{{item.availableStartTime}} 至 {{item.availableEndTime}}</view>
          </view>
          <view class="box-t-r">{{item.value}}</view>
        </view>
        <view class="rowView" bindtap="openOrClose" style="height: auto">
          <view class="box-b-l {{show?'show':'close'}}">{{parkInfo}}</view>
          <!-- <view class="udbtn {{show?'down':'up'}}"> </view> -->
        </view>
        <view class="count">{{item.count}}张</view>
      </view>
    </block>
    <view class="content">领取优惠券活动时间：{{activityStartTime}} 至 {{activityEndTime}}</view>
    <view class="content">活动说明：{{content}}</view>
  </scroll-view>
  <button wx:if="{{list.length}}" class="convention_button" bind:tap="onGet">立即领取</button>
</view>
