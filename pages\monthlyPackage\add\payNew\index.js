var util = require('../../../../utils/util')
var api = require('../../../../config/api.js')
var app = getApp()
//新版办的包月办理1.18
Page({
  data: {
    ruleInfo: {}, //规则信息
    isRenewal: false, //是否续费和再次购买，需要隐藏掉切换车牌的按钮
    payList: [],
    selectPay: '', //balance WeChatPay选择的支付方式
    balanceText: '', //当前余额
    selectIndex: 0, //选择套餐的索引
    parkId: '',
    isClick: true,
    carList: [],
    packageList: [],
    imgUrl:
      'data:image/png;base64,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',
    parkName: '',
    parkingAddress: '',
    carNum: '',
    vehicleId: '',
    tradeNo: '',
    isShowSlotModal: false,
  },
  onLoad(options) {
    const eventChannel = this.getOpenerEventChannel()
    eventChannel.on('setData', res => {
      this.setData({
        ruleInfo: res.data,
        parkId: res.parkId,
        isRenewal: res.isRenewal,
        certCode: res.certCode == undefined ? '' : res.certCode,
      })
      this.getPackageList()
    })
  },
  select(e) {
    var currentTarget = e.currentTarget
    const selectIndex = currentTarget.id
    this.setData({
      selectIndex,
    })

    // 自动选择支付方式
    if (this.data.packageList && this.data.packageList.length > 0) {
      const price = this.data.packageList[selectIndex].price
      this.autoSelectPayMethod(price)
    }
  },

  // 根据金额自动选择支付方式
  autoSelectPayMethod(price) {
    // 将浮点数乘以100转为整数后再比较，避免浮点数比较的精度问题
    const balanceInt = Math.round(parseFloat(this.data.balanceText) * 100)
    const priceInt = Math.round(parseFloat(price) * 100)
    const payList = this.data.payList

    if (!payList || payList.length <= 0) return

    // 如果余额大于等于价格，选择余额支付，否则选择微信支付
    const otherPayType = payList.find(item => item.payType !== 'BALANCE_PAY')
    const defaultPayType = balanceInt >= priceInt || !otherPayType ? 'BALANCE_PAY' : otherPayType.payType

    this.setData({
      selectPay: defaultPayType,
    })
  },

  selectPayMode(e) {
    var currentTarget = e.currentTarget
    this.setData({
      selectPay: currentTarget.id,
    })
  },
  pay() {
    //需要向后台 获取参数
    util.showLoading('正在加载…')
    var that = this
    var data = that.data
    var selectPay = data.selectPay
    that.setData({
      isClick: false, //在点击一次后，点击状态变为关闭，默认为开启
    })
    var clock = setTimeout(
      () => {
        //定义一个延时操作setTimeout
        that.setData({
          isClick: true,
        })
        clearInterval(clock)
      },
      3000 //在3秒后，点击状态恢复为默认开启状态
    )
    var selectPackage = data.packageList[data.selectIndex]
    var url = api.payMonthly
    var param = {
      groupId: selectPackage.groupId, //1.11新增，据说是运营改了规则所以海康需要这两个字段
      scope: selectPackage.scope, //1.11新增，据说是运营改了规则所以海康需要这两个字段
      bagType: 0, //包期类型 0 包月 1 包天
      duration: selectPackage.duration,
      endTime: selectPackage.endTime,
      id: data.parkId, //传停车场ID
      payType: selectPay,
      plateNo: data.plateNo,
      price: selectPackage.price,
      ruleId: selectPackage.ruleId,
      plateColor: '',
    }
    if (data.isRenewal) {
      //续费
      url = api.payMonthlyRenewal
      param = {
        groupId: selectPackage.groupId, //1.11新增，据说是运营改了规则所以海康需要这两个字段
        scope: selectPackage.scope, //1.11新增，据说是运营改了规则所以海康需要这两个字段
        bagType: 0, //包期类型 0 包月 1 包天
        certCode: data.certCode,
        duration: selectPackage.duration,
        endTime: selectPackage.endTime,
        id: data.parkId, //传停车场ID
        payType: selectPay,
        price: selectPackage.price,
        ruleId: selectPackage.ruleId,
      }
    }
    util.request(url, param, 'POST').then(function (res) {
      var infos = res.result
      if (res.code == '0') {
        var tradeNo = infos.tradeNo
        wx.hideLoading()
        that.setData({
          tradeNo: tradeNo,
        })
        if (infos.payStatus == 'PAID') {
          //说明是无需付款可以直接返回成功
          that.setData({
            tradeNo: infos.tradeNo,
          })
          var clock = setTimeout(() => {
            wx.redirectTo({
              url: '/pages/monthlyPackage/details/refund/result/index?id=' + tradeNo + '&type=zf',
            })
            clearInterval(clock)
          }, 1500)
        } else {
          that.payWeiPay(infos.timestamp, infos.nonceStr, infos.packageVal, infos.paySign, infos.signType)
        }
      } else if (res.code == '3006') {
        wx.hideLoading()
        wx.showModal({
          title: '温馨提示',
          content: '月卡续费功能升级中',
          confirmText: '我知道了',
          showCancel: false,
          success(res) {
            if (res.confirm) {
              console.log('用户点击确定')
            }
          },
        })
      } else {
        wx.hideLoading()
        util.showToast(res.message)
      }
    })
  },
  payWeiPay: function (timeStamp, nonceStr, packageWord, paySign, signType) {
    var that = this
    var data = that.data
    wx.requestPayment({
      //吊起微信支付
      timeStamp: timeStamp,
      nonceStr: nonceStr,
      package: packageWord,
      signType: signType,
      paySign: paySign,
      success(res) {
        wx.hideLoading()
        console.log('支付成功')
      },
      fail(res) {
        wx.hideLoading()
        console.log('支付失败')
      },
      'complete': function (res) {
        console.log('支付完成')
        if (res.errMsg == 'requestPayment:ok') {
          var clock = setTimeout(() => {
            wx.redirectTo({
              url: '/pages/monthlyPackage/details/refund/result/index?id=' + data.tradeNo + '&type=zf',
            })
            clearInterval(clock)
          }, 1500)
        } else {
          wx.showModal({
            title: '提示',
            content: '支付失败',
          })
        }
        return
      },
    })
  },
  getPackageList() {
    util.showLoading('正在加载…')
    var that = this
    var data = that.data
    var groupId = data.ruleInfo.groupId
    if (data.ruleInfo.groupId == null) {
      groupId = ''
    }

    var url =
      api.getMonthPackages_new +
      '?parkId=' +
      data.parkId +
      '&ruleId=' +
      data.ruleInfo.ruleId +
      '&scope=' +
      data.ruleInfo.scope +
      '&groupId=' +
      groupId
    if (data.isRenewal) {
      //续费
      url =
        api.getRenewalMonthPackages_new +
        '?certCode=' +
        data.certCode +
        '&ruleId=' +
        data.ruleInfo.ruleId +
        '&scope=' +
        data.ruleInfo.scope +
        '&groupId=' +
        groupId
    }
    util.request(url, {}, 'GET').then(function (res) {
      if (res.code == '0') {
        var result = res.result
        that.setData({
          parkName: result.parkName,
          parkingAddress: result.parkAddress,
          carList: result.vehicles,
          packageList: result.packages,
        })

        that.showPlate(result.vehicles)
        that.setPayMethod(result.paymentMethods, result.packages[that.data.selectIndex].price)
        wx.hideLoading()
      } else {
        wx.hideLoading()
        util.showToast(res.message)
      }
    })
  },
  showPlate(plates) {
    //展示车牌啊
    var that = this
    for (var i = 0; i < plates.length; i++) {
      if (plates[i].bagable === 1) {
        that.setData({
          plateNo: plates[i].plateNo,
        })
        break
      }
    }
  },
  setPayMethod(list, price) {
    // 获取支付方式
    this.setData({ payList: list || [] })
    if (list?.length <= 0) return

    // 优先查找余额支付方式
    const balanceItem = list.find(item => item.payType === 'BALANCE_PAY')
    const otherPayType = list.find(item => item.payType !== 'BALANCE_PAY')

    let defaultPayType = ''
    let balanceText = ''

    // 处理余额支付存在的情况
    if (balanceItem) {
      balanceText = balanceItem.iconUrl
      // 将浮点数乘以100转为整数后再比较，避免浮点数比较的精度问题
      const balanceInt = Math.round(parseFloat(balanceItem.iconUrl) * 100)
      const priceInt = Math.round(parseFloat(price) * 100)
      defaultPayType = balanceInt >= priceInt || !otherPayType ? 'BALANCE_PAY' : otherPayType.payType
    } else {
      // 余额支付不存在时，使用其他支付方式（如果存在）
      defaultPayType = otherPayType?.payType || ''
      console.log('无余额支付方式，默认使用其他支付方式')
    }

    this.setData({
      balanceText,
      selectPay: defaultPayType,
    })
    console.log('获取支付方式成功---默认支付类型: ' + defaultPayType)
  },
  showBox: function () {
    if (this.data.isRenewal) {
      return
    }
    this.setData({
      showPlateNoBox: true,
    })
  },
  selectItem: function (e) {
    var detail = e.detail
    this.setData({
      plateNo: detail.plateNo,
    })
  },
  goParkListPage: function (e) {
    wx.navigateTo({
      url: '/pages/monthlyPackage/add/payNew/parkList?groupId=' + this.data.ruleInfo.groupId,
    })
  },
  showApplication(e) {
    this.setData({
      isShowSlotModal: true,
    })
  },
  closeModal(e) {
    this.setData({
      isShowSlotModal: false,
    })
  },
})
