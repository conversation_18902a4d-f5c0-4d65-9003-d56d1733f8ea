<view class="saveOutView">
  <cu-custom bgColor="white-bg" contentTitle="更改手机号" isBack="true"></cu-custom>

  <view wx:if="{{!showVerification&&!showNextStep&&!showSuccess}}">
    <view class="tip">当前手机号（可用于登录）</view>
    <view class="text">{{phone}}</view>
    <button class="convention_button" bindtap="showNextStep">更换手机号</button>
  </view>

  <view wx:if="{{!showVerification&&showNextStep}}">
    <view class="tipT">请输入新的手机号码</view>
    <input class="input" auto-focus placeholder="请输入手机号码" type="number" bindinput="bindKeyInput" maxlength="11" />
    <button class="convention_button {{allowClicking?'':'noClike'}}" bindtap="nextStep">下一步</button>
  </view>
  <verificationCode showVerification="{{showVerification}}" successText="已发送验证码至{{inputValue}}" phone="{{inputValue}}" type="UPDATE_PHONE" bind:refresh="goBack" bind:sure="modifyPhone" showSuccess="{{showSuccess}}" bind:cancel="back" isClick="{{isClick}}"></verificationCode>
</view>