var util = require('../../../utils/util')
var validator = require('../../../utils/validator')
var api = require('../../../config/api.js')
var app = getApp()
Component({
  properties: {
    isShow: {
      type: Boolean,
      default: false,
    },
    authPid: {
      type: String,
    },
  },
  data: {
    showLoseTip: false, //显示校验失败的提示
    phoneIsTrue: true, //手机号码是否正确
    currentTime: 60,
    interval: null, //倒计时函数
    showSendCodeBtn: true, //显示倒计时时间
    code: '', //验证码
    verificationId: '',
    allowClicking: false,
    showVerification: false,
    phoneNumber: '',
    isClick: true,
  },
  methods: {
    //点击显示底部弹出
    changeRange: function () {
      this.showModal()
      console.log('我是弹窗打开----')
    },
    //底部弹出框
    showModal: function () {
      // 背景遮罩层
      var animation = wx.createAnimation({
        duration: 50,
        timingFunction: 'linear',
        delay: 0,
      })
      //this.animation = animation
      animation.translateY(50).step()
      this.setData({
        animationData: animation.export(),
        isShow: true,
      })
      setTimeout(
        function () {
          animation.translateY(0).step()
          this.setData({
            animationData: animation.export(),
          })
        }.bind(this),
        50
      )
    },

    //点击背景面任意一处时，弹出框隐藏
    hideModal: function (e) {
      //弹出框消失动画
      var animation = wx.createAnimation({
        duration: 10,
        timingFunction: 'linear',
        delay: 0,
      })
      //this.animation = animation
      animation.translateY(10).step()
      this.setData({
        animationData: animation.export(),
      })
      setTimeout(
        function () {
          animation.translateY(0).step()
          this.setData({
            animationData: animation.export(),
            isShow: false,
          })
        }.bind(this),
        10
      )
    },
    allow() {
      if (this.data.allowXY) {
        this.setData({
          allowXY: false,
        })
      } else {
        this.setData({
          allowXY: true,
        })
      }
    },
    viewAgreement: function (e) {
      var id = e.currentTarget.id
      wx.navigateTo({
        url: '/pages/common/viewAgreement/index?id=' + id,
        fail: function (res) {
          console.log(res)
        },
      })
    },
    nextStep: function () {
      var that = this
      var data = that.data
      if (data.allowXY) {
        if (data.allowClicking) {
          if (!validator.validatePhone(data.phoneNumber)) {
            util.showToast('请输入正确的手机号')
          } else {
            that.setData({
              showVerification: true,
            })
            that.getVerificationCode()
          }
        }
      } else {
        util.showToast('请阅读并同意服务协议、隐私政策协议及登录政策')
      }
    },
    bindKeyInput: function (e) {
      var phone = e.detail.value
      var flag = false
      if (phone.length == 11) {
        flag = true
      }
      this.setData({
        phoneNumber: phone,
        allowClicking: flag,
      })
    },
    bindKeyCodeInput: function (e) {
      var code = e.detail.value
      this.setData({
        code: code,
      })
      if (code.length == 6) {
        this.setData({
          isClick: true,
        })
      }
    },
    getCode: function (options) {
      console.log('getCode')
      var that = this
      var currentTime = that.data.currentTime
      var i = setInterval(function () {
        currentTime--
        that.setData({
          currentTime: currentTime,
        })
        if (currentTime <= 0) {
          clearInterval(that.data.interval)
          that.setData({
            phoneIsTrue: true,
            currentTime: 60,
            showSendCodeBtn: false,
          })
        }
      }, 1000)
      this.setData({
        interval: i,
        phoneIsTrue: false,
      })
    },
    getVerificationCode() {
      util.showLoading('正在加载…')
      var that = this
      that.setData({
        showSendCodeBtn: true,
      })
      util
        .request(
          api.getLoginCode,
          {
            'phone': that.data.phoneNumber,
            'verificationType': 'REGISTER_LOGON',
          },
          'POST'
        )
        .then(function (res) {
          if (res.code == '0') {
            wx.hideLoading()
            that.setData({
              verificationId: res.result,
              phoneIsTrue: true,
            })
            that.getCode()
          } else {
            wx.hideLoading()
            wx.showModal({
              title: '失败提醒',
              content: res.message,
              showCancel: false,
              confirmColor: app.globalData.tipBtnColor,
              success(res) {
                if (res.confirm) {
                  that.triggerEvent('cancel')
                } else if (res.cancel) {
                  console.log('用户点击取消')
                }
              },
            })
            that.setData({
              phoneIsTrue: false,
              currentTime: 60,
              showSendCodeBtn: false,
            })
            clearInterval(that.data.interval)
          }
        })
    },
    sure(e) {
      var that = this
      var data = that.data
      var code = data.code
      if (code.length < 6) {
        util.showToast('请输入正确的验证码')
      } else {
        if (data.isClick) {
          that.setData({
            isClick: false,
          })
          util.showLoading('正在加载…')
          util
            .request(
              api.loginByCode,
              {
                'code': code,
                'verificationId': that.data.verificationId,
              },
              'POST'
            )
            .then(async function (res) {
              if (res.code == '0') {
                try {
                  wx.hideLoading()
                  if (that.authPid) {
                    await util.request(
                      api.cxglAuthBindToken,
                      { authPid: that.authPid, cxglLoginToken: res.result.token },
                      'post'
                    )
                  }
                  app.globalData.userInfo = res.result
                  wx.setStorageSync('token', res.result.token) //登陆成功后需要重置token和用户信息
                  that.triggerEvent('refresh', {}) //通知调用页面刷新
                  that.hideModal()
                } catch (error) {
                  console.log('🚀 ~ error:', error)
                  util.showToast('绑定token信息失败')
                } finally {
                  wx.hideLoading()
                }
              } else {
                that.setData({
                  showLoseTip: true,
                  isClick: true,
                })
                wx.hideLoading()
              }
            })
        }
      }
    },
  },
  // 生命周期
  lifetimes: {
    ready: function () {},
  },
})
