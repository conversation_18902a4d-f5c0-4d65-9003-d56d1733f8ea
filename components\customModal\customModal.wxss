/* components/customModal/customModal.wxss */
.model {
  position: fixed;
  width: 100%;
  height: 100%;
  background: #000;
  z-index: 9999998;
  opacity: 0.6;
  top: 0;
  left: 0;
}

.custom-modal {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 99999999;
  display: flex;
  flex-direction: column;
  width: 620rpx;
  background: #ffffff;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.custom-modal-header {
  position: relative;
  padding: 30rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  border-bottom: 1rpx solid #f5f5f5;
}

.custom-modal-title {
  font-size: 34rpx;
  font-weight: 500;
  color: #333333;
  text-align: center;
}

.custom-modal-close {
  position: absolute;
  right: 30rpx;
  top: 30rpx;
  font-size: 40rpx;
  color: #999999;
  line-height: 1;
  padding: 10rpx;
}

.custom-modal-content {
  padding: 40rpx 30rpx;
  max-height: 60vh;
  overflow-y: auto;
}

.custom-modal-text {
  font-size: 30rpx;
  color: #666666;
  line-height: 1.6;
  text-align: center;
  display: block;
}

.custom-modal-footer {
  display: flex;
  border-top: 1rpx solid #f5f5f5;
}

.custom-modal-btn {
  flex: 1;
  height: 90rpx;
  line-height: 90rpx;
  text-align: center;
  font-size: 32rpx;
}

.custom-modal-btn.cancel {
  color: #666666;
  border-right: 1rpx solid #f5f5f5;
}

.custom-modal-btn.confirm {
  color: #4768f3;
  font-weight: 500;
}
