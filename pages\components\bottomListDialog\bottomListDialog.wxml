<view class="half-screen" catchtouchmove="preventTouchMove">
  <!--屏幕背景变暗的背景  -->
  <view class="background_screen" catchtap="hideModal" wx:if="{{isShow}}"></view>
  <!--弹出框  -->
  <view animation="{{animationData}}" class="attr_box" wx:if="{{isShow}}">
    <view class="dialog-box">
      <view class="dialog-head">
        <!-- <view class="dialog-title">车辆设置</view> -->
        <view class="close2ImgBox">
          <image src="../../../image/close.png" class="close2Img" catchtap="hideModal"></image>
        </view>
      </view>
      <view class='dialog-content'>
        <view class="select-box">
          <scroll-view scroll-y="true">
            <block wx:for="{{list}}" wx:key="index">
              <view class="select-item disable" wx:if="{{item[disablekey]==0}}">{{item[rangekey]}}{{item["explain"]}}</view>
              <view class="select-item {{item[rangekey]==value?'selectedItem':''}}" data-info="{{item}}" catchtap="getValueTap" wx:else>{{item[rangekey]}}{{item["explain"]}}</view>
            </block>
          </scroll-view>
        </view>
      </view>
    </view>
  </view>
</view>