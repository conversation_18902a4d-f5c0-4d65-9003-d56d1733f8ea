// app.js
App({
  onLaunch() {
    this.autoUpdate()
    wx.getSystemInfo({
      success: e => {
        this.globalData.windowWidth = e.windowWidth
        this.globalData.windowHeight = e.windowHeight
        this.globalData.StatusBar = e.statusBarHeight
        let capsule = wx.getMenuButtonBoundingClientRect()
        if (e.model.indexOf('iPhone X') !== -1) {
          this.globalData.iphoneX = true
        }
        var oneRpxToPx = 750 / e.windowWidth
        var v_px = 372.5 / oneRpxToPx
        if (capsule) {
          this.globalData.Custom = capsule
          this.globalData.CustomBar = capsule.bottom + capsule.top - e.statusBarHeight
          this.globalData.jnTop = capsule.top * oneRpxToPx
          if (
            this.globalData.iphoneX ||
            e.model.search('iPhone 11') != -1 ||
            e.model.search('iPhone 12') != -1 ||
            e.model.search('iPhone 13') != -1 ||
            e.model.search('iPhone 14') != -1
          ) {
            this.globalData.MyImageHeight = (capsule.top - e.statusBarHeight) * 4 + v_px
          } else {
            this.globalData.MyImageHeight = v_px
          }
        } else {
          this.globalData.MyImageHeight = v_px
          this.globalData.CustomBar = e.statusBarHeight + 50 //statusBarHeight 状态栏的高度
        }
      },
    })
    var that = this
    wx.getLocation({
      //获取当前位置
      type: 'gcj02',
      success(res) {
        //预约界面需要显示当前位置离车场多少千米。要是用户没有授权，就显示中心广场到这个车场的距离。所以存储当前的经纬度。如果是从地图界面进去到预约界面的，就是算戳在地图上的点到预约车场的距离
        that.globalData.latitude = res.latitude
        that.globalData.longitude = res.longitude
      },
      fail(res) {},
    })
  },

  // 引入`towxml3.0`解析方法
  towxml: require('/towxml/index'),

  globalData: {
    isLoginBack_mycar: false, //是否是从登陆页面返回的
    scale: 16, //地图层级
    balance: 0, //余额
    payList: [], //支付方式列表
    selectPay: 'BALANCE_PAY', //默认选中的支付方式
    tipBtnColor: '#4768F3',
    jnTop: '66',
    MyImageHeight: '372.5',
    iphoneX: false,
    mapScale: 16,
    latitude: 25.275033,
    longitude: 110.295961, //默认经纬度:如果用户拒绝授权位置，则经纬度为桂林中心广场的经纬度
    userInfo: {
      firstUse: true, //是否是第一次登陆
      loginStatus: 'TEMP_LOGIN', //TEMP_LOGIN表示临时登录，LOGGED_IN表示已登录，LOGIN_EXPIRED表示登录过期,可用值:TEMP_LOGIN,LOGGED_IN,LOGIN_EXPIRED
    },
    isFirstOpen: true,
  },

  // 自动更新小程序版本
  autoUpdate() {
    if (wx.canIUse('getUpdateManager')) {
      //1. 检查小程序是否有新版本发布
      const updateManager = wx.getUpdateManager()
      updateManager.onCheckForUpdate(function (res) {
        //2. 小程序有新版本，则静默下载新版本，做好更新准备
        if (res.hasUpdate) {
          updateManager.onUpdateReady(function () {
            wx.showModal({
              title: '更新提示',
              content: '新版本已经准备好，是否重启应用？',
              success: function (res) {
                if (res.confirm) {
                  //3. 新的版本已经下载好，调用applyUpdate应用新版本并重启
                  updateManager.applyUpdate()
                }
              },
            })
          })

          updateManager.onUpdateFailed(function () {
            // 新的版本下载失败
            wx.showModal({
              title: '已经有新版本',
              content: '新版本已经上线，请您删除当前小程序，重新搜索打开',
            })
          })
        }
      })
    } else {
      // 如果希望用户在最新版本的客户端上体验您的小程序，可以这样子提示
      wx.showModal({
        title: '提示',
        content: '当前微信版本过低，无法使用该功能，请升级到最新微信版本后重试。',
      })
    }
  },
})
