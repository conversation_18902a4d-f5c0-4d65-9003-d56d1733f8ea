page {
  background-color: #F6F7FB;
}
.container{
  width: 100%;
}

.header{
  padding-top: 165rpx;
  padding-bottom: 76rpx;
  width: 100%;
  display: flex;
  justify-content: center;
  flex-direction: column;
  align-items: center;
}
.header .top{
  margin-bottom: 30rpx;
  width: 94rpx;
  height: 94rpx;
}
.header .text{
  font-size: 26rpx;
  font-weight: bold;
  color: #353535;
}

.tip{
  font-size: 26rpx;
  font-weight: bold;
  color: #353535;
  line-height: 54rpx;
  text-align: center;
}
.control{
  margin-left: 20rpx;
  margin-top: 60rpx;
  display: flex;
  flex-direction: row;
}
.btn-left,
.btn-right{
  width: 355rpx;
  height: 80rpx;
  font-size: 34rpx;
  color: #FFFFFF;
  line-height:  80rpx;
  text-align: center;
}
.btn-left{
  background: #41E0AC;
  border-radius: 100rpx 0rpx 0rpx 100rpx;
}
.btn-right{
  background: linear-gradient(270deg, #458BFF 0%, #256BF5 100%);
  border-radius: 0rpx 100rpx 100rpx 0rpx;
}