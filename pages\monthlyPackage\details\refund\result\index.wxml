<cu-custom bgColor="white-bg" contentTitle="{{type=='tk'?'月卡退费':'包月办理'}}" isBack="true"></cu-custom>
<view class="infoBox">
  <view class="infoBox-t rowView">
    <image src="../../../../../image/yes-yhj.png" style="height: 72rpx; width: 72rpx;"></image>
    <view class="infoBox-t-r columnView">
      <view>支付成功</view>
      <view>实付：¥{{backFee}}</view>
    </view>
  </view>
  <view class="infoBox-b rowView">
    <view class="infoBox-b-l">包月车场名称:</view>
    <view class="infoBox-b-r">{{parkName}}</view>
  </view>
  <view class="infoBox-b rowView">
    <view class="infoBox-b-l">车牌号码:</view>
    <view class="infoBox-b-r">{{plateNo}}</view>
  </view>
  <view class="infoBox-b rowView" wx:if="{{type!='tk'}}">
    <view class="infoBox-b-l" >包月规则:</view>
    <view class="infoBox-b-r">{{durationDesc}}</view>
  </view>
  <view class="infoBox-b rowView" wx:if="{{type=='tk'}}">
    <view class="infoBox-b-l" >退款时间</view>
    <view class="infoBox-b-r">{{refundTime}}</view>
  </view>
  <view class="infoBox-b rowView" wx:if="{{type=='tk'}}">
    <view class="infoBox-b-l">退款金额</view>
    <view class="infoBox-b-r">{{refundFee}}</view>
  </view>
  <view class="infoBox-b rowView" wx:if="{{type!='tk'}}">
    <view class="infoBox-b-l" >支付时间:</view>
    <view class="infoBox-b-r">{{payTime}}</view>
  </view>
  <view class="infoBox-b rowView">
    <view class="infoBox-b-l" >{{type=='tk'?'退款渠道':'支付方式'}}:</view>
    <view class="infoBox-b-r">{{refundChannel}}</view>
  </view>
</view>
<button class="convention_button" wx:if="{{type=='tk'}}" bindtap="back">返回我的月卡</button>
<button class="convention_button" wx:else bindtap="goMyPage">查看我的月卡</button>
