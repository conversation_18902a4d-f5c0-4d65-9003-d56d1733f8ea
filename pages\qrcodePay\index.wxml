<cu-custom bgColor="white-bg" contentTitle="扫码缴费" isBack="true"></cu-custom>

<view class="container">
  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-container">
    <view class="loading-content">
      <view class="loading-icon"></view>
      <view class="loading-text">正在查询欠费信息...</view>
    </view>
  </view>

  <!-- 无欠费状态 -->
  <view wx:elif="{{querySuccess === true && !hasArrears}}" class="no-arrears-container">
    <view class="result-content">
      <image src="../../image/suc.png" class="result-icon"></image>
      <view class="result-title">无需缴费</view>
      <view class="result-message">{{noArrearsMessage}}</view>

      <view class="button-group">
        <button class="btn btn-primary" bindtap="goHome">返回首页</button>
        <button class="btn btn-secondary" bindtap="retryQuery">重新查询</button>
      </view>
    </view>
  </view>

  <!-- 错误状态 -->
  <view wx:elif="{{querySuccess === false}}" class="error-container">
    <view class="result-content">
      <image src="../../image/no-icon.png" class="result-icon"></image>
      <view class="result-title">查询失败</view>
      <view class="result-message">{{errorMessage || '网络异常，请重试'}}</view>

      <view class="button-group">
        <button class="btn btn-primary" bindtap="retryQuery">重新查询</button>
        <button class="btn btn-secondary" bindtap="goHome">返回首页</button>
      </view>
    </view>
  </view>
</view>
