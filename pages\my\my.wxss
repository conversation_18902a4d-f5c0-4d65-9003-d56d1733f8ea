@import '../common/index.wxss';
@import '../common/common.wxss';

page {
  background: #f8f8f8;
  height: 100hv;
}

.up {
  width: 100%;
  height: 349rpx;
}

.userinfo {
  color: #353535;
  /* padding-left: 60rpx;
  padding-top: calc(80rpx - constant(safe-area-inset-top));
  padding-top: calc(80rpx - env(safe-area-inset-top));
  padding-bottom: 48rpx; */
  flex-direction: row;
  display: flex;
  height: 102rpx;
  padding: 50rpx 80rpx 35rpx 80rpx;
  z-index: 2;
}

.userinfo-avatar {
  width: 102rpx;
  height: 102rpx;
}

.userinfo-text {
  width: calc(100% - 318rpx);
  min-height: 122rpx;
  flex-direction: column;
  margin-left: 26rpx;
}

.name {
  width: 100%;
  min-height: 55rpx;
  font-size: 40rpx;

  font-weight: 500;
  color: #353535;
  line-height: 55rpx;
  padding: 13rpx 0;
}

.username {
  width: 100%;
  min-height: 32rpx;
  line-height: 32rpx;
  font-size: 30rpx;

  font-weight: 800;
  color: #ffffff;
  word-wrap: break-word;
  white-space: pre-wrap;
}

.phone {
  line-height: 21rpx;
  margin-top: 50rpx;
  height: 21rpx;
  font-size: 26rpx;
  font-weight: bold;
  color: #ffffff;
}

.line {
  width: 2rpx;
  height: 72rpx;
  background: #f4f8ff;
  opacity: 0.15;
  border-radius: 1rpx;
  margin-top: 30rpx;
}

.usermotto {
  margin-top: 200px;
}

.userdata-v {
  margin-top: 30rpx;
  width: 183rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.userdata-num {
  width: 100%;
  line-height: 35rpx;
  text-align: center;
  height: 35rpx;
  font-size: 30rpx;

  font-weight: bold;
  color: #ffffff;
}

.userdata-title {
  width: 150rpx;
  height: 73rpx;
  line-height: 48rpx;
  font-size: 22rpx;

  font-weight: 500;
  text-align: center;
  color: #ffffff;
  margin-left: 32rx;
  background: url(data:image/png;base64,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);
  background-repeat: no-repeat;
  background-size: 100%;
}

.center-one {
  width: calc('100%- 120rpx');
  height: 238rpx;
  border-radius: 20rpx;
  margin: 0rpx 50rpx 30rpx 50rpx;
  background: #ffffff;
  box-shadow: 0rpx 10rpx 20rpx 0rpx rgba(228, 234, 248, 0.5);
  border-radius: 20rpx;
}

.center-one .center-one-v-text {
  color: #353535;
}

.center-one .center-one-v {
  margin: 0 auto;
}
.scroll-inner {
  overflow: hidden;
}
.navigator {
  width: calc(100% - 160rpx);
  margin: 0 50rpx;
}

.navigator-text {
  font-size: 28rpx;
  font-weight: 500;
  color: #353535;
}

.center-one-v-img {
  margin-top: 66rpx;
}
