<view class="saveOutView">
  <cu-custom bgColor="white-bg" contentTitle="系统消息" isBack="true"></cu-custom>
  <view class="header">
    <view class="headToolbar">未读（{{list.length}}）</view>
  <view class="clear">清除未读</view>
  </view>
  <scroll-view scroll-y="true" style="flex: 1;">
    <view class="no-data" hidden="{{list.length != 0}}">
      <image class="icon" src="{{imgUrl}}/noListData.png"/>
      <text class="tip">暂无消息</text>
    </view>
  </scroll-view>
</view>