/*模态框*/
/*使屏幕变暗  */
.background_screen {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  background: #000;
  opacity: 0.6;
  overflow: hidden;
  z-index: 1000;
  color: #fff;
}

/*对话框 */
.attr_box {
  background: #FFFFFF;
  opacity: 1;
  /* border-radius: 0px 0px 0px 0px; */
  /* height: 500rpx; */
  height: auto;
  width: 100%;
  overflow: hidden;
  position: fixed;
  bottom: 0;
  left: 0;
  z-index: 2000;
  background: #fff;
  /* background: rgba(66, 66, 66, .6); */
  padding-top: 30rpx;
  padding-bottom: 60rpx;
  box-sizing: border-box;

}


.dialog-box {
  width: 100%;
  height: 100%;
  /* background-color: pink; */
}

.dialog-head {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  height: 60rpx;
  /* background-color: rgb(215, 255, 192); */
}

.dialog-title {
  width: 80%;
  height: 48rpx;
  font-size: 34rpx;
  font-weight: bold;
  color: #353535;
  line-height: 54rpx;
  text-align: center;
}

.close2ImgBox {
  width: 10%;
  height: 100%;
  display: flex;
  align-items: center;
}

.close2Img {
  width: 25rpx;
  height: 25rpx;
}

.dialog-content {
  height: calc(100% - 60rpx);
  box-sizing: border-box;
}

/* 主体内容 */
.top {
  width: 100%;
  height: 780rpx;
  margin: 10 auto;
  border-radius: 15rpx;
}

.imagesize {
  margin-left: 15rpx;
  justify-content: center;
  align-items: center;
  vertical-align: middle;
  margin-top: -7rpx;
}

.toptwo {
  width: 100%;
  display: flex;
}

.paizhao {
  height: 40rpx;
  line-height: 40rpx;
  margin-top: 30rpx;
  background-color: #E5F4FF;
  border-radius: 10rpx;
  padding-right: 13rpx;
}

.ptitle {
  height: 38rpx;
  font-size: 28rpx;
  font-weight: 400;
  color: #6883F5;
  text-align: center;
  margin-bottom: 28rpx;
}

.cartitle {
  width: 61%;
  height: 100rpx;
  line-height: 100rpx;
  font-size: 20px;
  font-weight: 600;
  margin-left: 7%;
  color: #333333;
  background-color: #fff;
}

.bottomtext {
  width: 100%;
  text-align: center;
  height: 38rpx;
  font-size: 28rpx;

  font-weight: 400;
  color: #6883F5;
  margin: 10rpx 0 20rpx 0;
}

.con-query {
  /* height: 4.8rem; */
  width: 100%;
  border-radius: 8px;
  /* background-color: #FFF; */
}

.pages_header {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.pages_header_top {
  width: 33.3%;
  height: 60rpx;
  border-left: 5px solid green;
  border-right: 5px solid green;
}

.pages_header_btm {
  width: 70%;
  background: green;
  height: 120rpx;
  line-height: 120rpx;
  text-align: center;
  color: white;
  border-radius: 10rpx;
  font-weight: normal;
  font-size: 16pt;
}

.tips {
  text-align: center;
  margin: 60rpx 0;
  font-size: 12pt;
  color: #888888;

}

.plate-input-text {
  text-align: center;
  line-height: 90rpx;
  color: #f39900;
}

.plate-input-flag {
  float: right;
  margin-right: 8%;
  font-size: 14PX;
}

.plate-input-flag .new-energy {
  color: #14c414;
}

.plate-input-body {
  height: 90rpx;
  width: calc(100% - 18rpx);
  margin: 20rpx 0 35rpx 0;
  margin-left: 9rpx;
}

.plate-input-content {
  display: flex;
  flex-direction: row;
  height: 90rpx;
}

.plate-nums-foc {
  width: 70rpx;
  height: 90rpx;
  display: flex;
  flex: 1;
  margin: 0 5rpx 0 5rpx;
  align-items: center;
  justify-content: center;
  text-align: center;
  box-sizing: border-box;
  background: #FFFFFF;
  opacity: 1;
  border-radius: 5rpx 5rpx 5rpx 5rpx;
}

.plate-nums-foc .plate-num-text {
  border: 2rpx solid #4768F3;
}

.plate-nums-first {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.plate-num-text {
  font-size: 40rpx;
  font-weight: 300;
  border-radius: 5rpx 5rpx 5rpx 5rpx;
  opacity: 1;
  border: 1rpx solid #7e7e7e;
  line-height: 80rpx;
  width: 70rpx;
  height: 80rpx;
  background: #FFFFFF;
}

.new-plate-input-content {
  display: flex;
  flex-direction: row;
  height: 100rpx;
}

.kb_top {
  align-content: relative;
  width: 100%;
  height: 74rpx;
  background: #fff;
  border-top: solid #ebebeb 2rpx;
  border-bottom: 15rpx solid #F4F4F4;
}

.keyboard {
  z-index: 9999;
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  height: auto;
  background: #F4F4F4;
  display: flex;
  flex-wrap: wrap;
  border-bottom: 15rpx solid #F4F4F4;
}

.td {
  flex-grow: 1;
  text-align: center;
  font-size: 34rpx;
  height: 86rpx;
  line-height: 80rpx;
  background: #fff;
  margin: 10rpx 5rpx;
  color: #333;
  border-radius: 2rpx;
  box-shadow: 0rpx 2rpx 0rpx #a9a9a9;
}

.td_nor {
  flex: 1 1 6%;
}

.td_num {
  flex: 1 1 8%;
}

.td_spec {
  flex: 1 1 12%;
}

.board_bg {
  box-shadow: 0 0 0 #e5e5e5;
  background: #e5e5e5;
}

.del-first {
  position: absolute;
  bottom: 10rpx;
  right: 10rpx;
  width: 100rpx;
  height: 86rpx;
  background-color: #fff;
  box-shadow: 0rpx 2rpx 0rpx #a9a9a9;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 10rpx;
}

.del-hover {
  position: absolute;
  bottom: 10rpx;
  right: 10rpx;
  width: 137rpx;
  height: 86rpx;
  background-color: #e5e5e5;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 10rpx;
  box-shadow: 0 0 0 #e5e5e5;
}

.del-img {
  display: block;
  width: 46rpx;
  height: 38rpx;
}

.yes_icon {
  width: 40rpx;
  height: 40rpx;
  margin: 0 10rpx 0 10rpx;
}

.dx {
  flex-direction: row;
  display: flex;
  text-align: center;
  width: 300rpx;
  height: 38rpx;
  font-size: 28rpx;
  font-weight: 400;
  color: #7E7E7E;
  line-height: 38rpx;
  margin:0 20rpx 60rpx auto;
}
.dx>.text {
  width: 240rpx;
}

.columnView {
  flex-direction: column;
  display: flex;
}

/* 常规按钮样式 */
.convention_button {
  /* 通用渐变蓝绿大按钮 */
  text-align: center;
  border-bottom: 10px;
  font-size: 34rpx;
  font-weight: 400;
  color: #FFFFFF;
  line-height: 40rpx;
  width: calc(100% - 40rpx) !important;
  height: 80rpx;
  background: linear-gradient(90deg, #458BFF 0%, #08D7AE 100%);
  box-shadow: 0rpx 3rpx 10rpx 1rpx rgba(37, 107, 245, 0.2);
  border-radius: 40rpx 40rpx 40rpx 40rpx;
  margin-bottom: 20rpx;
}
.noAllowClick{
  background: #C0C0C0;
}