<!--pages/set/set.wxml-->
<view class="saveOutView">
  <cu-custom bgColor="white-bg" contentTitle="设置" isBack="true"></cu-custom>
  <view class="container" style="flex: 1;height: 1rpx;">
    <view class="box">
       <view class="row next" bindtap="gotoPage" data-url="/pages/my/set/account/index" data-check="true">
        账号管理
      </view>
      <view class="row next" bindtap="gotoPage" data-url="/pages/my/set/paymentMnagement/index" data-check="true">
        支付管理
      </view> 
      <view class="row next" bindtap="gotoPage" data-url="/pages/my/set/about/index" data-check="false">
        关于
      </view>
    </view>
    <view class="box">
      <view class="row" bindtap="gotoPage" data-url="/pages/my/set/logoff/logoffCheck/index" data-check="true">
        注销账号<text class="secord">提交申请，删除所有数据，永久注销账号 </text>
      </view>
    </view>
    <view class="box" wx:if="{{loginStatus == 'LOGGED_IN'}}">
      <view class="row red" bindtap="logout">
        <text>退出登录</text>
      </view>
    </view>
  </view>
</view>
