@import '../../common/common.wxss';

page {
  background-color: #f6f7fb;
}

.ccxq {
  display: flex;
  flex-direction: column;
  position: relative;
  height: 100%;
}

.listDivHeight {
  width: 100vh;
  height: 100vh;
}

.infoDiv {
  background: #FFFFFF;
  display: flex;
  flex-direction: column;
  width: calc(100% - 40rpx);
  min-height: 180rpx;
  box-shadow: 0rpx 0rpx 20rpx 1rpx rgba(228, 234, 248, 0.16);
  border-radius: 20rpx 20rpx 20rpx 20rpx;
  opacity: 1;
  margin: 20rpx;
}

.cwxqzt {
  width: 100%;
  height: 240rpx;
  background: #FFFFFF;
  border-radius: 0rpx 0rpx 0rpx 0rpx;
  opacity: 1;
}


.search-name {
  width: calc(100% - 64rpx);
  min-height: 50rpx;
  font-size: 34rpx;

  font-weight: bold;
  color: #353535;
  line-height: 50rpx;
  margin: 32rpx 32rpx 0 32rpx;
}

.nameinfo {
  width: calc(100% - 60rpx);
  min-height: 40rpx;
  margin: 10rpx 30rpx;
  display: flex;
  flex-direction: row;
}

.search-name-xq {
  width: calc(100% - 150rpx);
  height: 100%;
  font-size: 26rpx;
  flex: 1;
  font-weight: 400;
  color: #7E7E7E;
  line-height: 40rpx;
}

.search-dw {
  width: 100rpx;
  height: 40rpx;
  font-size: 28rpx;
  font-weight: 400;
  color: #888888;
  line-height: 40rpx;
  text-align: right;
}

.nameinfo image {
  width: 32rpx;
  height: 32rpx;
  background: #FFFFFF;
  border-radius: 0rpx 0rpx 0rpx 0rpx;
  opacity: 1;
  border: 1rpx solid #707070;
}


.search-dc-num cover-view {
  height: 100%;
  line-height: 54rpx;
}

.search-yw {
  width: calc(100% - 64rpx);
  min-height: 40rpx;
  color: #4768F3;
  line-height: 40rpx;
  margin: 10rpx 30rpx 30rpx 30rpx;
  font-size: 26rpx;
  font-weight: 400;
}

.search-dc-tip {
  width: calc(100% - 60rpx);
  min-height: 33rpx;
  font-size: 26rpx;
  font-weight: bold;
  color: #353535;
  line-height: 33rpx;
  margin: 10rpx 30rpx;
}

.carInfo {
  width: calc(100% - 92rpx);
  height: 264rpx;
  background: #FFFFFF;
  border-radius: 20rpx 20rpx 20rpx 20rpx;
  opacity: 1;
  margin: 0 20rpx 20rpx 20rpx;
  padding: 11rpx 30rpx;
}

.title-value-box-line {
  width: 100%;
  height: 89rpx;
  background: #FFFFFF;
  opacity: 1;
  line-height: 89rpx;
  font-size: 26rpx;

  font-weight: 400;
  color: #353535;
  border-bottom: 1rpx solid #F6F6F6;
}

.title-value-box-line:last-child {
  border-width: 0;
}

.title-value-box-line-title {
  padding-top: 26rpx;
  padding-bottom: 26rpx;
  width: 200rpx;
  height: 37rpx;
  font-size: 28rpx;
  font-weight: 400;
  color: #353535;
  line-height: 40rpx;
}

.title-value-box-line-value {
  padding-top: 26rpx;
  padding-bottom: 26rpx;
  flex: 1;
  font-size: 28rpx;
  font-weight: 400;
  color: #353535;
  line-height: 40rpx;
}

.picker {
  text-align: left;
}

.qhBtn {
  width: 102rpx;
  font-size: 28rpx;
  font-weight: 400;
  color: #888888;
  position: absolute;
  right: 30rpx;
}

.qhBtn:after {
  content: " ";
  display: inline-block;
  width: 15rpx;
  height: 15rpx;
  border-width: 4rpx 4rpx 0 0;
  border-color: #888888;
  border-style: solid;
  transform: matrix(0.71, 0.71, -0.71, 0.71, 0, 0);
  position: absolute;
  top: 24rpx;
  margin-top: -12rpx;
  right: 24rpx;
  border-radius: 1rpx;
}

.title-value-box-line-value {
  text-align: left;
  width: calc(100% - 400rpx);
}

.tipWord {
  width: calc(100% - 100rpx);
  margin-left: 50rpx;
  min-height: 40rpx;
  font-size: 22rpx;
  font-weight: 400;
  color: #888888;
  line-height: 40rpx;
}

.bottomBtn {
  position: absolute;
  width: 100%;
  height: 120rpx;
  bottom: 0;
}


.btnBox-down-right {
  margin: 0 20rpx;
  width: calc(100% - 40rpx);
  height: 80rpx;
  background: linear-gradient(90deg, #458BFF 0%, #08D7AE 100%);
  box-shadow: 0rpx 3rpx 10rpx 1rpx rgba(37, 107, 245, 0.2);
  border-radius: 40rpx;
  text-align: center;
  font-size: 36rpx;
  color: #FFFFFF;
  line-height: 80rpx;
}
.info-one-carTool-text {
  flex: 1;
}

.info-one-carTool-btn {
  min-height: 40rpx;
  width: 60rpx;
  font-size: 28rpx;
  font-weight: 400;
  color: #888888;
}