var util = require('../../../../utils/util');
var api = require('../../../../config/api.js');
var app = getApp();
//老版本的包月办理
Page({
  data: {
    showPlateNoBox: false, //显示车牌选择
    isRenewal: false, //是否是续费，续费时界面一致，但不可切换车牌，此按钮隐藏
    plateNo: "", //从其他页面传过来的车牌
    certCode: '', //包月凭证编号 从续费个页面传过来的，购买和再次购买是无此参数的
    payList: [],
    selectPay: '', //balance WeChatPay选择的支付方式
    balanceText: '', //当前余额
    selectIndex: 0, //选择套餐的索引
    parkId: '',
    isClick: true,
    carList: [],
    packageList: [],
    imgUrl: api.imgUrl,
    parkName: '',
    parkingAddress: '',
    carNum: '',
    vehicleId: '',
    tradeNo: ""
  },
  onLoad(options) {
    console.log(app.globalData.payType)
    var isRenewal = false;
    if (options['isRenewal'] != undefined) {
      isRenewal = JSON.parse(options.isRenewal);
    }
    this.setData({
      parkId: options.parkId,
      isRenewal: isRenewal
    })
    var plateNo = options.plateNo;
    if (plateNo != undefined) {
      this.setData({
        plateNo: plateNo
      })
    }
    if (options.certCode != undefined) {
      this.setData({
        certCode: options.certCode
      })
    }
  },
  onShow() {
    this.getPackageList();
  },
  select(e) {
    var currentTarget = e.currentTarget;
    this.setData({
      selectIndex: currentTarget.id
    })
  },
  selectPayMode(e) {
    var currentTarget = e.currentTarget;
    this.setData({
      selectPay: currentTarget.id,
    })
  },
  pay() { //需要向后台 获取参数
    util.showLoading('正在加载…')
    var that = this;
    var data = that.data;
    var selectPay = data.selectPay;
    that.setData({
      isClick: false //在点击一次后，点击状态变为关闭，默认为开启
    })
    var clock = setTimeout(() => { //定义一个延时操作setTimeout
        that.setData({
          isClick: true
        })
        clearInterval(clock);
      }, 3000 //在3秒后，点击状态恢复为默认开启状态
    )
    if (selectPay === "BALANCE_PAY") {
      // if (data.balanceText < data.money) {//暂时屏蔽
      //   wx.hideLoading();
      //   util.showToast("余额不足，请充值")
      //   return;
      // }
    }
    var selectPackage = data.packageList[data.selectIndex];
    var url = api.payMonthly;
    var param = {
      groupId:selectPackage.groupId,//1.11新增，据说是运营改了规则所以海康需要这两个字段
      scope:selectPackage.scope,//1.11新增，据说是运营改了规则所以海康需要这两个字段
      bagType: 0, //包期类型 0 包月 1 包天
      duration: selectPackage.duration,
      endTime: selectPackage.endTime,
      id: data.parkId, //传停车场ID
      payType: selectPay,
      plateNo: data.plateNo,
      price: selectPackage.price,
      ruleId: selectPackage.ruleId,
      plateColor: '',
    };
    if (data.isRenewal) { //续费
      url = api.payMonthlyRenewal;
      param = {
        groupId:selectPackage.groupId,//1.11新增，据说是运营改了规则所以海康需要这两个字段
        scope:selectPackage.scope,//1.11新增，据说是运营改了规则所以海康需要这两个字段
        bagType: 0, //包期类型 0 包月 1 包天
        certCode: data.certCode,
        duration: selectPackage.duration,
        endTime: selectPackage.endTime,
        id: data.parkId, //传停车场ID
        payType: selectPay,
        price: selectPackage.price,
        ruleId: selectPackage.ruleId,
      }
    }
    util.request(url, param, 'POST').then(function (res) {
      var infos = res.result;
      if (res.code == '0') {
        var tradeNo = infos.tradeNo;
        wx.hideLoading()
        that.setData({
          tradeNo: tradeNo
        })
        if (infos.payStatus == 'PAID') { //说明是无需付款可以直接返回成功
          that.setData({
            tradeNo: infos.tradeNo
          })
          var clock = setTimeout(() => {
            wx.redirectTo({
              url: '/pages/monthlyPackage/details/refund/result/index?id=' + tradeNo + "&type=zf"
            })
            clearInterval(clock);
          }, 1500)
        } else {
          that.payWeiPay(infos.timestamp, infos.nonceStr, infos.packageVal, infos.paySign, infos.signType)
        }
      } else if (res.code == "3006") {
        wx.hideLoading();
        wx.showModal({
          title: '温馨提示',
          content: '月卡续费功能升级中',
          confirmText: '我知道了',
          showCancel: false,
          success(res) {
            if (res.confirm) {
              console.log('用户点击确定')
            }
          }
        })
      } else {
        wx.hideLoading();
        util.showToast(res.message);
      }
    });
  },
  payWeiPay: function (timeStamp, nonceStr, packageWord, paySign, signType) {
    var that = this;
    var data = that.data;
    wx.requestPayment({ //吊起微信支付
      timeStamp: timeStamp,
      nonceStr: nonceStr,
      package: packageWord,
      signType: signType,
      paySign: paySign,
      success(res) {
        wx.hideLoading();
        console.log('支付成功');
      },
      fail(res) {
        wx.hideLoading();
        console.log('支付失败');
      },
      'complete': function (res) {
        console.log('支付完成');
        if (res.errMsg == 'requestPayment:ok') {
          var clock = setTimeout(() => {
            wx.redirectTo({
              url: '/pages/monthlyPackage/details/refund/result/index?id=' + data.tradeNo + "&type=zf"
            })
            clearInterval(clock);
          }, 1500)
        } else {
          wx.showModal({
            title: '提示',
            content: '支付失败'
          });
        }
        return;
      }
    })
  },
  getPackageList() {
    util.showLoading('正在加载…')
    var that = this;
    var data = that.data;
    var url = api.getMonthPackages + "?parkId=" + data.parkId;
    if (data.isRenewal) { //续费
      url = api.getRenewalMonthPackages + "?certCode=" + data.certCode;
    }
    util.request(url, {}, 'GET').then(function (res) {
      if (res.code == '0') {
        var result = res.result;
        that.setData({
          parkName: result.parkName,
          parkingAddress: result.parkAddress,
          carList: result.vehicles,
          packageList: result.packages
        });
        that.setPayMethod(result.paymentMethods);
        wx.hideLoading();
      } else {
        wx.hideLoading();
        util.showToast(res.message)
      }
    })
  },
  setPayMethod(list) { //获取支付方式
    this.setData({
      payList: list
    });
    if (list.length > 0) {
      var payType = list[0]['payType'];
      console.log("获取支付方式成功---默认支付类型" + payType)
      this.setData({
        selectPay: payType
      });
      if (payType === "BALANCE_PAY") {
        this.setData({
          balanceText: list[0]['iconUrl'] //用来对比余额
        });
      }
    }
  },
  showBox: function () {
    this.setData({
      showPlateNoBox: true
    })
  },
  selectItem: function (e) {
    var detail = e.detail;
    this.setData({
      plateNo: detail.plateNo
    })
  }
})