@import '/pages/common/common.wxss';

page {
  background-color: #f6f7fb;
}

.header {
  position: relative;
  margin-bottom: 10rpx;
  width: 100%;
  height: 80rpx;
  font-size: 34rpx;
  font-weight: bold;
  color: #000000;
  line-height: 80rpx;
  display: flex;
  flex-direction: row;
  background-color: #ffffff;
}
.header .picker-box {
  margin: auto;
  width: auto;
  height: 100%;
}
.header .picker-box.month {
  margin-left: 28rpx;
}
.header .picker-box .picker-content {
  display: flex;
  flex-direction: row;
}
.header .picker-box .value {
  margin-right: 20rpx;
  width: auto;
}
.header .picker-box .icon {
  margin-top: 25rpx;
  border-top: 4rpx solid #888888;
  border-left: 4rpx solid #888888;
  height: 20rpx;
  width: 20rpx;
  transform: rotate(225deg);
}

.header .invoice {
  position: absolute;
  top: 0;
  right: 28rpx;
  width: 115rpx;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.header .invoice .icon {
  margin-right: 5rpx;
  width: 26rpx;
  height: 24rpx;
}
.header .invoice .text {
  font-size: 26rpx;
  font-weight: 400;
  color: #256bf5;
}
.order {
  padding: 30rpx 20rpx 20rpx 20rpx;
  margin-bottom: 10rpx;
  width: 100%;
  min-height: 180rpx;
  background-color: #ffffff;
  box-sizing: border-box;
  display: flex;
  flex-direction: row;
}
.left {
  flex: 1;
}
.left .name {
  margin-bottom: 10rpx;
  width: 100%;
  min-height: 48rpx;
  font-size: 34rpx;
  font-weight: bold;
  color: #353535;
  line-height: 48rpx;
}
.left .address {
  margin-bottom: 10rpx;
  width: 100%;
  height: 40rpx;
  font-size: 28rpx;
  color: #353535;
  line-height: 40rpx;
  font-weight: 400;
}
.left .extra {
  margin-bottom: 10rpx;
  width: 100%;
  height: 37rpx;
  font-size: 26rpx;
  font-weight: 400;
  color: #7e7e7e;
  line-height: 37rpx;
}

.left .extra .state {
  margin-left: 20rpx;
}
.left .extra .state.red {
  color: #da5937;
}

.left .extra .state.green {
  color: #41e0ac;
}
.right {
  width: 172rpx;
  border-left: 1px solid #dfdfdf;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: 34rpx;
  font-weight: bold;
  color: #353535;
}

.list-tool {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  font-weight: bold;
  color: #353535;
  text-align: center;
  padding-left: 32rpx;
}
.list-tool-select {
  width: 131rpx;
  height: 38rpx;
  background: #ffffff;
  border-radius: 10rpx;
  line-height: 38rpx;
  margin-right: 16rpx;
}
.list-tool .selected {
  width: 131rpx;
  height: 38rpx;
  background: #e5f0ff;
  border-radius: 10rpx;
  color: #085ae5;
}

.qf-listDiv-right-three {
  font-size: 30rpx;
  font-weight: 400;
  color: #ffffff;
  line-height: 56rpx;
  text-align: center;
  margin: 0;
  width: 130rpx;
  height: 56rpx;
  background: linear-gradient(90deg, #ff5745 0%, #ffae00 100%);
  border-radius: 52rpx;
  opacity: 1;
  margin-top: 8rpx;
}
