<view class="ai-mask" wx:if="{{show}}" catchtouchmove="preventTouchMove"></view>
<view class="ai-dialog" wx:if="{{show}}">
  <view class="ai-header">
    <image class="ai-logo" src="/image/logo.png" />
    <view class="ai-title">畅行桂林AI助手</view>
    <image class="ai-close" src="/image/close.png" catchtap="closeAI" />
  </view>
  <scroll-view
    class="ai-content"
    scroll-y="true"
    scroll-into-view="{{scrollToView}}"
    scroll-with-animation="true"
    enhanced="true"
    show-scrollbar="false"
  >
    <block wx:for="{{messages}}" wx:key="index">
      <view class="ai-msg {{item.role}}" id="msg-{{index}}">
        <text wx:if="{{item.role === 'user'}}">{{item.text}}</text>
        <block wx:if="{{item.role === 'ai' && index !== messages.length - 1}}">
          <towxml wx:if="{{item.nodes}}" nodes="{{item.nodes}}" />
          <text wx:else>{{item.text}}</text>
        </block>
        <block wx:if="{{item.role === 'ai' && index === messages.length - 1 && !isStartRequesting}}">
          <towxml wx:if="{{item.nodes}}" nodes="{{item.nodes}}" />
          <text wx:else>{{item.text}}</text>
        </block>
        <view
          class="loading-spinner"
          wx:if="{{isStartRequesting && index === messages.length - 1 && item.role === 'ai'}}"
        ></view>
      </view>
    </block>
    <view id="scroll-bottom" style="height: 10px"></view>
  </scroll-view>
  <view class="ai-quick-options">
    <scroll-view scroll-x="true" class="quick-options-scroll">
      <view class="quick-option-items">
        <view
          class="quick-option-item"
          wx:for="{{quickOptions}}"
          wx:key="index"
          data-prompt="{{item.prompt}}"
          bindtap="handleQuickOption"
        >
          {{item.prompt}}
        </view>
      </view>
    </scroll-view>
  </view>

  <view class="ai-input-bar">
    <view class="ai-textarea-container">
      <!-- 语音波浪动画 -->
      <view class="voice-wave-container" wx:if="{{isVoice && recordState === 1}}">
        <view class="voice-wave-animation">
          <view wx:for="{{voiceWaves}}" wx:key="index" class="voice-wave" style="{{item.style}}"></view>
        </view>
      </view>
      <textarea
        wx:if="{{isVoice}}"
        class="ai-textarea ai-speak"
        placeholder="按住说话"
        value="按住说话"
        disabled="{{true}}"
        bindtouchstart="touchStart"
        bindtouchend="touchEnd"
      ></textarea>
      <textarea
        wx:else
        focus
        class="ai-textarea"
        placeholder="发消息"
        value="{{inputValue}}"
        bindinput="onInput"
        auto-height="{{true}}"
        fixed="true"
        maxlength="1000"
      ></textarea>

      <view class="ai-icon-container">
        <image
          wx:if="{{inputValue.length<=0}}"
          class="ai-voice-icon"
          src="{{isVoice ? '/image/keyboard.png' : '/image/voice.png'}}"
          catch:tap="toggleVoiceInput"
        ></image>
        <image wx:else class="ai-voice-icon" src="/image/send.png" catch:tap="handleSendOrStop"></image>

        <image
          wx:if="{{inputValue.length <= 0}}"
          class="ai-voice-icon2"
          src="/image/reset.png"
          catch:tap="clearMessages"
        ></image
      ></view>
    </view>
  </view>
</view>
