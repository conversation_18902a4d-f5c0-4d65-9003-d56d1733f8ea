// // components/dialog/dialog.js
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    isShow: {
      type: Boolean,
      default: false
    },
    rules:{
      type:Array,
      default:[]
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
      type: {//0-非新能源 1-新能源 2-车辆通用
          0:'非新能源',
          1:'新能源',
          2:'车辆通用',
      }, 
  },
  /**
   * 组件的方法列表
   */
  methods: {
      //点击显示底部弹出
      changeRange: function () {
          this.showModal();
          console.log('我是弹窗打开----');
      },

      //底部弹出框
      showModal: function () {
          // 背景遮罩层
          var animation = wx.createAnimation({
              duration: 50,
              timingFunction: "linear",
              delay: 0
          })
          //this.animation = animation
          animation.translateY(50).step()
          this.setData({
              animationData: animation.export(),
              isShow: true
          })
          setTimeout(function () {
              animation.translateY(0).step()
              this.setData({
                  animationData: animation.export()
              })
          }.bind(this), 50)
      },

      //点击背景面任意一处时，弹出框隐藏
      hideModal: function (e) {
          //弹出框消失动画
          var animation = wx.createAnimation({
              duration: 10,
              timingFunction: "linear",
              delay: 0
          })
          //this.animation = animation
          animation.translateY(10).step()
          this.setData({
              animationData: animation.export(),
          })
          setTimeout(function () {
              animation.translateY(0).step()
              this.setData({
                  animationData: animation.export(),
                  isShow: false
              })
          }.bind(this), 10)
      },

      // 选择选项-----弹出框选择添加类型
      getValueTap(e) {
          var index = e.currentTarget.dataset.dialogid;
          this.triggerEvent("selectRule",index);
          this.hideModal();
      },
      // 禁止滚动
      catchTouchHandler(){
       console.log('禁止滚动' )
       return false;
     }
  },
  // 生命周期
  lifetimes: {
      ready: function () {

      },
  }
})