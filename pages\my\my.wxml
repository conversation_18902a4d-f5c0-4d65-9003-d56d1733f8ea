<view class="saveOutView">
  <cu-custom bgColor="default-bg" contentTitle="客户详情"></cu-custom>
  <view class="self-adaptionBox">
    <image
      src="{{imgUrl}}/sy/img_bg_1.png"
      mode="scaleToFill"
      class="navigationPng"
      style="width:100%;z-index: 1;	height:{{imageHeight}}px;"
    ></image>
    <view class="userinfo">
      <view class="userinfo-avatar" bindtap="login"
        ><!-- 12.4新增点击头像打开登陆弹窗 -->
        <image class="userinfo-avatar" src="../../image/logo.png"></image>
      </view>
      <view class="userinfo-text" bindtap="login">
        <!-- <view class="username" >{{username}}</view> -->
        <view class="phone">{{phone}}</view>
      </view>
      <view class="line"></view>
      <view class="userdata-v">
        <view class="userdata-num">
          <image
            src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABkAAAAaCAYAAABCfffNAAAAAXNSR0IArs4c6QAABRNJREFUSEu1VmlMVFcUPvfNe2+YeTBLKDJQtVSpIKghWgktOjIO2NqoNa1pkwaxpmJdkbQVl2iYonHBlOK+pwVMk7a2sWpqFUeHURviFjcEoSJuMJYqM4/Z3t68GaGoM1DS9P69597vft859zsHQZ9LQlkWmwIgAY8CJSaHdwIjArTwNkuWAICkvq5A4QLGzL1I6BIojYjhBgxJ8aIEMQhDajleEiUvhqBdlFArJvIOZ4uHvrTndS7cXS+C/CApzH806CSMSBocrTBOHR1pTjIoR8TrSJ0mAiPli2i/yLY6Oecth//Gkctu630nXwMc32hNTHbCB0h4HuwZkBRLHRmnJAZqKSxngQnPH5dEpClxpOhNDoaXBPst7squ0/xel0esbmO4BzctqWzPM90gMkC8ihg6chCXVzTZM9+glbR9ad1z/5ELc208pt55/T5R2erjbvcECoLIEt1uemXUYHpu8fSWQq2KV/YHoCuW9uGM5VBC+bV7mj3Woa/d7ZIuAGJeVx+tjRRn7Mit3hir9faLwfOPcbjU9MIDOUUuN3bQunL4Y3kfyVWkHUKNXfnOb1vMKfVjwjEQWR7oc40ACEDz5jDASDwsWevN4ZfW/Tq1wNXsvCBXHZJZDIr+K3/XzE1rlTgfMskiK4DL/hB4pz9wMa5XgdYYDxgRuiYYHhfmVS1ddf/xS3tlNsi0oSm1IKuifHra8exQTxNZEVxnnND5hOD2OD+3YSCifF3ZhMhontCO1wFGBP7nC+vQlbdObrHNKjy9PPEmMm9szNk8Y0VlalyDIVSw0+4Gul3J7XyyvOICk1kpiQhlqG2z5uk3zYyK5QjdOCokSF1bsmPJwfV5gu/haWTa0PjRT7Pz9uspZ0SoaMcZ4DY3FlVc9I7fwTN4fUAuJRqerj6xqCC5bGZsJhChznV4dP73v6n8RPITP6OJpU1zjuVN202qpZC8S44vqz5z11jCu7GLNsurgaRkWe5EYBFcevawmuIV2V9NDAXCepE4ufLwp5SX+i4AcnTalN2qWBQSZK116Ulbs6lE8KILXSBvlP2uiuBj0t9OPFVcZPraFArE90gSpxw+GgSR5TqQ8eH++BG+kHL5eBVXWlNQdbbZtI1npHpG04YiGH2KaWjtoi+Mm3NVuD9kLbfeUPlza78PyiUnfk3s4sqMSS0GpAhtyj5OxZWeK6iy38mskBNvTjz78WeZ4QEkQYLa6gTHasfWrsQ3pc7WbC1/b9SRbGpIeDeRGZXVLrRjIKHCjO3GcAxk6bzNDBy8OtX6befiJcESXlcfbSBb80tj5qwdkI4r8MheTbdPS+PdAvx5nheWt+9b1crGBz9j0FZ0Yxdp1myZoLeO0Y5WgyLYAPu9REYE52Uv1HSYL22jV/9jK90GSbhnlGjnl8ZRDo1mpApwqn+MeI8A9HUftHkM9OqO7ctoQfNjt0EGnvvU6lPIq3OXRBUXanCXkhpMgmogCeGKoYumnGTfAxY891joFLRMOf1leZ03be+ppMSWZ6xePtDVtJKJa3nzqPULBigcGoQDKGMIIPU44BQGGBmsPpGVgPeIwHbwwLRzIPEA7YKB3ulZsaOBGxWmaT19Vnf7xV05uept+emEPY1EbK+6sRIpnOeMV6rcC/bRov5Er+23O8s9Bok4/K5xEvlL9hC8ITUOf6ijUGdgkPBIUayDf9l5m0+uO8G+e7KNf8WORO7WvxokepbT/zsSvVC4/324+xsd9HcHzqzIfwAAAABJRU5ErkJggg"
            style="height: 26rpx; width: 26rpx"
          ></image>
          {{balance}}
        </view>
        <view
          class="userdata-title"
          bindtap="goToPage"
          data-check="true"
          data-route="/pages/recharge/index"
          data-type="3"
          >余额充值</view
        >
      </view>
    </view>
    <view class="center-one">
      <view
        id="tingchedingdan"
        class="center-one-v"
        bindtap="goToPage"
        style="margin-left: 50rpx"
        data-check="true"
        data-route="/pages/supplementaryPayment/index"
        data-type="1"
      >
        <view class="center-one-v-img">
          <image src="{{imgUrl}}/sy/buttoin_tingchedingdan.png"></image>
          <view class="cu-tag badge" wx:if="{{unpaid>0}}">{{unpaid>99?'99+':unpaid}}</view>
        </view>
        <view class="center-one-v-text">待付订单</view>
      </view>
      <!-- <view id="yuyuedingdan" class="center-one-v " bindtap="goToPage" data-check="true" data-route="/pages/vehicleReservation/index">
        <view class="center-one-v-img">
          <image src="{{imgUrl}}/sy/button_yuyuedingdan.png"></image>
        </view>
        <view class="center-one-v-text">预约订单</view>
      </view> -->
      <view
        id="youhuiquan"
        class="center-one-v"
        bindtap="goToPage"
        data-check="true"
        data-route="/pages/my/discountCoupon/index"
        data-type="2"
      >
        <view class="center-one-v-img">
          <image src="{{imgUrl}}/sy/parkingDiscounts.png"></image>
          <!--<view class="cu-tag badge" wx:if="{{coupon>0}}">{{coupon}}</view>-->
        </view>
        <view class="center-one-v-text">优惠券</view>
      </view>
      <view
        id="dianzifapiao"
        class="center-one-v"
        bindtap="goToPage"
        data-check="true"
        data-route="/pages/orderQuery/index"
        data-type="1"
      >
        <view class="center-one-v-img">
          <image src="{{imgUrl}}/sy/button_dianzifapiao.png"></image>
        </view>
        <view class="center-one-v-text">全部订单</view>
      </view>
    </view>
    <view class="swiper">
      <swiper
        indicator-dots="{{indicatorDots}}"
        autoplay="{{autoplay}}"
        interval="{{interval}}"
        duration="{{duration}}"
        style="height: 261rpx"
      >
        <block wx:for="{{background}}" wx:key="index">
          <swiper-item data-item="{{item}}" bind:tap="onSwiperTap">
            <view class="swiper-item">
              <image
                src="{{item.fileUrl}}"
                style="position: absolute; width: 100%; height: 100%; border-radius: 30rpx"
              ></image>
            </view>
          </swiper-item>
        </block>
      </swiper>
    </view>
    <view class="common-title backgroundImg">常用功能</view>
    <scroll-view class="scrollViewSa" scroll-y="true">
      <view class="scroll-inner">
        <view
          wx:for="{{list}}"
          wx:key="index"
          id="{{item.id}}"
          class="navigator"
          bindtap="goModulePage"
          style="{{index==0 ?'border-radius: 20rpx 20rpx 0 0;':''}} {{index == (list.length-1)?'border-radius: 0 0 20rpx 20rpx; margin-bottom:32rpx':'' }}"
          data-route="{{item.url}}"
        >
          <view class="navigator-text">{{item.name}}</view>
          <view class="navigator-arrow"></view>
        </view>
      </view>
    </scroll-view>
  </view>
</view>
