<!--pages/supplementaryPayment/details/index.wxml-->
<view wx:if="{{!showSucc}}">
  <cu-custom bgColor="white-bg" contentTitle="取消月卡" isBack="true"></cu-custom>
  <view class="details-box">
    <!-- <view class="details-box-title" style="padding-top: 82rpx">
      <view class="details-box-title">
        <view class="details-box-title-fh">¥</view>
        <view class="details-box-title-value">{{info.refundableFee}}</view>
      </view>
    </view>
    <view class="details-box-title-two">可退额度</view> -->
    <view class="title-value-box">
      <view class="title-value-box-line">
        <view class="title-value-box-line-title">已享受天数:</view>
        <view class="title-value-box-line-value">{{info.enjoyedDays}}天</view>
      </view>
      <view class="title-value-box-line">
        <view class="title-value-box-line-title">可退天数:</view>
        <view class="title-value-box-line-value">{{info.surplusDays}}天</view>
      </view>
      <view class="title-value-box-line">
        <view class="title-value-box-line-title">包月车场名称:</view>
        <view class="title-value-box-line-value"> {{info.parkName}}</view>
      </view>
      <view class="title-value-box-line">
        <view class="title-value-box-line-title">车牌号码:</view>
        <view class="title-value-box-line-value">{{info.plateNo}}</view>
      </view>
      <view class="title-value-box-line">
        <view class="title-value-box-line-value" style="text-align: left">
          <view style="width: 300rpx; color: #353535; font-weight: bold"> 退款说明： </view>
          <view> 取消包月需要与客服核实包期月卡具体使用情况。 </view>
          <view> 情况一:车主购买包期月卡生效超过三天则不退不换。 </view>
          <view> 情况二:车主购买包期月卡生效不超过三天(含三天)则退款费用按照包期金额-实际停车费用进行退还。 </view>
          <view>
            情况三:由于施工管制或遭遇自然灾害等客观因素导致月卡无法使用可以按照包期金额/包期天数*(包期截至时间-申请退费时间)的计算方式进行退款。
          </view>
        </view>
      </view>
    </view>
  </view>
  <button class="convention_button convention_bottom_btn" bindtap="refunding">提交申请</button>
</view>
<view wx:else>
  <cu-custom bgColor="white-bg" contentTitle="月卡详情" isBack="true"></cu-custom>
  <view class="vBox">
    <view class="successStatus-icon">
      <image src="../../../../image/suc.png" class="successStatus-icon-img"></image>
    </view>
    <view class="successStatus-text">取消包月申请成功</view>
    <view class="successStatus-textT">客服审核通过后退回原支付账户</view>
  </view>
  <button class="convention_button" bindtap="back">返回我的月卡</button>
</view>
