/* pages/parkingSpaceInquiry/index.wxss */
@import '../../common/ccxqlistCss.wxss';
@import '../../common/common.wxss';
.mapDiv
{
  width: 100%;
  height: calc(100% - 334rpx);
}
map{
  width: 100%;
  height: 100%;
}
.flex-wrp{
  display:flex;
}
.listDiv-d{
  width: calc(100% - 130rpx);
  min-height: 180rpx;
  background: #FFFFFF;
  box-shadow: 0rpx 10rpx 0rpx 1rpx rgba(228,234,248,0.16);
  border-radius: 20rpx 20rpx 20rpx 20rpx;
  opacity: 1;
  position: fixed;
  bottom: 60rpx;
  right: 50rpx;
  padding: 0 15rpx 10rpx 15rpx;
}
.customCallout {
  height: 48rpx;
  line-height: 48rpx;
  background-color: #ffffff;
  border-radius: 12px;
  box-shadow: 0 0 3px 1px rgba(0, 0, 0, 0.2);
  align-items: center;
}

.icon {
  width: 40rpx;
  height: 40rpx;
  padding-left: 5rpx;
}

.content {
  font-size: 26rpx;
  font-weight: 400;
  color: #353535;
  text-align: center;
  line-height: 37rpx;
  height: 37rpx;
  min-width: 180rpx;
  border-radius: 12px;
  z-index: 1;
}

.view-search-left-dw {
  position: absolute;
  right: 60rpx;
  width: 74rpx;
  height: 74rpx;
  background: #FFFFFF;
  box-shadow: 0rpx 10rpx 20rpx 0rpx rgba(173, 190, 222, 0.5);
  border-radius: 14rpx;
}
