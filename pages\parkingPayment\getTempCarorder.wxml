<view class="saveOutView">
  <cu-custom bgColor="white-bg" contentTitle="停车缴费" isBack="true"></cu-custom>
  <view class="header">
    <view
      class="order-type {{currentOrderType == 'stopping' ? 'active' : ''}}"
      bindtap="changeOrderType"
      data-type="stopping"
    >
      正在停车
    </view>
    <view
      class="order-type {{currentOrderType == 'arrears' ? 'active' : ''}}"
      bindtap="changeOrderType"
      data-type="arrears"
    >
      欠费补缴
      <view class="cu-tag badge" wx:if="{{total>0}}">{{total>99?'99+':total}}</view>
    </view>
  </view>
  <view wx:if="{{currentOrderType=='arrears'&&list.length>0}}" class="listBox columnView">
    <scroll-view scroll-y="true" class="scrollViewSa" bindscrolltolower="bindReachBottom">
      <block wx:for="{{list}}" wx:key="id">
        <view
          class="qf-listDiv"
          bindtap="changeCheckState"
          data-info="{{item}}"
          data-checkstate="{{item.checkstate}}"
          data-index="{{index}}"
        >
          <view class="left" hidden="{{mode == 'other'}}">
            <image src="/image/{{item.checkstate ? 'yes-icon-select' : 'yes-icon'}}.png" />
          </view>
          <view class="qf-listDiv-left">
            <view class="qf-listDiv-left-one">{{item.parkName}}</view>
            <view class="qf-listDiv-left-three">共{{item.parkPeriodTime}}</view>
          </view>
          <view class="qf-listDiv-right">
            <view class="qf-listDiv-right-one">需缴费</view>
            <view class="qf-listDiv-right-two">￥{{item.totalFee}}</view>
            <view class="qf-listDiv-right-three" catchtap="goxq" id="{{item.uniqueId}}">缴费</view>
          </view>
        </view>
      </block>
    </scroll-view>
    <view class="backWihte columnView" style="margin-top: 10rpx; height: 242rpx">
      <view class="info-bt">
        <view class="icon-sjx-left-tilte"></view>
        <view class="info-bt-text">支付方式</view>
      </view>
      <view class="paymentListBox">
        <block wx:for="{{payList}}" wx:key="index">
          <view
            id="{{item.payType}}"
            class="infoBox  {{selectPay == item.payType ? 'choosePay':''}}"
            bindtap="selectPayMode"
            wx:if="{{item.payType=='BALANCE_PAY'}}"
          >
            <view class="infoBox-text" style="margin-top: 14rpx">余额</view>
            <view class="infoBox-text" id="ye" data-money="{{item.iconUrl}}">{{item.iconUrl}}元</view>
          </view>
          <view
            id="{{item.payType}}"
            class="infoBox {{selectPay == item.payType ? 'choosePay':''}}"
            style="display: flex; flex-direction: column"
            bindtap="selectPayMode"
            wx:else
          >
            <image
              class="img"
              src="{{item.iconUrl}}"
              style="width: 48rpx; height: 48rpx; position: static; margin: 14rpx auto 0"
            ></image>
            <view class="infoBox-text">微信支付</view>
          </view>
        </block>
      </view>
    </view>
    <view class="footer rowView" hidden="{{currentInvoiceState == 'other'}}">
      <view class="checkbox" bindtap="batchSelected" data-type="all">
        <image src="/image/{{selectedList.length == list.length ? 'yes-icon-select' : 'yes-icon'}}.png" />全选
      </view>
      <view class="totalBox columnView">
        <view class="text">账单个数：{{selectedList.length}}个</view>
        <view class="text">补缴金额：￥{{invoiceAmout}}</view>
      </view>
      <view class="btn" bindtap="gotoInvoiceTitle">支付</view>
    </view>
  </view>
  <view class="listBox columnView" wx:elif="{{currentOrderType=='stopping'&&finded}}">
    <scroll-view scroll-y="true" class="scrollViewSa">
      <view wx:if="{{imageState!==0}}" class="rowView">
        <view class="cwxqzt">
          <image
            class="cwxqzt-img"
            mode="scaleToFill"
            src="{{info.parkingPicUrl}}"
            lazy-load="true"
            bindtap="previewSqs"
            data-src="{{info.parkingPicUrl}}"
            wx:if="{{info.parkingPicUrl!=null}}"
          ></image>
          <view class="cwxqzt-text" wx:else>暂无入车照片</view>
        </view>
        <view class="cwxqzt">
          <image
            class="cwxqzt-img"
            mode="scaleToFill"
            src="{{info.outImageUrl}}"
            lazy-load="true"
            bindtap="previewSqs"
            data-src="{{info.outImageUrl}}"
            wx:if="{{info.outImageUrl!=null}}"
          ></image>
          <view class="cwxqzt-text" wx:else>暂无出车照片</view>
        </view>
      </view>
      <view class="backWihte columnView paddingView-top">
        <view class="title-value-box-line">
          <view class="title-value-box-line-title">停车场:</view>
          <view class="title-value-box-line-value">{{info.parkName}}</view>
        </view>
        <view class="title-value-box-line">
          <view class="title-value-box-line-title">车牌号码: </view>
          <view class="title-value-box-line-value">{{info.plateNo}}</view>
        </view>
        <view class="title-value-box-line">
          <view class="title-value-box-line-title">入场时间:</view>
          <view class="title-value-box-line-value">{{info.enterTime}}</view>
        </view>
        <view class="title-value-box-line">
          <view class="title-value-box-line-title">停车时长:</view>
          <view class="title-value-box-line-value">{{info.parkPeriodTime}}</view>
        </view>
        <!-- <view class="title-value-box-line">
          <view class="title-value-box-line-title">免费时长:</view>
          <view class="title-value-box-line-value">{{info.freeTime}}</view>
        </view> -->
      </view>
      <block wx:if="{{activeLeaveState==0}}">
        <view class="backWihte columnView paddingView-top" style="margin-top: 10rpx">
          <view class="title-value-box-line">
            <view class="title-value-box-line-title">账单金额:</view>
            <view class="title-value-box-line-value" wx:if="{{info.evDeductMoney==0.0}}">¥{{info.billMoney}}</view>
            <view class="title-value-box-line-value" wx:else>
              <text class="xny">新能源车立减{{info.evDeductMoney}}元</text>
              <text class="lj">¥{{info.totalCost}}</text>
              <text class="zh">¥{{info.billMoney}}</text>
            </view>
          </view>
          <view class="title-value-box-line">
            <view class="title-value-box-line-title">优惠券:</view>
            <view class="title-value-box-line-value rowView">
              <view
                style="flex: 1; {{info.couponName==''||info.couponName==null?'':'color: #DA5937;'}}"
                bindtap="selectDiscountCoupon"
                >{{info.couponName==""||info.couponName==null?discountCoupon:info.couponName}}</view
              >
              <view class="icon-jt-left"></view>
            </view>
          </view>
          <view class="title-value-box-line">
            <view class="title-value-box-line-title">已付金额:</view>
            <view class="title-value-box-line-value">¥{{info.actualPaidMoney}}</view>
          </view>
          <view class="title-value-box-line">
            <view class="title-value-box-line-title">待付金额:</view>
            <view class="title-value-box-line-value">¥{{info.totalFee}}</view>
          </view>
        </view>
        <view class="backWihte columnView" style="margin-top: 10rpx">
          <view class="info-bt">
            <view class="icon-sjx-left-tilte"></view>
            <view class="info-bt-text">支付方式</view>
          </view>
          <view class="paymentListBox">
            <block wx:for="{{payList}}" wx:key="index">
              <view
                id="{{item.payType}}"
                class="infoBox  {{selectPay == item.payType ? 'choosePay':''}}"
                bindtap="selectPayMode"
                wx:if="{{item.payType=='BALANCE_PAY'}}"
              >
                <view class="infoBox-text" style="margin-top: 14rpx">余额</view>
                <view class="infoBox-text" id="ye" data-money="{{item.iconUrl}}">{{item.iconUrl}}元</view>
              </view>
              <view
                id="{{item.payType}}"
                class="infoBox {{selectPay == item.payType ? 'choosePay':''}}"
                style="display: flex; flex-direction: column"
                bindtap="selectPayMode"
                wx:else
              >
                <image
                  class="img"
                  src="{{item.iconUrl}}"
                  style="width: 48rpx; height: 48rpx; position: static; margin: 14rpx auto 0"
                ></image>
                <view class="infoBox-text">微信支付</view>
              </view>
            </block>
          </view>
        </view>
        <view class="infoBox-money">
          <view class="infoBox-pay" style="margin-left: 52rpx">待付金额:</view>
          <view class="infoBox-pay" style="color: #ff2d2d">¥{{info.totalFee}}</view>
        </view>
      </block>
      <view class="box_convention_button">
        <button
          wx:if="{{activeLeaveState!=0}}"
          class="convention_button {{activeLeaveState==1&&'btn-disabled'}}"
          disabled="{{activeLeaveState==1}}"
          bind:tap="onLeave"
        >
          离场
        </button>
        <button wx:else class="convention_button" bind:tap="pay">支付</button>
      </view>
    </scroll-view>
  </view>
  <view wx:else>
    <image src="../../image/qfjf-null.png" class="nullImg"></image>
    <view class="nullTip">暂无停车订单</view>
  </view>
</view>
