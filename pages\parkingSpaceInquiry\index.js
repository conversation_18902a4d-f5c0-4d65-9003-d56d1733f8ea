var util = require('../../utils/util')
var api = require('../../config/api.js')
const app = getApp()
Page({
  data: {
    changePosition: false, //是否修改定位按钮的位置，当手机屏幕太小是修改到左边
    parkingLotFilterValue: '', //停车场筛选调条件,默认不选中
    parkingLotFilters: [], //停车场筛选调条件
    allowClick: false,
    scale: app.globalData.scale, //地图层级
    isGet: 0, //地图视野发生改变的时候有个开始和结束，业务上只希望结束的时候向后台获取数据
    isV: false, //判断跳转的页面是否是只有预约的车位
    imgUrl: api.imgUrl,
    swidth: 0,
    sheight: 0,
    markers: [],
    mapCtx: null,
    latitude: app.globalData.latitude, //地图的中心点
    longitude: app.globalData.longitude,
    currentPage: 1,
    pageSize: 20, //分页大小
    is_selected: '',
    filter: 'ALL', //RESERVE,DISCOUNT,CHARGE,TOILET,ALL,RESERVE
    isShowPxSelect: false,
    orderByText: '距离最近',
    orderBy: 'DISTANCE', //DISTANCE表示距离最近，FREE表示免费最久，SURPLUS表示余位最多),可用值:DISTANCE,FREE,SURPLUS
    pslist: [],
    tcdNum: 0,
    listDivHeight: '501rpx', //calc(100% - 185rpx)  600rpx
    btntext: '点击查找更多', //点击查找更多 收回
    toolSelectTop: '30%', //-100%
    keyword: '查找停车位/公厕/加油站/充电桩/景点',
    isSearch: false, //是否是搜索页面返回的
    showNullMoreTip: false,
    showNullTip: false,
    jnTop: app.globalData.jnTop,
    showMakers: [],
    btnIcon: {
      s: {
        PARKING_LOT: '../../image/button_tingchechangs.png',
        FILLING_STATION: '../../image/button_jiayouzhans.png',
        CHARGE: '../../image/button_chongdianzhuangs.png',
        TOILET: '../../image/button_wcs.png',
        SCENIC_AREA: '../../image/button_jingdians.png',
      },
      n: {
        PARKING_LOT: '../../image/button_tingchechang.png',
        FILLING_STATION: '../../image/button_jiayouzhan.png',
        CHARGE: '../../image/button_chongdianzhuang.png',
        TOILET: '../../image/button_wc.png',
        SCENIC_AREA: '../../image/button_jingdian.png',
      },
    },
  },
  onLoad: function (options) {
    //只会调用一次
    var swidth = wx.getSystemInfoSync().windowWidth
    var sheight = wx.getSystemInfoSync().windowHeight - util.rpxToPx(500)
    var that = this
    var keyword = options.keyword
    var isV = JSON.parse(options.isV)
    if (isV) {
      that.setData({
        filter: 'RESERVE', //限制为查询可预约的车场
      })
    }
    if (keyword != undefined) {
      that.setData({
        isV: isV,
        isSearch: true,
        keyword: keyword,
        latitude: options.latitude,
        longitude: options.longitude,
        swidth: swidth,
        sheight: sheight,
        isGet: 0,
      })
    } else {
      keyword = '查找停车位/公厕/加油站/充电桩/景点'
      if (isV) {
        keyword = '查找可预约停车位'
      }
      that.setData({
        isV: isV,
        isSearch: false,
        keyword: keyword,
        swidth: swidth,
        sheight: sheight,
        isGet: 0,
      })
    }
    that.getTCCFilters() //获取停车场删选条件
  },
  onShow: function (e) {
    this.setData({
      isGet: 0,
    })
    this.initData()
  },
  initData: function () {
    let that = this
    wx.getSetting({
      success: res => {
        if (res.authSetting['scope.userLocation'] === false) {
          // 提示用户授权
          wx.showModal({
            title: '提示',
            content: '系统需获取您当前位置，便于向您推送周边车场信息。',
            confirmText: '去授权',
            success: res => {
              if (res.confirm) {
                wx.openSetting({
                  // 打开设置页面
                  success: res => {
                    if (res.authSetting['scope.userLocation'] === true) {
                      that.getUserInfo()
                    }
                  },
                })
              } else {
                //点击取消
                that.getInfo(app.globalData.latitude, app.globalData.longitude) //使用默认经纬度进行查询
              }
            },
          })
        } else {
          // 已授权，调用wx.getLocation
          that.getUserInfo()
        }
      },
      fail: res => {
        wx.showToast({
          title: res,
        })
      },
    })
  },
  getUserInfo: function () {
    let that = this
    var data = that.data
    var latitude = null
    var longitude = null
    if (data.isSearch) {
      //有搜索关键字
      latitude = data.latitude
      longitude = data.longitude
      that.getInfo(latitude, longitude)
    } else {
      //没有搜索关键字时用当前的位置
      wx.getLocation({
        //获取当前位置
        type: 'gcj02',
        success(res) {
          latitude = res.latitude
          longitude = res.longitude
          that.setData({
            latitude: latitude,
            longitude: longitude,
          })
          that.getInfo(latitude, longitude)
        },
        fail(res) {
          util.showToast(res)
        },
      })
    }
  },
  getInfo: function (latitude, longitude) {
    util.showLoading('正在加载…')
    this.setData({
      isGet: 0,
    })
    this.getNeedShowMarkersInMap()
    this.getMapMakerList(latitude, longitude)
  },
  isMapLoaded: function () {},
  getMapMakers: function (latitude, longitude, neLatitude, neLongitude, swLatitude, swLongitude) {
    util.showLoading('正在加载…')
    let that = this
    var data = that.data
    util
      .request(
        api.getAreaResource,
        {
          neLatitude: neLatitude, //地图可视化区域东北纬度
          neLongitude: neLongitude, ////地图可视化区域东北纬度
          swLatitude: swLatitude, //地图可视化区域西南纬度
          swLongitude: swLongitude, //地图可视化区域西南纬度
          latitude: latitude,
          longitude: longitude,
          orderBy: data.orderBy,
          filter: data.filter,
          parkingLotFilter: data.parkingLotFilterValue, //停车场过滤条件 后台做了只有选择停车场之后才拿这个字段
        },
        'GET'
      )
      .then(function (res) {
        util.showLoading('正在加载…')
        var markers = res.result
        if (res.code == '0') {
          markers = markers.concat({
            id: 0,
            latitude: latitude,
            longitude: longitude,
            iconPath: data.imgUrl + 'iMarker.png?ra=' + Math.random(),
            width: 25,
            height: 30,
          }) //追加新数据
          that.setData({
            markers: markers, //存储所有地图节点
          })
          wx.hideLoading()
        } else {
          util.showToast(res.message)
        }
      })
  },
  getMapMakerList: function (latitude, longitude) {
    let that = this
    var data = that.data
    var currentPage = data.currentPage
    var pageSize = data.pageSize
    console.log('当前页码：' + currentPage)
    util
      .request(
        api.getMapMakerList,
        {
          latitude: latitude,
          longitude: longitude,
          currentPage: currentPage,
          filter: data.filter,
          orderBy: data.orderBy, //DISTANCE表示距离最短，FREE表示免费最久，SURPLUS表示余位最多),可用值:DISTANCE,FREE,SURPLUS
          pageSize: pageSize,
          parkingLotFilter: data.parkingLotFilterValue,
        },
        'GET'
      )
      .then(function (res) {
        var result = res.result
        var pslist = result.records
        if (res.code == '0') {
          var total = result.total
          if (total == 0) {
            if (currentPage == 1) {
              //没有数据
              that.setData({
                pslist: [],
                showNullTip: true,
                showNullMoreTip: false,
                tcdNum: 0,
              })
            } else {
              //没有更多数据
              that.setData({
                showNullMoreTip: true,
                showNullTip: false,
              })
            }
            wx.hideLoading()
          } else {
            const sortPslist = util.sortParkingLotsByHoliday(pslist)
            that.setData({
              pslist: that.data.pslist.concat(sortPslist),
              showNullTip: false,
              tcdNum: total,
            })
            if (total <= pageSize || pslist.length == 0) {
              //没有更多数据
              that.setData({
                showNullMoreTip: true,
              })
            } else {
              that.setData({
                showNullMoreTip: false,
              })
            }
            wx.hideLoading()
          }
          //加载下一页
          that.setData({
            currentPage: currentPage + 1,
          })
        } else {
          wx.hideLoading()
          util.showToast(res.message)
        }
      })
  },
  bindDownRefresh: function () {
    console.log('下拉刷新...')
    util.showLoading('正在加载…')
    var data = this.data
    this.setData({
      currentPage: 1,
      pslist: [],
    })
    this.getMapMakerList(data.latitude, data.longitude)
    wx.stopPullDownRefresh()
  },
  bindReachBottom: function () {
    console.log('上拉加载....')
    util.showLoading('正在加载…')
    var that = this
    var data = that.data
    if (data.showNullMoreTip) {
      util.showToast('没有更多数据了')
    } else {
      that.getMapMakerList(data.latitude, data.longitude)
    }
  },
  modifyLocation: function (e) {
    var _this = this
    console.log('modifyLocation' + _this.data.allowClick)
    if (_this.data.allowClick) {
      this.setData({
        allowClick: false,
      })
      return
    }
    // 根据标记点的 id 来显示详情
    var detail = e.detail
    var latitude = detail.latitude
    var longitude = detail.longitude
    var markerIndex = _this.data.markers.findIndex(t => t.id == 0)
    var markersLat = `markers[${markerIndex}].latitude`
    var markersLnt = `markers[${markerIndex}].longitude`
    _this.setData({
      [markersLat]: latitude,
      [markersLnt]: longitude,
      latitude: latitude,
      longitude: longitude,
    }) //修改位置，居中
    _this.setData({
      pslist: [],
      currentPage: 1,
    })
    util.showLoading('正在加载...')
    _this.getInfo(latitude, longitude)
  },
  markertap: function (e) {
    //资源点击事件
    console.log('markertap' + this.data.allowClick)
    this.setData({
      allowClick: true,
    })
    // 根据标记点的 id 来显示详情
    var _this = this
    var markerId = e.markerId
    if (markerId != null) {
      if (markerId == 0) {
        return
      }
      var resourceType = ''
      //循环遍历数据
      var lat, lng
      var data = _this.data
      let markers = data.markers //获取到需要遍历的集合数据
      var marker = [] //修改为只展示选中的那个图标
      markers.forEach(function (item, index) {
        if (item.id == markerId) {
          resourceType = item.type
          lat = item.latitude
          lng = item.longitude
          item['width'] = 40
          item['height'] = 50
          marker.push(item)
        }
      })
      wx.setStorage({
        key: 'markers',
        data: marker,
      })
      wx.navigateTo({
        url:
          '/pages/parkingSpaceInquiry/mapInfo/index?id=' +
          markerId +
          '&lat=' +
          lat +
          '&long=' +
          lng +
          '&resourceType=' +
          resourceType +
          '&destinationlat=' +
          data.latitude +
          '&destinationlng=' +
          data.longitude,
      })
    }
  },
  selectItem: function (e) {
    util.showLoading('正在加载…')
    let that = this
    var id = e.target.id + ''
    var data = that.data
    var is_selected = data.is_selected
    if (id == is_selected) {
      that.setData({
        parkingLotFilterValue: '',
        filter: 'ALL',
        is_selected: '',
        isShowPxSelect: false,
        pslist: [],
        currentPage: 1,
      })
      if (is_selected == 'PARKING_LOT') {
        that.changeBtnPosition()
      }
    } else {
      that.setData({
        parkingLotFilterValue: '',
        filter: id,
        is_selected: id,
        isShowPxSelect: false,
        pslist: [],
        currentPage: 1,
      })
      that.changeBtnPosition()
    }
    that.getInfo(data.latitude, data.longitude)
  },
  changeBtnPosition() {
    //当用户选择停车场时需要判断是否需要修改 “定位到当前位置按钮” 的位置
    var that = this
    wx.createSelectorQuery()
      .select('#leftToolBox')
      .boundingClientRect(function (rect) {
        var leftToolBoxBottom = rect.bottom
        console.log('leftToolBoxBottom:' + leftToolBoxBottom)
        wx.createSelectorQuery()
          .select('#centerBox')
          .boundingClientRect(function (rects) {
            var listBoxTop = rects.top // 节点的下边界坐标
            if (listBoxTop < leftToolBoxBottom) {
              that.setData({
                changePosition: true,
              })
            } else {
              that.setData({
                changePosition: false,
              })
            }
            console.log('centerBoxTop:' + listBoxTop)
            console.log('aaa:' + (listBoxTop - leftToolBoxBottom))
          })
          .exec()
      })
      .exec()
  },
  goCustomerServiceHelp: function () {
    wx.navigateTo({
      url: '/pages/customerServiceHelp/index',
    })
  },
  openPXTool: function () {
    let that = this
    let isShowPxSelect = that.data.isShowPxSelect
    if (isShowPxSelect) {
      that.setData({
        isShowPxSelect: false,
      })
    } else {
      that.setData({
        isShowPxSelect: true,
      })
    }
  },
  selectPx: function (e) {
    util.showLoading('正在加载…')
    var data = this.data
    var currentTarget = e.currentTarget
    var id = currentTarget.id
    var orderByText = currentTarget.dataset.text
    this.setData({
      orderByText: orderByText,
      orderBy: id,
      isShowPxSelect: false,
      pslist: [],
      currentPage: 1,
    })
    this.getMapMakerList(data.latitude, data.longitude)
  },
  openMapApp: function (e) {
    var dataset = e.currentTarget.dataset
    var latitude = dataset.lat
    var longitude = dataset.long
    var name = dataset.name
    var address = dataset.address
    util.openMapApp(latitude, longitude, app.globalData.mapScale, name, address)
  },
  goSearchPage: function () {
    var data = this.data
    this.setData({
      isShowPxSelect: false,
    })
    var url = './searchPage/index?lat=' + data.latitude + '&lng=' + data.longitude + '&goback=index&isV=' + data.isV
    if (data.isV) {
      url = url + '&type=reservation'
    }
    wx.navigateTo({
      url: url,
    })
  },
  addListHeight: function (e) {
    let that = this
    var listDivHeight = that.data.listDivHeight
    if (listDivHeight == '501rpx') {
      //固定列表的长度
      that.setData({
        btntext: '收起',
        listDivHeight: '1020rpx',
        isShowPxSelect: false,
      })
    } else {
      that.setData({
        btntext: '点击查找更多',
        listDivHeight: '501rpx',
        isShowPxSelect: false,
      })
    }
  },
  goxq: function (e) {
    var id = e.currentTarget.id
    var type = e.currentTarget.dataset.type
    if (type === 'PARKING_LOT' || type === 'PARKING_SPACE' || type === 'CHARGE') {
      //停车场和露天泊位和充电桩可以跳转到详情
      wx.navigateTo({
        url:
          '/pages/parkingSpaceInquiry/info/index?id=' +
          id +
          '&lat=' +
          this.data.latitude +
          '&long=' +
          this.data.longitude +
          '&resourceType=' +
          type,
      })
    }
  },
  goBack: function (e) {
    wx.navigateBack({
      delta: 1,
    })
  },
  setCenter: function (e) {
    let mpCtx = wx.createMapContext('myMap')
    mpCtx.moveToLocation()
  },
  getNeedShowMarkersInMap: function (e) {
    let mpCtx = wx.createMapContext('myMap')
    var that = this
    if (that.data.isGet == 1) {
      that.setData({
        isGet: 0,
      })
    } else {
      that.setData({
        isGet: 1,
      })
      mpCtx.getRegion({
        success: function (res) {
          var southwest = res.southwest //西南角经纬度
          var swLongitude = southwest.longitude
          var swLatitude = southwest.latitude
          var northeast = res.northeast //东北角经纬度
          var neLongitude = northeast.longitude
          var neLatitude = northeast.latitude
          that.getMapMakers(that.data.latitude, that.data.longitude, neLatitude, neLongitude, swLatitude, swLongitude)
        },
      })
    }
  },
  selectFilterItem: function (e) {
    util.showLoading('正在加载…')
    let that = this
    var id = e.target.id + ''
    var data = that.data
    if (id == data.parkingLotFilterValue) {
      that.setData({
        filter: '',
        isShowPxSelect: false,
        pslist: [],
        currentPage: 1,
      })
    } else {
      that.setData({
        parkingLotFilterValue: id,
        isShowPxSelect: false,
        pslist: [],
        currentPage: 1,
      })
    }
    that.getInfo(data.latitude, data.longitude)
  },
  getTCCFilters() {
    let that = this
    util.request(api.getFilters, {}, 'GET').then(function (res) {
      var list = res.result
      if (res.code == '0') {
        that.setData({
          parkingLotFilters: list,
        })
      }
    })
  },
})
