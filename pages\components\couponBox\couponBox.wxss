@import '/pages/common/common.wxss';
.rowView {
  flex-direction: row;
  display: flex;
}

.columnView {
  flex-direction: column;
  display: flex;
}
.box {
  width: calc(100% - 56rpx);
  min-height: 200rpx;
  background: #FFFFFF;
  box-shadow: 0rpx 10rpx 0rpx 1rpx rgba(228, 234, 248, 0.16);
  border-radius: 20rpx 20rpx 20rpx 20rpx;
  opacity: 1;
  margin: 30rpx 28rpx;
  padding-bottom: 10rpx;
}

.box-t {
  width: calc(100% - 64rpx);
  margin: 26rpx 32rpx 0 32rpx;
  border-bottom: 1rpx solid #DFDFDF;
  min-height: 10rpx;
}

.box-t-l {
  width: calc(100% - 64rpx);
  margin: 26rpx 32rpx 0 32rpx;
  border-bottom: 1rpx solid #DFDFDF;
}

.box-t-l-t {
  min-width: 170rpx;
  height: 60rpx;
  line-height: 60rpx;
  font-size: 34rpx;
  font-weight: bold;
  color: #353535;
}

.box-t-l-b {
  min-width: 248rpx;
  height: 30rpx;
  line-height: 30rpx;
  font-size: 22rpx;
  font-weight: 400;
  color: #7E7E7E;
  margin-bottom: 10rpx;
}

.box-t-r {
  min-width: 120rpx;
  min-height: 48rpx;
  font-size: 34rpx;
  font-weight: bold;
  color: #E94F4F;
  text-align: center;
  margin: auto;
}

.box-b-l {
  width: 580rpx;
  font-size: 28rpx;
  font-weight: 400;
  color: #353535;
  line-height: 30rpx;
  margin-left: 32rpx;
  margin-top: 20rpx;
}
.box-b-l.show {
  min-height: 30rpx;
}
.box-b-l.close {
  height: 30rpx;
  display: -webkit-box;
  text-overflow: ellipsis;
  overflow: hidden;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
}
.bottomBox {
  width: 750rpx;
  height: 140rpx;
  background: #FFFFFF;
  border-radius: 0rpx 0rpx 0rpx 0rpx;
  opacity: 1;
  font-size: 26rpx;
  font-weight: bold;
  color: #353535;
  text-align: center;
  line-height: 70rpx;
}

.grey .box-t-l-t,
.grey .box-t-l-b,
.grey .box-t-r,
.grey .box-b-l {
  color: #C0C0C0 !important;
}

.grey {
  background-size: 145rpx 160rpx;
  background-repeat: no-repeat;
  background-position: right bottom;
}



.udbtn {
  width: 26rpx;
  height: 16rpx;
  margin: 20rpx 32rpx;
}

.up {
  background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABoAAAAQCAYAAAAI0W+oAAAAAXNSR0IArs4c6QAAAixJREFUOE+VlLtrFFEUxr9vRhesLCR/gWxll90hs7fXQvAB6gaCb1wTlPgAiSiiiaKgQaJoQggSIwEfYGERi1jYzb27O5MqYKUWIaXWYszOkQlGdiazO7u3PPd853de9xJdHmOC66Tscl1npBspu3E2xr8J8H6kEeETpQpXO9V3DNI6uE1irDmwiEwq5QxH3CxgRyBjgnsAbqUFE5GZUqk4RLItLBNUrQYPRHCjfcbycnHx47nR0dGwlV9bkOf5Dy2LsaGLyDrJbcmAIpxfXf12plwuN9JgLUHVajAugmuJmawDVhlo7CatRwASennrusXjJLfAUkFa+xMkryQya4jICaWcN5Fd6+AyiYkkTETeLy9zYHCw+KdZvwVkTPAUwKWYExmGYVhRyplttnte/YJt289ExIr748PKyo7+cnnP2qY9BtLaf07yYqISITnsuoXJtN4bs1QBZBpADCaChZ6enUfz+fzvSLcJotb+FMmhlCGPKFUYb7d1xtRPA9YLAHaisk9huHZYKfWLIsJaLZgW4flkMBJ3Xbd4J+sxRveeV4+WYI5kDCaCz7kcDtCYYAZAJSXY41KpGNu6LKAxS/2AzAPYnvBdiFp2EsBsIpOpUqmYnFUWZ+Pe8/wjts3XIsj9E/wIw3D/xoy0rg2Q9isA0UOcc93C2awvpR3V8/xDlsV3AH6S1l7X7f3yf+u0Do5ZFg/29fWeItnyK+moLADVarDPtuWr4zjfI81fOFTPFRxHTMQAAAAASUVORK5CYII=");
  background-size: 100%;
  background-repeat: no-repeat;
}

.down {
  background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABoAAAAQCAYAAAAI0W+oAAAAAXNSR0IArs4c6QAAAjdJREFUOE+dlM1rE1EUxc+ZtjaiXblNFxZXbrrITOmd7ATpogs3IiqiFhW/KopQkaISqFDqB2pRS2rBUlFhKIpFkOoiq8yEZiq4cOs/4N5FmsyVSRNJxmna5C1mmPfOPb95950ZojaKxeJAqcQD6bT5tT7X6d1xnK5kcmBJVT+m09Zy6MPwUih8P6gafAOwD9BjItZKpxBVpeetvyZxBkBZVU/btvWe+fyaZRjGl00IQKKkyhMiqQ+dwDzPfwngcr1WVSuGgTG6rv+ZxGjEdAPgKZGU0w7M84qPAd6M1pCc5+rqjz19fRsrJA5FBGWAZ0VSb3cC87y1KcC4E9Wq6ryIeal6Rq7r7iZ7PgE8HBFWgiA4n04PLbaCeV7xFsCZGM2ciHkVgFZB4cjlcone3r7lmDYGAC6KmAtxsHzev2YYeFYPVsPZvLBta7z+/A8UTjjOz13J5B+H5JFGU5KBqo6LmHON865bPEfyVRQCYFbEvN7kEX3LbNbvGRzEO1UcjawpgBsi5uxmu/2TJJYAdDXqVPWJbVv/ByKuHblcrjuR2PsG4PEojMQEyV+VShDuvLt553g0PGxOxHk2ta5REH7d/f37F8OYxySpHIUAOiNi3d4qNFuCwoJMJmOMjIwuABxrlTpVTNu2OdlK0xIUFoa/lEJhPQvgQrxRcF9k6G4rSLi2LahmQM/znwO40nzwuGfb5tR2kHZAVS/X9Z+SqMZWFZO2bU7vBNI2qAZ7SOpvEevBTiGh7i9zjsC95keagwAAAABJRU5ErkJggg==");
  background-size: 100%;
  background-repeat: no-repeat;
}