<checkbox-group wx:if="{{data.tag === 'todogroup'}}" class="{{data.attrs.class}}" bindchange="_change">
    <block wx:if="{{data.children}}" wx:for="{{data.children}}" wx:for-item="item" wx:key="i">
        <label wx:if="{{item.tag}}" class="{{item.attrs.class}}">
            <block wx:if="{{item.children}}" wx:for="{{item.children}}" wx:for-item="item" wx:key="i">
                <!--解析选择框-->
                <checkbox wx:if="{{item.tag === 'checkbox'}}" class="{{item.attrs.class}}" value="{{item.attrs.value}}" data-_e="{{item}}" checked="{{item.attrs.checked}}" disabled="{{item.attrs.disabled}}"/>
                <!--解析文字-->
                <decode wx:if="{{item.children}}" nodes="{{item}}"/>
            </block>
        </label>
    </block>
</checkbox-group>