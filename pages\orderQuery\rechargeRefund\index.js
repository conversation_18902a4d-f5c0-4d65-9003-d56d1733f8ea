const app = getApp()
var util = require('../../../utils/util');
var api = require('../../../config/api.js');
Page({
  data: {
    pageHeight: 0,
    id: '',
    info: {},
    refund: {},
    refundState: false
  },
  onLoad(options) {
    console.log('setdata', app.globalData.windowHeight)
    console.log('setdata', app.globalData.StatusBar)
    let height = app.globalData.windowHeight - app.globalData.CustomBar + 48; // 48是tabbar的高度
    this.setData({
      pageHeight: height
    })
    const eventChannel = this.getOpenerEventChannel();
    eventChannel.on('setData', (res) => {
      this.setData({
        id: res.data.id
      })
      this.getDetail()
      console.log('setdata', JSON.stringify(res.data))
    })
  },
  refund(){
    let that = this
    util.showLoading('正在加载…')
    util.request(api.rechargeRefund + this.data.id, {}, 'POST').then(function (res) {
      wx.hideLoading();
      let {code, result} = res || {}
      if (code == '0') {
        that.setData({
          refund: result,
          refundState: true
        });
      }else{
        util.showToast(res.message)
      }
    })
    .catch(err=>{
      wx.hideLoading();
      util.showToast('服务异常，请稍后重试')
    })
  },
  getDetail(){
    let that = this
    util.showLoading('正在加载…')
    util.request(api.getRechargeDetail + this.data.id, {}, 'GET').then(function (res) {
      wx.hideLoading();
      let {code, result} = res || {}
      if (code == '0') {
        that.setData({
          info: result
        });
      }else{
        util.showToast(res.message)
      }
    })
    .catch(err=>{
      wx.hideLoading();
      util.showToast('服务异常，请稍后重试')
    })
  },
  backList(){
    if(this.data.refundState){
      wx.navigateBack({
        delta: 1
      });
    }
  }
})