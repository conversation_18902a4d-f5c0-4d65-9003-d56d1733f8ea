page {
  background-color: #F6F7FB;
}
.container{
  padding: 60rpx 0 0 0;
  width: 100%;
}
.state-box{
  padding: 60rpx 60rpx 50rpx 60rpx;
  margin: 20rpx;
  width: calc(100% - 20rpx * 2);
  height: 560rpx;
  background: #FFFFFF;
  box-shadow: 0rpx 10rpx 0rpx 1rpx rgba(228,234,248,0.16);
  border-radius: 20rpx 20rpx 20rpx 20rpx;
  box-sizing: border-box;
}
.state-box .header{
  margin-bottom: 89rpx;
  width: 100%;
  flex-direction: column;
}

.state-box .header .money{
  display: flex;
  justify-content: center;
}
.state-box .header .money .symbol{
  font-size: 24rpx;
  color: #256BF5;
  line-height: 78rpx;
}
.state-box .header .money .num{
height: 78rpx;
font-size: 56rpx;
font-weight: bold;
color: #256BF5;
line-height: 56rpx;
}
.state-box .header .tip{
  margin-bottom: 70rpx;
  width: 100%;
  font-size: 26rpx;
  font-weight: bold;
  color: #353535;
  line-height: 54rpx;
  text-align: center;
}

.state-box .row{
  display: flex;
  margin-bottom: 16rpx;
  width: 100%;
  height: 37rpx;
}

.state-box .row .label,
.state-box .row .value{
  height: 100%;
  font-size: 26rpx;
  font-weight: 400;
  line-height: 37rpx;
}

.state-box .row .label{
  width: 210rpx;
  color: #353535;
  text-align: left;
}
.state-box .row .value{
  flex: 1;
  color: #7E7E7E;
  text-align: right;
}