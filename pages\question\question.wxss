.page {
  box-sizing: border-box;
  background: #fe8029;
  overflow: hidden;
}
.header-image {
  width: 100%;
}
.title {
  font-weight: bold;
  text-align: center;
  margin-top: 32rpx;
  margin-left: 32rpx;
  margin-right: 32rpx;
  margin-bottom: 48rpx;
  font-size: 36rpx;
  word-break: break-all;
}
.desc {
  text-indent: 2em;
  word-break: break-all;
  font-weight: bold;
  color: #000001;
  font-size: 28rpx;
  line-height: 42rpx;
}

.list {
  padding-left: 32rpx;
  padding-right: 32rpx;
  padding-top: 38rpx;
  background: #fff;
  border-radius: 20rpx;
  margin: 0 30rpx;
  padding-bottom: 32rpx;
  margin-bottom: calc(20rpx + constant(safe-area-inset-bottom));
  margin-bottom: calc(20rpx + env(safe-area-inset-bottom));
}
.list-item {
  margin-bottom: 48rpx;
}
.list-header {
  margin-bottom: 24rpx;
}
.list-title {
  font-weight: bold;
  word-break: break-all;
}
.required::before {
  content: '*';
  color: #ff5745;
  width: 24rpx;
  display: inline-block;
}
.list-required-tip {
  color: #ff5745;
  font-size: 24rpx;
  border-radius: 8rpx;
  padding: 0 8rpx;
  display: inline-block;
  margin-left: 12px;
}

.input {
  border: 1px solid #ddd;
  padding: 16rpx;
}

.textarea {
  border: 1px solid #ddd;
  padding: 16rpx;
  width: 100%;
  box-sizing: border-box;
}

.list-flex {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}
