<cu-custom bgColor="white-bg" contentTitle="车位预订" isBack="true"></cu-custom>
<view wx:if="{{showSuccese}}" class="sucBox columnView">
  <view class="successStatus-icon" >
    <image src="../../../../image/suc.png" class="successStatus-icon-img"></image>
  </view>
  <view class="sucBox-text" >提交成功</view>
  <view class="sucBox-text" >预约成功后请半小时之内进入停车场</view>
  <view class="box_conventionTwo_button">
    <view class="conventionTwo_button-left" bindtap="goPage" data-type="index">返回首页</view>
    <view class="conventionTwo_button-right" bindtap="goPage" data-type="vehicleReservation">查看预约订单</view>
  </view>
</view>
<view wx:else>
  <view class="backWihte columnView" style="margin-top: 60rpx;padding: 15rpx 0 ;">
    <view class="backWihte-line rowView">
      <view class="backWihte-line-title">停车场:</view>
      <view class="backWihte-line-value">{{parkName}}</view>
    </view>
    <view class="backWihte-line rowView">
      <view class="backWihte-line-title">车牌号码:</view>
      <view class="backWihte-line-value">{{plateNo}}</view>
    </view>
    <view class="backWihte-line rowView">
      <view class="backWihte-line-title">入场时间:</view>
      <view class="backWihte-line-value">{{entryTimeDesc}}</view>
    </view>
  </view>
  <view class="backWihte columnView">
    <view class="backWihte-line rowView">
      <view class="backWihte-line-title">预约定金:</view>
      <view class="backWihte-line-value" style="text-align: right;">¥{{deposit}}</view>
    </view>
  </view>
  <view class="backWihte columnView">
    <view class="info-bt">
      <view class="icon-sjx-left-tilte"></view>
      <view class="info-bt-text">支付方式</view>
    </view>
    <view class="paymentListBox">
      <block wx:for="{{payList}}" wx:key="index">
        <view id="{{item.payType}}" class="infoBox  {{selectPay == item.payType ? 'choosePay':''}}" bindtap="selectPayMode" wx:if="{{item.payType=='BALANCE_PAY'}}">
          <view class="infoBox-text" style="margin-top: 14rpx;">余额</view>
          <view class="infoBox-text" id="ye" data-money="{{item.iconUrl}}">{{item.iconUrl}}元</view>
        </view>
        <view id="{{item.payType}}" class="infoBox {{selectPay == item.payType ? 'choosePay':''}}" style="display: flex; flex-direction: column;" bindtap="selectPayMode" wx:else>
          <image class="img" src="{{item.iconUrl}}" style="width: 48rpx;height: 48rpx;position: static;margin: 14rpx auto 0;"></image>
          <view class="infoBox-text">微信支付</view>
        </view>
      </block>
    </view>
  </view>
  <view class="infoBox-money">
    <view class="infoBox-pay" style="margin-left: 52rpx;">应付金额:</view>
    <view class="infoBox-pay" style="color: #FF2D2D;">¥{{deposit}}</view>
  </view>
  <button class="convention_button" bindtap="pay">支付</button>
</view>