/* pages/set/set.wxss */
@import "../../common/index.wxss";

page {
  background: #F5F7F9;
  height: 100vh;
}

.box{
  padding: 0 20rpx;
  margin: 20rpx;
  width: calc(100% - 20rpx * 2);
  background: #FFFFFF;
  border-radius: 20rpx 20rpx 20rpx 20rpx;
  box-sizing: border-box;
}
.box .row{
  display: flex;
  padding: 0 9rpx 0 30rpx;
  width: 100%;
  height: 89rpx;
  border-bottom: 1rpx solid #E5E5E5;
  line-height: 89rpx;
  font-size: 34rpx;
  color: #353535;
  box-sizing: border-box;
}
.box .row:last-child{
  border-width: 0;
}
.box .row.next{
  position: relative;
}
.box .row.next::after{
  content: '';
  position: absolute;
  top: 39rpx;
  right: 9rpx;
  height: 15rpx;
  width: 15rpx;
  border-width: 4rpx 4rpx 0 0;
  border-color: #C7C7CC;
  border-style: solid;
  transform: matrix(0.71, 0.71, -0.71, 0.71, 0, 0);
}

.box .row .secord{
  margin-left: 29rpx;
  font-size: 26rpx;
  color: #7E7E7E;
  line-height: 89rpx;
}
.box .row.red{
  width: 100%;
  color: #DA5937;
  display: flex;
  justify-content: center;
}