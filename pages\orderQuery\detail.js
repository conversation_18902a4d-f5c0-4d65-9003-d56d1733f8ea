var util = require('../../utils/util')
var api = require('../../config/api.js')
var app = getApp()
var commonApi = require('../../utils/commonApi')
Page({
  data: {
    info: {},
    type: '',
    typeNames: {
      'ARREARS': '停车订单',
      'RESERVATION_SPACE': '预约订单',
      'RECHARGE': '充值记录',
      'MONTHLY_PACKAGE': '包月订单详情',
    },
    stateNames: {
      1: '进行中',
      2: '待支付',
      3: '已完成',
    },
    titleName: '',
    oderState: 0, //停车订单类型

    activeLeaveState: 0, //离场按钮状态，0隐藏，1禁用，2可用
  },
  onLoad() {
    const eventChannel = this.getOpenerEventChannel()
    eventChannel.on('setData', res => {
      this.setData({
        info: res.data,
        type: res.type,
        oderState: res.state,
        titleName: this.data.typeNames[res.type],
      })
      if (res.type == 'ARREARS') {
        this.getParkingDetail()
      }
      // if (res.state === 1) {
      //   this.fetchParkState()
      // }
    })
  },
  toRefund(e) {
    wx.navigateTo({
      url: '/pages/orderQuery/rechargeRefund/index',
      success: res => {
        res.eventChannel.emit('setData', {
          data: e.detail,
        })
      },
    })
  },
  getParkingDetail() {
    let that = this
    util.showLoading('正在加载…')
    util
      .request(api.getArrearsDetail + this.data.info.id, {}, 'GET')
      .then(function (res) {
        wx.hideLoading()
        let { code, result } = res || {}
        if (code == '0') {
          that.setData({
            info: result,
          })
        } else {
          util.showToast(res.message)
        }
      })
      .catch(err => {
        wx.hideLoading()
        util.showToast('服务异常，请稍后重试')
      })
  },

  async fetchParkState() {
    const data = await commonApi.getParkState(this.data.info.plateNo)
    this.parkCode = data.parkCode
    this.uniqueId = data.uniqueId
    this.setData({ activeLeaveState: data.activeLeaveState })
  },
  async onLeave() {
    try {
      const result = await commonApi.carLeave(this.parkCode, this.uniqueId)
      if (result) {
        this.fetchParkState()
      }
    } catch (error) {}
  },
  onPay() {
    const uniqueId = this.uniqueId || this.data.info.uniqueId
    wx.navigateTo({
      url: '/pages/supplementaryPayment/details/index?uniqueId=' + uniqueId,
    })
  },
})
