<cu-custom bgColor="white-bg" contentTitle="{{title[type]}}" isBack="true"></cu-custom>
<image
  src="{{imgUrl}}/sy/img_bg.png"
  mode="scaleToFill"
  class="navigationPng"
  style="width:100%;z-index: 1;	height:{{imageHeight}}px;"
></image>
<view class="columnView">
  <view class="box columnView">
    <view class="box-top-imgBox">
      <image src="../../../image/logo.png" style="width: 127rpx; height: 127rpx"></image>
    </view>
    <view class="box-top-text">畅行桂林</view>
    <view class="rowView box-center"> 登录提示 </view>
    <view class="rowView box-bottom"> {{tip[type]}} </view>
  </view>
</view>
<view class="modalDlg-xyView">
  <image class="yes_icon" wx:if="{{!allowXY}}" src="../../../image/yes-icon.png" bindtap="allow"></image>
  <image class="yes_icon" wx:if="{{allowXY}}" src="../../../image/yes-icon-select.png" bindtap="allow"></image>
  <text class="modalDlg-xyView-text">阅读并同意</text>
  <text class="modalDlg-xyView-text xy" id="serviceUrl" bindtap="viewAgreement">《服务协议》、</text>
  <text class="modalDlg-xyView-text xy" id="privacyUrl" bindtap="viewAgreement">《软件注册及隐私政策协议》、</text>
  <text class="modalDlg-xyView-text xy" id="logonUrl" bindtap="viewAgreement">《登录政策》</text>
</view>
<view wx:if="{{!allowXY}}" class="convention_button bcolor viewB" bindtap="sqbd">快捷登录</view>
<button
  wx:if="{{allowXY}}"
  class="convention_button bcolor"
  open-type="getPhoneNumber"
  bindgetphonenumber="getPhoneNumber"
>
  快捷登录
</button>
<button class="convention_button gcolor" bindtap="login">手机号验证登录</button>
<view class="auth-container" bind:tap="onIdentityLogin">
  <image src="/image/auth-logo.png" class="btn-auth" />
  <view>国家网络身份认证</view>
</view>
<view class="zbdl" bindtap="goBack">暂不登录</view>
<view class="bTip">未注册的手机号验证后将自动为您创建账户</view>
<loginDialogByPhone isShow="{{isShowLongin}}" authPid="{{authPid}}" bind:refresh="refresh"></loginDialogByPhone>
<loginDialogByAuth
  isShow="{{isShowAuth}}"
  authPid="{{authPid}}"
  bind:refresh="refresh"
  bind:verificationCode="onVerificationCode"
></loginDialogByAuth>
