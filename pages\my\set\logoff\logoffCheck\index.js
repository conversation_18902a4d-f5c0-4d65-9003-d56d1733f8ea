var util = require('../../../../../utils/util');
var api = require('../../../../../config/api');
Page({
  data: {
    status:false
  },
  onLoad(options) {
  },
  onShow(){
    this.logoffable();
  },
  logoffable(){
    var that = this;
    util.showLoading('正在加载...')
    util.request(api.logoffable, "", 'GET').then(function (res) {
      if (res.code == '0') {
        wx.hideLoading();
        that.setData({
          status :res.result.logoffAble//是否可注销，true表示可注销，false表示不可注销
        })
      } else {
        wx.hideLoading();
        util.showToast(res.message);
      }
    })
  },
  next(){
    wx.navigateTo({
      url: '/pages/my/set/logoff/logoffConfirm/index',
    })
  },
  viewAgreement: function (e) {
    var id = e.currentTarget.id;
    util.viewAgreement(id);
  }
})