<cu-custom bgColor="white-bg" contentTitle="预约订单退款" isBack="true"></cu-custom>
<view class="details-box" wx:if="{{!showSucPage}}">
  <view class="details-box-title " style="padding-top: 40rpx;">
    <view class="details-box-title ">
      <view class="details-box-title-fh ">￥</view>
      <view class="details-box-title-value ">{{info.refundableAmount}}</view>
    </view>
  </view>
  <view class="details-box-title-two">可退定金</view>
  <view class="title-value-box">
    <view class="title-value-box-line">
      <view class="title-value-box-line-title">预约定金:</view>
      <view class="title-value-box-line-value">￥{{info.depositAmount}}</view>
    </view>
    <view class="title-value-box-line">
      <view class="title-value-box-line-title">可退定金:</view>
      <view class="title-value-box-line-value"> ￥{{info.refundableAmount}}</view>
    </view>
    <view class="title-value-box-line">
      <view class="title-value-box-line-title">预约车场名称: </view>
      <view class="title-value-box-line-value">{{info.parkName}}</view>
    </view>
    <view class="title-value-box-line">
      <view class="title-value-box-line-title">预约车牌号码:</view>
      <view class="title-value-box-line-value">{{info.plateNo}}</view>
    </view>
    <view class="title-value-box-line">
      <view class="title-value-box-line-title">预约入场时间:</view>
      <view class="title-value-box-line-value">{{info.reservationTime}}</view>
    </view>
    <view class="refundRuleText">{{info.bookRefundRuleText}}</view>
  </view>
</view>
<button class="convention_button convention_bottom_btn" bindtap="srue" wx:if="{{!showSucPage}}">确定</button>

<view class="infoBox" wx:if="{{showSucPage}}">
  <view class="infoBox-t rowView">
    <image src="../../../image/yes-yhj.png" style="height: 72rpx; width: 72rpx;"></image>
    <view class="infoBox-t-r columnView">
      <view>取消成功</view>
      <view>已退：¥{{resInfo.refundableAmount}}</view>
    </view>
  </view>
  <!-- <view class="infoBox-b rowView">
    <view class="infoBox-b-l">退款时间:</view>
    <view class="infoBox-b-r">{{resInfo.refundTime}}</view>
  </view> -->
  <view class="infoBox-b rowView">
    <view class="infoBox-b-l">退款金额:</view>
    <view class="infoBox-b-r">{{resInfo.refundAmount}}</view>
  </view>
  <view class="infoBox-b rowView">
    <view class="infoBox-b-l">退款渠道:</view>
    <view class="infoBox-b-r">退回到付款账户</view>
  </view>
</view>
<button class="convention_button" bindtap="goBack"  wx:if="{{showSucPage}}">返回预约订单</button>
