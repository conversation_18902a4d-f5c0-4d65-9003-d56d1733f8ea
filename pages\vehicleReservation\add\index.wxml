<view class="saveOutView">
  <cu-custom bgColor="white-bg" contentTitle="车位预订" isBack="true"></cu-custom>
  <scroll-view scroll-y="true" style="flex: 1;height: 1;">
    <view class="ccxq">
      <view class="cwxqzt">
        <image class="cwxqzt" mode="scaleToFill" src="{{info.parkPicUrl==''?imgurl:info.parkPicUrl}}" lazy-load='true'></image>
      </view>
      <view class="infoDiv" id="{{id}}" data-type="{{info.type}}">
        <view class="search-name flex-wrp">{{info.parkName}}</view>
        <view class="nameinfo">
          <view class="search-name-xq flex-wrp">{{info.parkingAddress}}</view>
          <view class="search-dw flex-wrp">{{info.distance}}km</view>
        </view>
        <view class="search-yw flex-wrp">余位 :{{info.leftParkingSpaceNum}}<text style="color: #7E7E7E;">/{{info.totalParkingSpaceNum}}</text></view>
        <!-- <view class="search-dc-tip">{{info.payRuleDesc}}</view> -->
      </view>
      <view class="carInfo">
        <view class="title-value-box-line rowView" bindtap="showBox">
          <view class="title-value-box-line-title">车牌号码：</view>
          <view class="info-one-carTool-text" >{{plateNo}}</view>
          <view class="info-one-carTool-btn icon-jt-left-bj">切换</view>
          <view class="icon-jt-left"></view>
          <!-- <picker class="title-value-box-line-value" bindchange="bindPickerChange" value="{{index}}" range="{{carList}}" range-key="plateNo" mode="selector">
          <view class="picker">
            {{carList[index]["plateNo"]}}
            <text class="qhBtn">切换</text>
          </view>
        </picker> -->
        </view>
        <view class="title-value-box-line rowView">
          <view class="title-value-box-line-title">预约入场时间:</view>
          <view class="title-value-box-line-value">{{entryTimeDesc}}</view>
        </view>
        <view class="title-value-box-line rowView">
          <view class="title-value-box-line-title">预约定金:</view>
          <view class="title-value-box-line-value">¥{{deposit}}</view>
        </view>
      </view>
      <view class="tipWord">{{inTimeLimitText}};</view>
      <view class="tipWord">{{refundRuleText}}</view>
      <view class="bottomBtn">
        <view class="btnBox-down-right" bindtap="reservation">立即预约</view>
      </view>
    </view>
  </scroll-view>
</view>
<bottomListDialog catchtouchmove="preventTouchMove" bind:selectItem="selectItem" isShow="{{showPlateNoBox}}" list="{{carList}}" rangekey="plateNo" disablekey="bagable" disableText="已包月,需办理请前往续费" value="{{plateNo}}"></bottomListDialog>