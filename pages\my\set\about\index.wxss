@import "../../my.wxss";

.box {
  width: calc("100%-100rpx");
  background: #FFFFFF;
  box-shadow: 0rpx 10rpx 20rpx 0rpx rgba(228, 234, 248, 0.5);
  border-radius: 20rpx;
  margin: 20rpx;
  min-height: 1rpx;
}

.box-top {
  margin-top: 60rpx;
  margin-bottom: 10rpx;
}

.box-top-imgBox {
  width: 83rpx;
  height: 83rpx;
  background: #FFFFFF;
  margin-left: 232rpx;
  margin-right: 21rpx;
}

.box-top-text {
  width: 144rpx;
  height: 50rpx;
  font-size: 36rpx;
  font-weight: bold;
  color: #256BF5;
  line-height: 85rpx;
}

.box-center {
  height: 40rpx;
  font-size: 28rpx;
  font-weight: bold;
  color: #7E7E7E;
  line-height: 40rpx;
  margin-bottom: 40rpx;
  margin: 0 auto;
}
.box-bottom {
  width: calc(100% - 60rpx);
  min-height: 192rpx;
  font-size: 28rpx;
  font-weight: 400;
  color: #353535;
  line-height: 40rpx;
  margin: 30rpx;
   text-indent:50rpx;/*首行缩进*/
}
.navigator {
  padding: 0;
  width: calc(100% - 100rpx);
}