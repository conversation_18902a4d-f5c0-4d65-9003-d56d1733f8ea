<!--pages/parkingSpaceInquiry/index.wxml-->

<view class="mapDiv">
  <map id="myMap" style="width: 100%; height: 100vh;" latitude="{{latitude}}" longitude="{{longitude}}" markers="{{markers}}" scale="{{scale}}"  show-location="true">
    <view bindtap="goBack" class="zhtc-back" style="top:{{jnTop}}rpx;">
      <image src="../../../image/zhtc-back.png" style="width: 35rpx;height: 35rpx;"></image>
    </view>
    <view class="mode-back" bindtap="changeMode" style="top:calc(70rpx + {{jnTop}}rpx) "> 返回</view>
    <view class="view-search-left-dw" style="bottom: 360rpx" bindtap="setCenter">
      <image style="width: 44rpx;height: 44rpx;margin: 15rpx;" src="../../../image/button_dinwei.png"></image>
    </view>
    <cover-view slot="callout">
      <block wx:for="{{markers}}" wx:key="this">
        <cover-view class="customCallout rowView" marker-id="{{item.id}}" wx:if="{{item.type=='PARKING_LOT'||item.type=='PARKING_SPACE'||(item.type=='CHARGE'&&item.dataSource=='HIK')}}">
          <cover-image class="icon" src="{{item.bubbleIcon}}"></cover-image>
          <cover-view class="content" wx:if="{{item.type=='PARKING_LOT'&&item.dataSource!='HIK'}}">{{item.total}}
          </cover-view>
          <cover-view class="content" wx:else>
            {{(item.dataSource=='HIK'&&item.type == 'CHARGE')?"快充":"余位"}} {{item.surplus}}/{{item.total}}
          </cover-view>
        </cover-view>
      </block>
    </cover-view>
    <view class="listDiv-d" bindtap="goxq" id="{{info.id}}" data-type="{{info.type}}" style="margin: 0;">
      <view class="listDiv-d-left">
        <view class="rowView">
          <view class="listDiv-search-name ">{{info.name}}</view>
          <view class="listDiv-search-dc-by" wx:if="{{item.monthly}}">可包月</view>
        </view>
        <view class="listDiv-search-name-xq">{{info.address}}</view>
        <view class="listDiv-search-dc">
          <view class="listDiv-search-dc-num flex-wrp">
            <view class="listDiv-search-yw flex-wrp" wx:if="{{info.dataSource=='HIK'}}">余位 {{info.surplus}}/</view>
              <view class="listDiv-search-dc-zw" wx:if="{{info.dataSource=='HIK'}}">{{info.total}}</view>
            <view class="listDiv-search-dc-zw flex-wrp" wx:else hidden="{{info.type=='FILLING_STATION'||info.type=='SCENIC_AREA'||info.type=='CHARGE'||info.type=='TOILET'}}">停车位{{info.total}}</view>
            <view class="listDiv-search-dc-tip" wx:if="{{info.freeTimeDesc!=''}}">{{info.freeTimeDesc}}</view>
            <view class="listDiv-search-dc-yhj" wx:if="{{info.discount}}">优惠停车</view>
          </view>
        </view>
      </view>
      <view class="listDiv-d-right">
        <view class="listDiv-d-dhBtn" catchtap="openMapApp" data-lat="{{info.latitude}}" data-long="{{info.longitude}}" data-name="{{info.name}}" data-address="{{info.address}}">
          <image src="../../../image/dh.png"></image>
          <text>导航</text>
        </view>
        <view class="listDiv-search-dw">{{info.distance}}km</view>
      </view>
    </view>
  </map>
</view>