/* pages/supplementaryPayment/details/index.wxss */
page {
  background: #F6F7FB;
}

.details-titleTab {
  padding-top: 10rpx;
  flex-direction: row;
  display: flex;
  height: 90rpx;
}

.details-tab {
  width: 50%;
  height: 88rpx;
  background: #FFFFFF;
  border-radius: 0rpx 0rpx 0rpx 0rpx;
  opacity: 1;
  font-size: 36rpx;
  font-weight: 400;
  color: #888888;
  line-height: 88rpx;
  text-align: center;
}

.details-tab.select {
  color: #4768F3;
  border-bottom: 2rpx solid #4768F3;
}

.details-box {
  width: calc(100% - 40rpx);
  min-height: 470rpx;
  background: #FFFFFF;
  box-shadow: 0rpx 10rpx 0rpx 1rpx rgba(228, 234, 248, 0.16);
  border-radius: 20rpx 20rpx 20rpx 20rpx;
  opacity: 1;
  margin: 20rpx;
}

.details-box-title {
  margin: auto;
  flex-direction: row;
  display: flex;
}

.details-box-title-fh {
  width: 14rpx;
  height: 78rpx;
  font-size: 24rpx;
  
  font-weight: 400;
  color: #353535;
  line-height: 90rpx;
  margin-right: 10rpx;
}

.details-box-title-value {
  min-height: 78rpx;
  font-size: 56rpx;
  
  font-weight: bold;
  color: #E94F4F;
  line-height: 78rpx;
}

.details-box-title-two {
  width: 100%;
  height: 40rpx;
  font-size: 26rpx;
  
  font-weight: bold;
  color: #353535;
  line-height: 40rpx;
  text-align: center;
  margin-bottom: 6rpx;
}

.title-value-box {
  width: calc(100% - 120rpx);
  min-height: 180rpx;
  background: #FFFFFF;
  flex-direction: column;
  display: flex;
  padding: 60rpx;
}

.title-value-box-line {
  width: 100%;
  min-height: 40rpx;
  font-size: 26rpx;
  font-weight: 400;
  color: #353535;
  line-height: 40rpx;
  flex-direction: row;
  display: flex;
}

.title-value-box-line-title {
  min-width: 130rpx;
  padding-right: 21rpx;
}

.title-value-box-line {
  margin-bottom: 30rpx;
}

.title-value-box-btn {
  width: 200rpx;
  height: 64rpx;
  border-radius: 52rpx 52rpx 52rpx 52rpx;
  opacity: 1;
  font-size: 30rpx;
  font-weight: 400;
  line-height: 64rpx;
  text-align: center;
  margin-left: 70%;
}

.title-value-box-line-value {
  text-align: right;
  flex: 1;
  color: #7E7E7E;
}

.details-box-tip {
  min-width: 433rpx;
  min-height: 40rpx;
  font-size: 28rpx;
  
  font-weight: 400;
  color: #A3A3A3;
  line-height: 40rpx;
  margin: 20rpx;
}