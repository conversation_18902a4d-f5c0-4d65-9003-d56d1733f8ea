<view class="saveOutView">
  <cu-custom bgColor="white-bg" contentTitle="预约订单" isBack="true"></cu-custom>

  <scroll-view scroll-y="true" scroll-y="true" style="height: 1rpx;flex: 1;" bindscrolltolower="scrolltolower">
    <view wx:if="{{list.records.length==0}}">
      <image src="../../image/qfjf-null.png" class="nullImg"></image>
      <view class="nullTip">您还没有预约订单</view>
    </view>
    <view wx:for="{{list.records}}" wx:key="id" scroll-y="true"  >
      <reservation-record record="{{item}}" bind:cancelReservation="cancel" bind:addReservation="add" bind:reservationInfo="goXq"></reservation-record>
    </view>
  </scroll-view>
</view>