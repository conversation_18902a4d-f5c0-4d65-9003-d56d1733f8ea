var util = require('../../../utils/util');
var api = require('../../../config/api.js');
const app = getApp()
Page({
  data: {
    scale: app.globalData.scale,
    imgUrl: api.imgUrl,
    id: null,
    mapCtx: null,
    resourceType: "",
    destinationlat:null, //戳点选中的目的地的经纬度
    destinationlng:null, 
    latitude: null, //资源点的经纬度
    longitude: null,
    Max_markerIndex: null, //放大图标索引
    isSHM: false, //是否需要把气泡修改成水滴
    markers: [],
    info: {},
    jnTop: app.globalData.jnTop
  },
  onLoad: function (options) {
    var that = this;
    var markers = [];
    wx.getStorage({
      key: "markers",
      success(res) {
        markers = res.data;
        var marker = markers[0];
        if(marker.type=='PARKING_LOT'&&marker.dataSource!='HIK'){//单击社会面停车场时，水滴图标变更为停车数量气泡
          var img = that.data.imgUrl;
          marker["customCallout"]={
            display:'ALWAYS',
            anchorX:0,
            anchorY:0
          }
          marker["bubbleIcon"]=img+"HIK.png"; 
          marker["iconPath"]=img+"tran.png"; //修改水滴为透明图片
        }
        that.setData({
          markers: markers
        })
        var data = that.data;
        that.initData(data.id, data.resourceType);
      }
    })
    this.setData({
      id: options.id,
      latitude: options.lat,
      longitude: options.long,
      destinationlat: options.destinationlat,
      destinationlng: options.destinationlng,
      resourceType: options.resourceType,
    })
  },
  onShow: function (e) {
    let that = this;
    that.mapCtx = wx.createMapContext('myMap') //创建 map 上下文 MapContext 对象

  },
  initData: function (id, resourceType) {
    this.getMapResInfo(id, resourceType);
  },
  getMapResInfo: function (id, resourceType) {
    let that = this;
    var data = that.data;
    util.request(api.getMapResInfo, {
      latitude: data.destinationlat,//戳在地图上的点的经纬度
      longitude: data.destinationlng,
      id: id,
      resourceType: resourceType
    }, 'GET').then(function (res) {
      if (res.code == '0') {
        var infos = res.result
        that.setData({
          info: infos
        })
      } else {
        util.showToast(res.message);
      }
    });
  },
  openMapApp: function (e) {
    var currentTarget = e.currentTarget;
    var latitude = currentTarget.dataset.lat;
    var longitude = currentTarget.dataset.long;
    var name = currentTarget.dataset.name;
    var address = currentTarget.dataset.address;
    wx.openLocation({
      longitude: longitude,
      latitude: latitude,
      scale: this.data.scale,
      name: name,
      address: address
    })
  },
  goxq: function (e) {
    var id = e.currentTarget.id;
    var type = e.currentTarget.dataset.type;
    if (type === "PARKING_LOT" || type === "PARKING_SPACE" || type === "CHARGE") {  //目前只做
      wx.navigateTo({
        url: "../info/index?id=" + id + "&lat=" + this.data.destinationlat + "&long=" + this.data.destinationlng + "&resourceType=" + type
      })
    }
  },
  goBack: function (e) {
    wx.navigateBack({
      delta: 2
    });
  },
  changeMode: function (e) {
    wx.navigateBack({
      delta: 1
    });
  },
  setCenter: function (e) {
    let mpCtx = wx.createMapContext("myMap");
    mpCtx.moveToLocation();
  }
})