@import '../../common/common.wxss';

.box {
  margin: 20rpx;
  min-height: 1rpx;
  z-index: 2;
}

.box-top-imgBox {
  width: 127rpx;
  height: 127rpx;
  margin: 40rpx auto 0 auto;
}

.box-top-text {
  height: 50rpx;
  font-size: 36rpx;
  font-weight: bold;
  color: #256bf5;
  line-height: 50rpx;
  text-align: center;
  margin-bottom: 90rpx;
}

.box-center {
  margin-bottom: 40rpx;
  margin: 0 auto 20rpx auto;
  width: 144rpx;
  height: 50rpx;
  font-size: 36rpx;
  font-weight: bold;
  color: #000000;
  line-height: 48rpx;
}

.box-bottom {
  min-height: 192rpx;
  height: 40rpx;
  font-size: 28rpx;
  font-weight: 400;
  color: #000000;
  line-height: 48rpx;
  width: 100%;
  justify-content: center;
  margin: 0 55rpx;
  width: calc(100% - 110rpx);
}
.convention_button {
  width: calc(100% - 150rpx) !important;
  line-height: 52rpx !important;
}
.convention_button.viewB {
  line-height: 80rpx !important;
  margin: 20rpx 75rpx;
}

.modalDlg-xyView {
  width: calc(100% - 150rpx);
  min-height: 100rpx;
  margin: 30rpx 75rpx;
  line-height: 50rpx;
}

.zbdl {
  height: 68rpx;
  font-size: 34rpx;
  font-weight: 400;
  color: #7e7e7e;
  line-height: 68rpx;
  text-align: center;
  margin-top: 16px;
}

.bTip {
  width: 100%;
  position: absolute;
  bottom: 60rpx;
  text-align: center;
  height: 40rpx;
  font-size: 28rpx;
  font-weight: 400;
  color: #a3a3a3;
  line-height: 40rpx;
}

.auth-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 32px;
}
.btn-auth {
  width: 50px;
  height: 50px;
  margin-bottom: 4px;
}
