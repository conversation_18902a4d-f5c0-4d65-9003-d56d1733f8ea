@import '/pages/common/common.wxss';
page {
  background: #F6F7FB;
}

.header{
  width: 100%;
  height: 80rpx;
  background-color: #ffffff;
  display: flex;
  flex-direction: row;
}
.headToolbar {
  line-height: 80rpx;
  font-size: 26rpx;
  font-weight: bold;
  color: #256BF5;
  padding-left: 37rpx;
}
.clear{
  margin-left: 33rpx;
  line-height: 80rpx;
  font-size: 26rpx;
  color: #7E7E7E;
}
.no-data{
  padding-top: 52rpx;
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.no-data .icon{
  width: 367rpx;
  height: 283rpx;
}
.no-data .tip{
  margin-top: 52rpx;
  font-size: 30rpx;
  font-weight: bold;
  color: #7E7E7E;
  line-height: 56rpx;
  text-align: center;
}