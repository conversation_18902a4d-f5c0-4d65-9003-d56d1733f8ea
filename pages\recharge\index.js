var util = require('../../utils/util');
var api = require('../../config/api.js');
const app = getApp()
Page({
  data: {
    amount:'',//其他充值金额
    isClick: true,
    imgUrl: api.imgUrl,
    id: '',
    tradeNo: '', //支付订单的id
    selectPay: "WeChatPay", //balance WeChatPay
    selectIndex: 0,
    accountName: app.globalData.userInfo.phone,
    balance: '0',
    giftBalance: '0', //当前余额中赠送的金额
    money: 0,
    list: [],
    withdrawableBalance: 0 //可提现余额
  },
  onLoad(options) {
    this.setData({
      accountName: options.accountName,
      amount:''
    })
  },
  onShow() {
    this.initData();
  },
  initData: function () {
    var that = this;
    util.request(api.getRechargeInfo, null, 'GET').then(function (res) {
      var infos = res.result;
      if (res.code == '0') {
        that.setData({
          selectIndex:0,
          id: infos.packages[0]['pkgId'],
          money: infos.packages[0]['price'],
          amount:'',
          balance: infos.balance,
          list: infos.packages,
          giftBalance: infos.giftBalance,
          withdrawableBalance: infos.withdrawableBalance
        })
      } else {}
    });
  },
  selectPackage(e) {
    var currentTarget = e.currentTarget;
    var id = currentTarget.id;
    this.setData({
      money: currentTarget.dataset.money,
      id: id,
      selectIndex: currentTarget.dataset.index,
      amount: ''
    })
  },
  selectPay(e) {
    var currentTarget = e.currentTarget;
    this.setData({
      selectPay: currentTarget.id,
    })
  },
  pay: function () {
    this.setData({
      isClick: false //在点击一次后，点击状态变为关闭，默认为开启
    })
    var clock = setTimeout(() => { //定义一个延时操作setTimeout
        this.setData({
          isClick: true
        })
        clearInterval(clock);
      }, 3000 //在3秒后，点击状态恢复为默认开启状态
    )
    util.showLoading('正在加载…')
    var that = this;
    util.request(api.payRecharge, {
      "id": that.data.id,
      "payType": "WECHAT_PAY",
      "amount":that.data.amount
    }, 'POST').then(function (res) {
      var infos = res.result;
      if (res.code == '0') {
        wx.hideLoading()
        that.setData({
          tradeNo: infos.tradeNo
        })
        that.payWeiPay(infos.timestamp, infos.nonceStr, infos.packageVal, infos.paySign, infos.signType)
      } else {
        wx.hideLoading()
        util.showToast(res.message);
      }
    });
  },
  payWeiPay: function (timeStamp, nonceStr, packageWord, paySign, signType) {
    var that = this;
    wx.requestPayment({ //吊起微信支付
      timeStamp: timeStamp,
      nonceStr: nonceStr,
      package: packageWord,
      signType: signType,
      paySign: paySign,
      success(res) {
        wx.hideLoading();
        console.log('支付成功');
      },
      fail(res) {
        wx.hideLoading();
        console.log('支付失败');
      },
      'complete': function (res) {
        console.log('支付完成');
        if (res.errMsg == 'requestPayment:ok') {
          var clock = setTimeout(() => {
            wx.navigateTo({
              url: '/pages/recharge/details/index?id=' + that.data.tradeNo,
            })
            clearInterval(clock);
          }, 1000)
        } else {
          wx.showModal({
            title: '提示',
            content: '充值失败'
          });
        }
        return;
      }
    })
  },
  showRefundTip() {
    wx.navigateTo({
      url: '/pages/recharge/withdrawal/index?withdrawableBalance=' + this.data.withdrawableBalance
    })
  },goDetail() {
    wx.navigateTo({
      url: '/pages/recharge/detail/index' 
    })
  },
  bindKeyInput: function (e) {//输入其他金额
    var amount = e.detail.value;
    this.setData({
      amount: amount,
      money:amount
    })
  },
  bindfocus:function(){//当选中其他金额时就自动取消选择充值额度的套餐
    this.setData({
      selectIndex: null,
      money:0
    })
  }
})