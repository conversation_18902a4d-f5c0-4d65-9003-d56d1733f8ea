<view class="saveOutView" >
  <cu-custom bgColor="white-bg" contentTitle="停车订单申诉" isBack="true"></cu-custom>
  <scroll-view scroll-y="true" class="scrollViewSa" style="margin-bottom: 40rpx;">
    <view class="title">申诉订单</view>
    <view class="box padding">
      <view class="conventionView">{{parkingBillInfo.parkName}}</view>
      <view class="conventionView">{{parkingBillInfo.plateNo}}</view>
      <view class="conventionView">{{parkingBillInfo.time}} {{parkingBillInfo.timeDesc}}</view>
      <view class="conventionView">{{parkingBillInfo.status=='PAID'?"已付：":"欠费"}}￥{{parkingBillInfo.amount}}</view>
    </view>
    <view class="title">申诉类型</view>
    <view class="box autowrapBox">
      <block wx:for="{{appealType}}" wx:key="index">
        <view id="{{item.appealType}}" class="typeBox {{item.appealType == selectAppealType ? 'choose':''}}" bindtap="chooseAppealType" data-remark='{{item.remark}}'>
          {{item.title}}
        </view>
      </block>
      <view class="describe">{{describe}}</view>
    </view>
    <view class="title" wx:if="{{selectAppealType=='WRONG_PARKING_TIME'||selectAppealType=='STILL_CHARGING'}}">申诉停车时间</view>
    <view class="box " wx:if="{{selectAppealType=='WRONG_PARKING_TIME'||selectAppealType=='STILL_CHARGING'}}">
      <view class="rowView timeView line">
        <view class="nowp">
          入车时间：
        </view>
        <picker mode="multiSelector" value="{{start_time}}" data-type="start_time" data-param='start_time_p' bindchange="changeDateTime" bindcolumnchange="changeDateTimeColumn" range="{{dateTimeArray}}"
        wx:if="{{selectAppealType=='WRONG_PARKING_TIME'}}">
          <view class="flex-row act_right">
            <text class="sel_text" wx:if="{{start_time_p!=''}}">{{start_time_p}}</text>
            <text class="sel_text" wx:else>请选择入车时间</text>
            <view class="icon-jt-left"></view>
          </view>
        </picker>
        <view style="flex: 1;text-align: right;" wx:else>{{start_time_p}}</view>
      </view>
      <view class="rowView timeView">
        <view class="nowp">
          出车时间:
        </view>
        <picker mode="multiSelector" value="{{end_time}}" data-type="end_time" data-param='end_time_p' bindchange="changeDateTime" bindcolumnchange="changeDateTimeColumn" range="{{dateTimeArray}}">
          <view class="flex-row act_right">
            <text class="sel_text" wx:if="{{end_time_p!=''}}">{{end_time_p}}</text>
            <text class="sel_text" wx:else>请选择出车时间</text>
            <view class="icon-jt-left"></view>
          </view>
        </picker>
      </view>
    </view>
    <view class="title">申述原因</view>
    <view class="box columnView">
      <textarea class="box-textarea" placeholder="请输入申诉原因" auto-focus name="textarea" bindinput="appealInput" value="{{appealRemark}}" />
      <view class="upBtnBox verticalCenter" bindtap="showActionSheet" wx:if="{{showQImg}}">
        <image src="../../../image/add.png" class="icon-img"></image>
        上传图片
      </view>
      <image src="{{showImgUrl}}" class="whImg" bindtap="showActionSheet" wx:else></image>
    </view>
  </scroll-view>
  <button class="convention_button {{allowClicking?'':'noClike'}}" bindtap="{{allowClicking?'addParkingBillAppeal':'tip'}}"> 提交</button>
</view>
