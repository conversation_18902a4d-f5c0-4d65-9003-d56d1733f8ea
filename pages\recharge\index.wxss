/* pages/recharge/index.wxss */
@import '../common/common.wxss';
@import '../components/pay.wxss';

page {
  background: #F6F7FB;
}

.recharge {
  min-height: 150rpx;
}

.info {
  width: 710rpx;
  height: 340rpx;
  background: #FFFFFF;
  border-radius: 20rpx 20rpx 20rpx 20rpx;
  margin: 20rpx 20rpx 20rpx 20rpx;
}

.info-one {
  background-size: 100% 100%;
  background-repeat: no-repeat;
  width: calc(100% - 40rpx);
  min-height: 220rpx;
  background-color: #ffffff;
  margin: 20rpx 20rpx 0 20rpx;
  padding: 0 0;
}

.info-oneR-tilte-up {
  width: calc(100% - 64rpx);
  height: calc(100% - 188rpx);
  padding: 0 32rpx;
  color: #353535;
  line-height: 45rpx;
  font-size: 26rpx;

  font-weight: bold;
  padding-top: 19rpx;
}

.sqtk {
  font-weight: 400;
  font-size: 26rpx;
  width: 120rpx;
  height: 56rpx;
  line-height: 56rpx;
  background: rgba(37, 107, 245, 0.1);
  border-radius: 28rpx 28rpx 28rpx 28rpx;
  opacity: 1;
  border: 1rpx solid rgba(37, 107, 245, 0.2);
  color: #256BF5;
  text-align: center;
  margin-right: 27rpx;
  margin-top: 20rpx;
}

.info-oneR-tilte-down {
  width: 100%;
  min-height: 162rpx;
  z-index: 1;
  text-align: center;
}

.info-oneR-text-up {
  min-width: 34rpx;
  height: 78rpx;
  font-size: 56rpx;
  font-weight: bold;
  color: #353535;
  line-height: 78rpx;
  margin-top: 51rpx;
}

.info-oneR-text-down {
  min-width: 96rpx;
  height: 40rpx;
  font-size: 28rpx;
  font-weight: 400;
  color: #353535;
  line-height: 40rpx;
}

.info-oneR-tip {
  min-height: 40rpx;
  font-size: 26rpx;
  font-weight: bold;
  color: #353535;
  line-height: 40rpx;
  background-color: #FFFFFF;
  margin: 30rpx auto 23rpx 38rpx;
}

.recharge {
  min-height: 150rpx;
}

.other {
  height: 110rpx;
  font-size: 34rpx;
  font-weight: 400;
  color: #353535;
  line-height: 110rpx;
  width: calc(100% - 60rpx);
  padding-left: 20rpx;
}

.other-input {
  height: 110rpx;
  line-height: 110rpx;
}

.other-tip {
  width: 556rpx;
  height: 37rpx;
  font-size: 26rpx;
  font-weight: 400;
  color: #7E7E7E;
  line-height: 37rpx;
  margin:0 0 30rpx 50rpx;
}
.infoBox-pay {
  line-height: 96rpx;
}
.other.selected{
  border: #256BF5 1rpx solid;

}