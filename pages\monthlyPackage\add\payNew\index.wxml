<view class="saveOutView">
  <cu-custom bgColor="white-bg" contentTitle="包月办理" isBack="true"></cu-custom>
  <scroll-view scroll-y="true" class="scrollViewSa">
    <view class="backWihte columnView">
      <view class="info-bt">
        <view class="icon-sjx-left-tilte"></view>
        <view class="info-bt-text">包月车场</view>
      </view>
      <view class="info-one-tilte-up" style="background-image: url('{{imgUrl}}');">
        <view class="info-one-tilte">{{ruleInfo.ruleName}}</view>
        <view class="info-one-tilteF">{{parkName}}</view>
        <view class="info-one-tilteF" wx:if="{{ruleInfo.scope>0}}" bindtap="goParkListPage"
          >{{ruleInfo.parkCountDesc}}></view
        >
        <view class="info-one-carTool" bindtap="showBox">
          <view class="info-one-carTool-text">包月车牌：{{plateNo}}</view>
          <view class="info-one-carTool-btn icon-jt-left-bj" hidden="{{isRenewal}}">切换</view>
          <view class="icon-jt-left" hidden="{{isRenewal}}"></view>
        </view>
      </view>
    </view>
    <view class="backWihte columnView">
      <view class="info-row">
        <view class="icon-sjx-left-tilte"></view>
        <view class="info-title">包月套餐</view>
        <view class="circle-i-orange" catchtap="showApplication">使用须知</view>
      </view>
      <view>
        <view class="infoListBox">
          <block wx:for="{{packageList}}" wx:key="index">
            <view
              bindtap="select"
              data-info="{{item}}"
              id="{{index}}"
              class="infoBox {{selectIndex == index ? 'choose':''}}"
            >
              <view class="infoBox-ysb" wx:if="{{item.sfyh}}">限时优惠</view>
              <view class="infoBox-text" style="{{item.sfyh?'': 'margin-top: 22rpx'}}">
                <text>{{item.pkgDesc}}</text></view
              >
            </view>
          </block>
        </view>
      </view>
    </view>
    <view class="backWihte columnView">
      <view class="info-bt">
        <view class="icon-sjx-left-tilte"></view>
        <view class="info-bt-text">支付方式</view>
      </view>
      <view class="paymentListBox">
        <block wx:for="{{payList}}" wx:key="index">
          <view
            id="{{item.payType}}"
            class="infoBox  {{selectPay == item.payType ? 'choosePay':''}}"
            bindtap="selectPayMode"
            wx:if="{{item.payType=='BALANCE_PAY'}}"
          >
            <view class="infoBox-text" style="margin-top: 14rpx">余额</view>
            <view class="infoBox-text" id="ye" data-money="{{item.iconUrl}}">{{item.iconUrl}}元</view>
          </view>
          <view
            id="{{item.payType}}"
            class="infoBox {{selectPay == item.payType ? 'choosePay':''}}"
            style="display: flex; flex-direction: column"
            bindtap="selectPayMode"
            wx:else
          >
            <image
              class="img"
              src="{{item.iconUrl}}"
              style="width: 48rpx; height: 48rpx; position: static; margin: 14rpx auto 0"
            ></image>
            <view class="infoBox-text">微信支付</view>
          </view>
        </block>
      </view>
    </view>
    <view class="infoBox-money columnView">
      <view class="infoBox-pay" style="margin-left: 52rpx">应付金额:</view>
      <view class="infoBox-pay" style="color: #ff2d2d">{{packageList[selectIndex].price}}元</view>
    </view>
    <view class="box_convention_button">
      <button class="convention_button" bindtap="pay">支付</button>
    </view>
  </scroll-view>
  <bottomListDialog
    catchtouchmove="preventTouchMove"
    bind:selectItem="selectItem"
    isShow="{{showPlateNoBox}}"
    list="{{carList}}"
    rangekey="plateNo"
    disablekey="bagable"
    disableText="已包月，需办理请前往续费"
    value="{{plateNo}}"
  ></bottomListDialog>
</view>

<customModal
  isShow="{{isShowSlotModal}}"
  title="使用须知"
  showFooter="{{false}}"
  bind:close="closeModal"
  data-type="slot"
>
  <!-- 自定义内容插槽 -->
  <view slot="content" class="custom-content">
    <monthly-rules />
  </view>
  <!-- 自定义底部插槽 -->
  <view slot="footer" class="custom-footer">
    <button class="custom-btn" bindtap="closeModal" data-type="slot">我知道了</button>
  </view>
</customModal>
