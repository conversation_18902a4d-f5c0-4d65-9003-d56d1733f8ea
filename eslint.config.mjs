import globals from 'globals'
import pluginJs from '@eslint/js'

export default [
  {
    files: ['**/*.js'],
    languageOptions: {
      sourceType: 'module',
    },
    ignores: ['node_modules/*', 'pnpm-lock.yaml', 'miniprogram_npm', '.git', '**/ec-canvas'],
  },
  {
    languageOptions: {
      globals: {
        ...globals.browser,
        ...globals.node,
        wx: 'readonly',
        App: 'readonly',
        Page: 'readonly',
        getCurrentPages: 'readonly',
        getApp: 'readonly',
        Component: 'readonly',
        requirePlugin: 'readonly',
        requireMiniProgram: 'readonly',
      },
    },
  },
  pluginJs.configs.recommended,
  {
    rules: {
      'no-unused-vars': 0,
      'no-debugger': 0,
      'no-empty': 0,
    },
  },
]
