<view class="details-box reservation">
  <view class="title-value-box">
    <view class="title-value-box-line one">
      {{record.parkName}}
    </view>
    <view class="title-value-box-line two">
      {{record.plateNo}}
    </view>
    <view class="title-value-box-line two">
      {{record.reservationTime}}
    </view>
    <view class="stateBox {{record.reservationState }}">
      {{record.reservationStateDesc}}
    </view>
    <view class="rowView">
      <view class="title-value-box-btn m1 "  bindtap="goXq">详情</view>
      <view class="title-value-box-btn m0" bindtap="cancel" wx:if="{{record.reservationState == 'EFFECTIVE'}}">取消预约</view>
      <view class="title-value-box-btn m0 o" bindtap="add" wx:else>再次预约</view>
    </view>
    <!-- <view class="bottomTip" hidden="{{record.reservationState != 'EXPIRED'}}">提示：预约订单超时未入场超过5次，3个月内不可再预约车位</view> -->
  </view>
</view>