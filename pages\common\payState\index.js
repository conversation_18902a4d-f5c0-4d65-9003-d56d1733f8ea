var util = require('../../../utils/util');
var api = require('../../../config/api.js');
Page({
  data: {
    allowXY:true,//设置车牌为默认车牌
    type:'',//表示从哪个页面跳转过来的
    info:{},
    id:''
  },
  onLoad(options) {
    this.setData({
      id:options.id,
      type:options.type
    })
  },
  onShow() {
    var that = this;
    util.showLoading('正在加载…')
    util.request(api.getArrearsDetails + that.data.id, null, 'GET').then(function (res) {
      var infos = res.result;
      if (res.code == '0') {
        that.setData({
          info: infos
        })
        wx.hideLoading()
      } else {
        util.showToast(res.message);
        wx.hideLoading()
      }
    });
  },
  goIndex() {
    wx.switchTab({
      url: '/pages/index/index',
    })
  },
  goBack() {
    wx.navigateBack({
      delta: 2
    })
  },
  allow() {
    if (this.data.allowXY) {
      this.setData({
        allowXY: false
      })
    } else {
      this.setData({
        allowXY: true
      })
    }
  },
  addCar(){
    util.request(api.addCarByQrcode , {
      id: this.id
    }, 'POST').then(function (res) {
      //产品要求没有任何提示
    });
    this.goIndex();
  }
})