@import '../../../common/common.wxss';
@import '../../../common/ccxqlistCss.wxss';

page {
  background-color: #F6F7FB;
}

.searchBar {
  width: calc(100% - 40rpx);
  height: 96rpx;
  background: #FFFFFF;
  border-radius: 0rpx 0rpx 0rpx 0rpx;
  opacity: 0.96;
  flex-direction: row;
  display: flex;
  margin: 20rpx 20rpx;
  box-shadow: 0rpx 10rpx 0rpx 1rpx rgba(228, 234, 248, 0.16);
  border-radius: 20rpx 20rpx 20rpx 20rpx;
}

.searchBar input {
  width: calc(100% - 170rpx);
  height: 100%;
}

.cuIcon-delete {
  width: 30rpx;
  height: 30rpx;
  background: #A0A1A7;
  opacity: 1;
  margin: 34rpx 10px 34rpx 40rpx;
}

.listDiv-search-dc-num {
  display: flex;
  flex-direction: row;
}

.listDiv-d-left {
  width: 100%;
}

.Icon-search {
  margin-left: 30rpx;
}
.listDiv-search-dc {
  flex-wrap: wrap;
  display: flex;
  padding-left: 20rpx;
}

