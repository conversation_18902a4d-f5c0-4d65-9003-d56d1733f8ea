var util = require('../../../../../utils/util')
var api = require('../../../../../config/api.js')
Page({
  data: {
    operate: '', //modify代表是修改,add代表添加
    allowClicking: false, //满足条件后提交审核按钮才可以点击
    allowXY: false, //是否勾选了协议
    carNum: '',
    id: '',
    frontFile_sl: api.imgUrl + 'drivingLicense.png',
    contraryFile_sl: api.imgUrl + 'drivingLicense_back.png',
    vehicleFile_sl: api.imgUrl + 'drivingLicense_car.png',
    contraryFile: '',
    frontFile: '',
    identification: '',
    name: '',
    phone: '',
    vehicleFile: '',
  },
  onLoad(options) {
    var operate = options.operate
    this.setData({
      operate: operate,
      carNum: options.carNum,
      id: options.id,
      sourceType: ['camera', 'album'],
    })
    if (operate == 'modify') {
      var that = this
      const eventChannel = this.getOpenerEventChannel()
      // 监听acceptDataFromOpenerPage事件，获取上一页面通过eventChannel传送到当前页面的数据
      eventChannel.on('acceptDataFromOpenerPage', function (data) {
        that.setData({
          contraryFile: data.contraryFile,
          frontFile: data.frontFile,
          identification: data.identification,
          name: data.name,
          phone: data.phone,
          vehicleFile: data.vehicleFile,
          frontFile_sl: data.frontFile,
          contraryFile_sl: data.contraryFile,
          vehicleFile_sl: data.vehicleFile,
        })
      })
    } else {
    }
  },
  onReady() {},
  onShow() {},
  showActionSheet: function (e) {
    const that = this
    wx.showActionSheet({
      itemList: ['拍照', '相册'],
      itemColor: '',
      //成功时回调
      success: function (res) {
        if (!res.cancel) {
          /*
           res.tapIndex返回用户点击的按钮序号，从上到下的顺序，从0开始
           比如用户点击本例中的拍照就返回0，相册就返回1
           我们res.tapIndex的值传给chooseImage()
          */
          var id = e.currentTarget.id
          that.chooseImage(res.tapIndex, id)
        }
      },
      //失败时回调
      fail: function (res) {
        console.log('获取图片调用失败')
      },
      complete: function (res) {},
    })
  },
  chooseImage(tapIndex, id) {
    //上传图片
    const that = this
    wx.chooseImage({
      //count表示一次可以选择多少照片
      count: 1,
      //sizeType所选的图片的尺寸，original原图，compressed压缩图
      sizeType: ['compressed'],
      //如果sourceType为camera则调用摄像头，为album时调用相册
      sourceType: [that.data.sourceType[tapIndex]],
      success(res) {
        // tempFilePath可以作为img标签的src属性显示图片
        const tempFilePaths = res.tempFilePaths
        for (var x = 0; x < tempFilePaths.length; x++) {
          wx.getFileSystemManager().readFile({
            filePath: tempFilePaths[x], //选择图片返回的相对路径
            encoding: 'base64', //这个是很重要的
            success: res => {
              //成功的回调//返回base64格式
              that.upImg(id, res.data)
            },
          })
        }
        that.saveData(id + '_sl', tempFilePaths)
      },
    })
  },
  upImg: function (id, img) {
    var fileBase = 'data:image/png;base64,' + img
    var that = this
    util.showLoading('上传中')
    wx.request({
      url: api.uploadPicture, //将传入的网址与baseurl拼接
      data: {
        fileBase: fileBase,
        fileType: 2,
      }, //有data就传入data，没有设置为空
      method: 'POST', //请求的类型
      header: {
        //设置请求头
        'content-Type': 'application/json',
        'token': wx.getStorageSync('token'),
      },
      success(res) {
        //成功时的回调

        var result = res.data
        if (result.code == '0') {
          wx.hideLoading()
          that.saveData(id, result.result)
          that.saveData(id + '_sl', result.result)
        } else {
          wx.hideLoading()
          util.showToast(result.message)
        }
      },
      fail() {
        //失败时的回调
        wx.hideLoading()
        util.showToast('图片过大，请上传2M以内图片，或选择拍照上传。')
      },
    })
  },
  saveData(name, obj) {
    var data = {}
    data[name] = obj
    this.setData(data)
  },
  bindKeyInput: function (e) {
    this.saveData(e.currentTarget.id, e.detail.value)
  },
  formSubmit(e) {
    var data = this.data
    var param = e.detail.value
    if (param.name == '') {
      util.showToast('请输入姓名')
    } else if (param.phone == '') {
      util.showToast('请输入手机号码')
    } else if (param.identification == '') {
      util.showToast('请输入身份证号')
    } else if (data.frontFile == '') {
      util.showToast('请上传行驶证正面')
    } else if (data.contraryFile == '') {
      util.showToast('请上传行驶证副面')
    } else if (data.vehicleFile == '') {
      util.showToast('请上车头照片')
    } else if (!data.allowXY) {
      util.showToast('请阅读并同意平台服务协议及隐私协议')
    } else {
      this.vehicleAuthentication()
    }
  },
  vehicleAuthentication(fileName) {
    //提交认证审核
    var that = this
    var data = that.data
    util.showLoading('上传中')
    util
      .request(
        api.vehicleAuthentication,
        {
          contraryFile: data.contraryFile,
          frontFile: data.frontFile,
          identification: data.identification,
          name: data.name,
          phone: data.phone,
          vehicleFile: data.vehicleFile,
          vehicleId: data.id,
        },
        'POST'
      )
      .then(function (res) {
        if (res.code == '0') {
          wx.hideLoading()
          wx.showToast({
            title: '认证成功',
            icon: 'success',
            duration: 2000,
          })
          wx.navigateBack({
            delta: 1,
          })
        } else {
          wx.hideLoading()
          util.showToast(res.message)
        }
      })
  },
  allow() {
    if (this.data.allowXY) {
      this.setData({
        allowXY: false,
      })
    } else {
      this.setData({
        allowXY: true,
      })
    }
  },
  viewAgreement: function (e) {
    var id = e.currentTarget.id
    wx.navigateTo({
      url: '/pages/common/viewAgreement/index?id=' + id,
      fail: function (res) {
        console.log(res)
      },
    })
  },
})
