const util = require('./util')
const api = require('../config/api')
const app = getApp()

/**
 * 获取广告数据
 * @param {number} position 广告位置
 */
async function fetchAd(position) {
  try {
    const { result } = await util.request(api.wxPageDetailed, { page: 1, pageSize: 1, position }, 'post')
    let data = []
    let id = 0
    if (result && result.items && result.items.length) {
      const item = result.items[0]
      id = item.id
      data = item.advertisementBindingList
    }
    return { data, id }
  } catch (error) {
    console.log(error)
  }
}

/**
 * 响应广告点击事件
 * @param {object} item 广告对象
 * @param {number} id 广告位置id
 */
async function handleAdClick(item, id) {
  try {
    let uuid = wx.getStorageSync('uuid')
    if (!uuid) {
      uuid = util.generateUUID()
      wx.setStorage('uuid', uuid)
    }
    await util.request(
      api.wxClickRecord,
      {
        Id: item.advertisementId,
        OpenId: app.globalData?.userInfo?.driverId ? app.globalData?.userInfo?.driverId : 'visitor-' + uuid,
        AdvertisementSpaceId: id,
      },
      'GET'
    )
    if (item.url) {
      if (item.url.startsWith('/pages')) {
        wx.navigateTo({ url: item.url })
      } else if (item.url.startsWith('/miniProgram')) {
        const obj = util.getURLParameters(item.url)
        console.log('跳转第三方小程序参数：', obj)
        wx.navigateToMiniProgram(obj)
      } else if (item.url.startsWith('#小程序')) {
        console.log('跳转shortLink小程序参数：', item.url)
        wx.navigateToMiniProgram({ shortLink: item.url })
      } else {
        console.log(item.url)
        wx.navigateTo({
          url: '/pages/webview/webview?url=' + encodeURIComponent(item.url),
        })
      }
    }
  } catch (error) {
    console.log(error)
  }
}

/**
 * @description: 获取图片状态
 * @param {number} plateNo 车牌号
 * @return {number} imageState 图片状态 0隐藏，1可看小图，2可点击看大图
 */
async function getImageState(plateNo) {
  try {
    let imageState = 0

    if (app.globalData.userInfo.loginStatus === 'LOGGED_IN') {
      const res = await util.request(api.fullPlateList, {}, 'get')
      if (res.code == '200') {
        const carList = res.data.results
        // 绑定车牌，可以查看小图；认证车主，可以查看大图；未绑定不可查看
        //reviewState=1代表认证通过
        carList.forEach(item => {
          if (item.plateNo === plateNo) {
            imageState = item.reviewState == 1 ? 2 : 1
          }
        })
      }
    }

    return imageState
  } catch (error) {
    console.log(error)
  }
}

/**
 * @description: 获取停车状态
 * @param {*} plateNo 车牌号
 */
async function getParkState(plateNo) {
  try {
    const res = await util.request(api.getOrderParking, { plateNo }, 'GET')
    console.log('停车状态：', res)
    let result = {
      parkCode: '',
      uniqueId: '',
      activeLeaveState: 0,
    }

    let { code, data } = res || {}
    if (code == '200' && data) {
      result.activeLeaveState = data?.activeLeaveState || 0
      result.parkCode = data.parkCode
      result.uniqueId = data.uniqueId
    }

    return result
  } catch (error) {
    console.log(error)
  }
}

/**
 * @description: 车辆主动离场
 * @param {*} parkCode
 * @param {*} uniqueId
 */
function carLeave(parkCode, uniqueId) {
  return new Promise((resolve, reject) => {
    wx.showModal({
      title: '温馨提示',
      content: '确认离场？',
      showCancel: true,
      confirmText: '确认',
      confirmColor: app.globalData.tipBtnColor,
      async success(res) {
        if (res.confirm) {
          try {
            util.showLoading('正在加载…')
            let params = {
              parkCode,
              uniqueId,
            }
            if (app.globalData?.userInfo?.driverId) {
              params.driverId = app.globalData?.userInfo?.driverId
              params.mobile = app.globalData?.userInfo?.phone
            }
            const res2 = await util.request(api.getDriverActiveCarOut, params, 'GET')
            wx.hideLoading()
            if (res2.code == 200) {
              wx.showModal({
                title: '温馨提示',
                content: res2.data.tipText,
                confirmText: '我知道了',
                confirmColor: app.globalData.tipBtnColor,
              })
              resolve(true)
            } else {
              util.showToast(res2.msg)
              reject()
            }
          } catch (error) {
            console.log(error)
            wx.hideLoading()
            util.showToast('服务异常，请稍后重试')
          }
        }
      },
      fail() {
        resolve(false)
      },
    })
  })
}

module.exports = {
  fetchAd,
  handleAdClick,
  getImageState,
  getParkState,
  carLeave,
}
