<view class="saveOutView">
  <cu-custom bgColor="white-bg" contentTitle="开票历史" isBack="true"></cu-custom>
  <view wx:if="{{ list.length == 0 }}">
    <image src="../../../image/qfjf-null.png" class="nullImg"></image>
    <view class="nullTip">暂无数据</view>
  </view>
  <scroll-view wx:else class="scrollViewSa" scroll-y="true">
    <block wx:for="{{list}}" wx:key="index">
      <view class="listDiv columnView" bindtap="goXq" data-info="{{item}}">
        <view class="rowView">
          <view class="listDiv-name ">{{item.invoiceContent}}</view>
          <view class="icon-jt-left"></view>
        </view>
        <view class="rowView">
          <view class="listDiv-time ">{{item.requestTime}}</view>
          <view class="listDiv-amount">{{item.invoiceAmount}}元</view>
        </view>
        <view class="rowView line"> 
          <view class="listDiv-time m">{{item.invoiceStateText}}</view>
          <view class="btn" catchtap="reissueInvoice" data-id="{{item.invoiceId}}" wx:if="{{item.invoiceState==2}}">重发发票</view>
          <view class="btn" catchtap="applyForReopening" data-type="{{item.invoiceContentType}}" wx:if="{{item.invoiceState==3}}">申请重开</view>
        </view>
      </view>
    </block>
  </scroll-view>
  <view wx:if="{{showNullMoreTip}}" class="njgtip">没有更多信息了</view>
  <!-- 预览模式 -->
  <view class="preview-box" hidden="{{!isShowPreviewBox}}" bindtap="closePreviewBox">
    <view class="container" catchtap="placeholderFun">
      <view class="header">
        开具电子发票
        <image src="/image/close.png" bindtap="closePreviewBox" class="close" />
      </view>
      <view class="content">
        <view class="section-content columnView">
          <view class="form-row">
            <view class="form-label required">电子邮箱</view>
            <view class="form-value">
              <input  name="email" class="input" placeholder="请输入电子邮箱" bindinput="bindKeyInput" maxlength='50'/>
            </view>
          </view>
          <view class="form-row">
            <view class="form-label">发票类型</view>
            <view class="form-value">
              <text class="text">电子发票</text>
            </view>
          </view>
        </view>
        <view class="tip">请确认电子邮箱号无误，电子发票将在系统开具后发送至该邮箱，请注意查收</view>
        <view class="form-control">
          <view class="btn" bindtap="onSubmit">确认提交</view>
        </view>
      </view>
    </view>
  </view>
</view>