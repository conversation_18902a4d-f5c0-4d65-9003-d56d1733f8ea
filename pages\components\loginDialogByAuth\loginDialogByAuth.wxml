<view class="half-screen" catchtouchmove="preventTouchMove">
  <!--屏幕背景变暗的背景  -->
  <view class="background_screen" catchtap="hideModal" wx:if="{{isShow}}"></view>
  <!--弹出框  -->
  <view animation="{{animationData}}" class="attr_box" wx:if="{{isShow}}">
    <view class="dialog-box">
      <view class="dialog-head">
        <view class="dialog-title">选择登录方式</view>
        <view class="close2ImgBox">
          <image src="../../../image/close.png" class="close2Img" catchtap="hideModal"></image>
        </view>
      </view>

      <view class="modalDlg-xyView">
        <image class="yes_icon" wx:if="{{!allowXY}}" src="../../../image/yes-icon.png" bindtap="allow"></image>
        <image class="yes_icon" wx:if="{{allowXY}}" src="../../../image/yes-icon-select.png" bindtap="allow"></image>
        <text class="modalDlg-xyView-text">阅读并同意</text>
        <text class="modalDlg-xyView-text xy" id="serviceUrl" bindtap="viewAgreement">《服务协议》、</text>
        <text class="modalDlg-xyView-text xy" id="privacyUrl" bindtap="viewAgreement"
          >《软件注册及隐私政策协议》、</text
        >
        <text class="modalDlg-xyView-text xy" id="logonUrl" bindtap="viewAgreement">《登录政策》</text>
      </view>

      <view class="btn-container">
        <view wx:if="{{!allowXY}}" class="convention_button bcolor viewB" bindtap="sqbd">快捷登录</view>
        <button
          wx:if="{{allowXY}}"
          class="convention_button bcolor"
          open-type="getPhoneNumber"
          bindgetphonenumber="getPhoneNumber"
        >
          快捷登录
        </button>
        <button class="convention_button gcolor" bind:tap="login">手机号验证登录</button>
      </view>
    </view>
  </view>
</view>
