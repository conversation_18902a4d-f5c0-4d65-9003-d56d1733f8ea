@import '../../common/common.wxss';

page {
  background-color: #F6F9FF;
}

.cardBox {
  width: 685rpx;
  height: 350rpx;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  margin: 41rpx 37rpx 0 28rpx;
}

.cardBox-t {
  color: #FFFFFF;
  margin: 39rpx 61rpx 93rpx 49rpx;
}

.userinfo-text {
  margin-left: 26rpx;
  flex: 1;
}

.username {
  font-size: 32rpx;
  font-weight: 800;
  margin-bottom: 16rpx;
}

.phone {
  font-size: 28rpx;
  font-weight: bold;
}

.cardBox-t-r {
  width: 169rpx;
  min-height: 26rpx;
  font-size: 28rpx;

  font-weight: bold;
  margin-left: 10rpx;
}

.userinfo-avatar {
  width: 102rpx;
  height: 102rpx;
  background: #F0493A;
  border: 2px solid rgba(255, 255, 255, 0.25);
  border-radius: 50%;
}

.cardBox-b {
  width: 198rpx;
  height: 56rpx;
  background: #FFFFFF;
  box-shadow: 0rpx 4rpx 6rpx 0rpx rgba(255, 47, 47, 0.3);
  border-radius: 28rpx;
  font-size: 28rpx;

  font-weight: bold;
  color: #FF6146;
  text-align: center;
  line-height: 56rpx;
  margin: 0 61rpx 93rpx 49rpx;
}

.circle-i-orange {
  margin-left: 50rpx;
  width: 140rpx;
  height: 28rpx;
  font-size: 24rpx;

  font-weight: 500;
  color: #353535;
  line-height: 28rpx;
}