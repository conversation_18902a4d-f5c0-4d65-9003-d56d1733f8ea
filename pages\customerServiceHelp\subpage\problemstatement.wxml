<view class="saveOutView ProblemConsultation" wx:if="{{type=='ProblemConsultation'}}">
  <cu-custom bgColor="white-bg" contentTitle="问题咨询" isBack="true"></cu-custom>
  <view class="title">问题类型</view>
  <view class="box autowrapBox">
    <block wx:for="{{questionType}}" wx:key="index">
      <view id="{{item.type}}" class="typeBox {{item.type == selectQuestionType ? 'choose':''}}" bindtap="selectQuestionType">
        {{item.title}}
      </view>
    </block>
  </view>
  <!-- <view class="title" wx:if="{{selectQuestionType==1||selectQuestionType==7}}">问题订单</view>
  <view class="box " wx:if="{{selectQuestionType==1||selectQuestionType==7}}">
    <view class="rowView" bindtap="selectOrder">
      <view class="selectText">请选择订单</view>
      <view class="selectText-arrow"></view>
    </view>
    <view class="box padding line " wx:if="{{flagDetails}}">
      <view class="conventionView">山水公园南门停车场{{parkName}}</view>
      <view class="conventionView">桂C12345{{plateNo}}</view>
      <view class="conventionView">2023-02-07至2023-03-07{{timeDesc}}</view>
    </view>
  </view> -->
  <view class="title">问题描述</view>
  <view class="box" style="margin-bottom: 40rpx;">
    <textarea class="box-textarea" placeholder="请输入您的问题描述" auto-focus name="textarea" bindinput="questionInput" value="{{questionValue}}" />
    <view class="upBtnBox verticalCenter" bindtap="showActionSheet" wx:if="{{showQImg}}">
      <image src="../../../image/add.png" class="icon-img"></image>
      上传图片
    </view>
    <image src="{{showImgUrl}}" class="whImg" bindtap="showActionSheet" wx:else></image>
  </view>
  <button class="convention_button {{allowClicking?'':'noClike'}}" bindtap="addProblemConsultation"> 提交</button>
</view>
<view class="saveOutView" wx:if="{{type=='CUSTOMERSERVICE'}}">
  <cu-custom bgColor="white-bg" contentTitle="联系客服" isBack="true"></cu-custom>
  <view class="box">
    <view class="rowView line">
      <view class="text">联系电话</view>
      <view class="text_value">0773-3690690</view>
    </view>
    <view class="rowView">
      <view class="text">电子邮箱</view>
      <view class="text_value"><EMAIL></view>
    </view>
  </view>
</view>
<view class="saveOutView" wx:if="{{type=='feedback'}}">
  <cu-custom bgColor="white-bg" contentTitle="反馈详情" isBack="true"></cu-custom>
  <!-- <view class="title">问题订单</view>
  <view class="box padding">
    <view class="conventionView">{{parkName}}</view>
    <view class="conventionView">{{plateNo}}</view>
    <view class="conventionView">{{timeDesc}}</view>
  </view> -->
  <view class="title">咨询信息</view>
  <view class="box padding">
    <view class="conventionView line" style="margin-bottom: 20rpx;padding-bottom: 15rpx;">
      问题描述：{{feedback.content}}
    </view>
    <view class="upBtnBox verticalCenter">
      <image src="{{feedback.picUrls[0]}}" class="img"></image>
    </view>
  </view>
  <view class="title">反馈状态</view>
  <view class="box padding">
    <view class="conventionView {{feedbackStateClass[feedback.state]}}">{{feedback.stateText}}</view>
    <view class="conventionView">{{feedback.replyContent}}</view>
  </view>
</view>
<view class="saveOutView" wx:if="{{type=='parkingAppealOrderDetails'}}">
  <cu-custom bgColor="white-bg" contentTitle="反馈详情" isBack="true"></cu-custom>
  <view class="title">问题订单</view>
  <view class="box padding">
    <view class="conventionView">{{parkingAppealOrderDetails.billInfo.parkName}}</view>
    <view class="conventionView">{{parkingAppealOrderDetails.billInfo.formatPlateNo}}</view>
    <view class="conventionView">{{parkingAppealOrderDetails.billInfo.parkStartTime}} {{parkingAppealOrderDetails.billInfo.parkTimeText}}</view>
    <view class="conventionView">{{parkingAppealOrderDetails.status=='PAID'?"已付：":"欠费"}}￥{{parkingAppealOrderDetails.billInfo.shouldPayTotal}}</view>
  </view>
  <view class="title">申诉信息</view>
  <view class="box padding">
    <view class="conventionView line" style="margin-bottom: 20rpx;padding-bottom: 15rpx;">
      <view class="conventionView">申诉类型：{{parkingAppealOrderDetails.appealTypeText}}</view>
      <view class="conventionView">驶入时间：{{parkingAppealOrderDetails.appealInTime}}</view>
      <view class="conventionView" wx:if="{{parkingAppealOrderDetails.appealType=='WRONG_PARKING_TIME'||parkingAppealOrderDetails.appealType=='STILL_CHARGING' }}">驶离时间：{{parkingAppealOrderDetails.appealOutTime==null?"":parkingAppealOrderDetails.appealOutTime}}</view>
      <view class="conventionView">申诉理由：{{parkingAppealOrderDetails.appealRemark}}</view>
    </view>
    <view class="upBtnBox verticalCenter">
      <image src="{{parkingAppealOrderDetails.picUrls[0]}}" class="img"></image>
    </view>
  </view>
  <view class="title">反馈状态</view>
  <view class="box padding">
    <view class="conventionView {{parkingAppealOrdeClass[parkingAppealOrderDetails.appealState]}}">{{parkingAppealOrderDetails.appealStateText}}</view>
    <view class="conventionView">{{parkingAppealOrderDetails.handleRemark}}</view>
  </view>
</view>