Component({
  /**
   * 组件的一些选项
   */
  options: {
    multipleSlots: true, // 在组件定义时的选项中启用多slot支持
    addGlobalClass: true,
  },
  /**
   * 组件的属性列表
   */
  properties: {
    // 是否显示modal
    isShow: {
      type: Boolean,
      value: false,
    },
    // 标题
    title: {
      type: String,
      value: '提示',
    },
    // 内容
    content: {
      type: String,
      value: '',
    },
    // 是否显示关闭按钮
    showClose: {
      type: Boolean,
      value: true,
    },
    // 是否显示底部按钮
    showFooter: {
      type: Boolean,
      value: true,
    },
    // 取消按钮文字
    cancelText: {
      type: String,
      value: '取消',
    },
    // 确认按钮文字
    confirmText: {
      type: String,
      value: '确定',
    },
    // 自定义宽度
    width: {
      type: String,
      value: '680rpx',
    },
    // 自定义样式
    customStyle: {
      type: String,
      value: '',
    },
  },

  /**
   * 组件的初始数据
   */
  data: {},

  /**
   * 组件的方法列表
   */
  methods: {
    // 防止滑动穿透
    preventTouchMove() {
      return
    },

    // 关闭弹窗
    closeModal() {
      this.triggerEvent('close')
    },

    // 取消按钮点击事件
    onCancel() {
      this.triggerEvent('cancel')
      this.closeModal()
    },

    // 确认按钮点击事件
    onConfirm() {
      this.triggerEvent('confirm')
      this.closeModal()
    },
  },
})
