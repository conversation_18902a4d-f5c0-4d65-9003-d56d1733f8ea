var util = require('../../../utils/util')
var api = require('../../../config/api.js')
Page({
  data: {
    isShowRules: false, //显示包月规则的弹窗
    rules: [], //包月规则
    bagMyself: 0, //是否自己办理:0表示否，1表示是
    certCode: '',
    info: {},
  },
  onLoad(options) {
    this.setData({
      bagMyself: options.bagMyself,
      certCode: options.certCode,
    })
  },
  renewImmediately() {
    //立即续费
    // var info = this.data.info;
    // wx.navigateTo({
    //   url: '/pages/monthlyPackage/add/pay/index?parkId=' + info.parkId+"&isRenewal=true"+"&plateNo="+info.plateNo+"&certCode="+info.certCode,
    //   success: (res) => {//isRenewal=true点击立即续费的时候不能切换车牌
    //     res.eventChannel.emit('setData', {
    //       data: info
    //     });
    //   }
    // })1.18修改包月流程
    this.getRules()
  },
  reProcessing() {
    //再次办理
    // var info = this.data.info;
    // wx.navigateTo({
    //   url: '/pages/monthlyPackage/add/pay/index?parkId=' + info.parkId+"&isRenewal=true"+"&plateNo="+info.plateNo+"&certCode="+info.certCode,
    //   success: (res) => {//isRenewal=true点击再次办理的时候不能切换车牌
    //     res.eventChannel.emit('setData', {
    //       data: info
    //     });
    //   }
    // })1.18修改包月流程
    this.getRules()
  },
  getRules() {
    util.showLoading('正在加载…')
    var that = this
    util.request(api.getMonthRules, { certCode: that.data.certCode }, 'GET').then(function (res) {
      var rules = res.result
      if (res.code == '0') {
        that.showMonthlySubscriptionRules(rules)
        wx.hideLoading()
      } else {
        wx.hideLoading()
        util.showToast(res.message)
      }
    })
  },
  showMonthlySubscriptionRules(rules) {
    if (rules.length > 1) {
      this.setData({
        isShowRules: true,
        rules: rules,
      })
    } else {
      //只有一个规则直接跳转包月页面
      this.goMonthlyPage(rules[0])
    }
  },
  goMonthlyPage(rules) {
    var that = this

    wx.navigateTo({
      url: '/pages/monthlyPackage/add/payNew/index',
      success: res => {
        res.eventChannel.emit('setData', {
          data: rules,
          certCode: that.data.certCode,
          parkId: that.data.info.parkId,
          isRenewal: true, //表示是续费
        })
      },
    })
  },
  selectRule: function (e) {
    var detail = e.detail

    this.goMonthlyPage(this.data.rules[detail])
  },
  onShow() {
    util.showLoading('正在加载…')
    this.initData()
  },
  initData() {
    var that = this
    util
      .request(
        api.getMonthlyDetail,
        {
          bagMyself: that.data.bagMyself,
          certCode: that.data.certCode,
        },
        'GET'
      )
      .then(function (res) {
        var infos = res.result
        if (res.code == '0') {
          that.setData({
            info: infos,
          })
          wx.hideLoading()
        } else {
          wx.hideLoading()
          util.showToast(res.message)
        }
      })
  },
  goRefund() {
    var data = this.data
    var info = data.info
    util.showLoading('正在加载...')
    util
      .request(
        api.monthlyCancelPreview,
        {
          certCode: info.certCode,
        },
        'GET'
      )
      .then(function (res) {
        if (res.code == '0') {
          wx.hideLoading()
          wx.navigateTo({
            url: '/pages/monthlyPackage/details/refund/index?certCode=' + info.certCode,
          })
        } else {
          //说明这个订单不能取消包月，展示后台返回的取消包月失败的提示
          wx.hideLoading()
          wx.showModal({
            title: '提示',
            content: res.message,
            confirmText: '我知道了',
            confirmColor: '#000000',
            showCancel: false,
          })
        }
      })
  },
  back() {
    wx.navigateBack({
      delta: 2,
    })
  },
})
