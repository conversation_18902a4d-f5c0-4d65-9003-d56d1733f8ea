var util = require('../../../utils/util');
var api = require('../../../config/api.js');
Page({
  data: {
    id:"",
    info:{}
  },
  onLoad(options) {
    this.setData({
      id:options.id
    })
    this.getRecordData()
  },
  getRecordData: function () {
    var that = this;
    util.showLoading('正在加载…')
    util.request(api.getReservationInfo+"/"+that.data.id, "", 'GET').then(function (res) {
      wx.hideLoading();
      let {
        code,
        result
      } = res || {}
      if (code == '0') {
        that.setData({
          info:result
        })
      } else {
        util.showToast(res.message);
      }
    })

  }
})