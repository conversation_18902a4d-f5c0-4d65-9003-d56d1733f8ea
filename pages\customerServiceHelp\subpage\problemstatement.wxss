page {
  background: #F8F8F8;
  height: 100hv;
}

.box {
  margin: 20rpx;
  width: calc("100%-100rpx");
  background: #FFFFFF;
  box-shadow: 0rpx 10rpx 20rpx 0rpx rgba(228, 234, 248, 0.5);
  border-radius: 20rpx;
  box-sizing: border-box;
  padding: 0 23rpx;
}

.container .box {
  padding: 30rpx;
}

.box .header {
  width: 100%;
  height: 93rpx;
  border-bottom: 1rpx solid #DFDFDF;
  font-size: 34rpx;
  font-weight: bold;
  color: #353535;
  line-height: 80rpx;
}

.box .content {
  width: 100%;
  font-size: 26rpx;
  font-weight: 400;
  color: #7E7E7E;
  line-height: 54rpx;
}

.rowView {
  height: 90rpx;
  font-size: 34rpx;
  font-weight: 400;
  color: #353535;
  line-height: 90rpx;
}

.line {
  width: 100%;
  border-bottom: 1rpx solid #E5E5E5;
}

.text {
  text-align: center;
  width: 150rpx;
}

.text_value {
  flex: 1;
  text-align: right;
}

.title {
  min-width: 112rpx;
  height: 40rpx;
  font-size: 28rpx;
  font-family: PingFang SC-Bold, PingFang SC;
  font-weight: bold;
  color: #7E7E7E;
  line-height: 54rpx;
  margin: 20rpx auto 0rpx 20rpx;
}

.typeBox {
  height: 54rpx;
  background: #FFFFFF;
  border-radius: 27rpx 27rpx 27rpx 27rpx;
  opacity: 1;
  border: 1rpx solid #707070;
  font-size: 28rpx;
  font-weight: 400;
  color: #353535;
  line-height: 54rpx;
  margin: 20rpx 20rpx 0 0;
  padding: 0 20rpx;
}

.autowrapBox {
  padding-bottom: 20rpx;
  margin-bottom: 0;
}

.choose {
  color: #256BF5;
  border-color: #256BF5;
}

.box-input {
  width: 650rpx;
  height: 169rpx;
  font-size: 34rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #C0C0C0;
  line-height: 34rpx;
}

.ProblemConsultation .box {
  margin: 20rpx 20rpx 0rpx 20rpx;
}

.box-textarea {
  color: #353535;
  height: 169rpx;
  line-height: 54rpx;
  font-weight: 400;
  font-size: 34rpx;
  margin: 20rpx 0;
  width: 100%;
  border-bottom: 1rpx solid #DFDFDF;
}

.upBtnBox {
  width: 200rpx;
  height: 200rpx;
  background: #FFFFFF;
  opacity: 1;
  border: dashed 2rpx #DFDFDF;
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  line-height: 60rpx;
  color: #7E7E7E;
  font-size: 28rpx;
  font-weight: 400;
}

.icon-img {
  width: 40rpx;
  height: 40rpx;
}

.selectText {
  width: calc(100% - 30rpx);
  height: 80rpx;
  font-size: 28rpx;
  font-weight: 400;
  color: #353535;
  line-height: 80rpx;
}

.selectText-arrow {
  margin: 35rpx auto;
}

.selectText-arrow:after {
  content: " ";
  position: absolute;
  border-color: #c0c0c0;
  height: 15rpx;
  width: 15rpx;
  border-width: 4rpx 4rpx 0 0;
  border-style: solid;
  transform: matrix(0.71, 0.71, -0.71, 0.71, 0, 0);
  border-top-width: 2px;
  border-right-width: 2px;
  border-bottom-width: 0px;
  border-left-width: 0px;
  border-top-style: solid;
  border-right-style: solid;
  border-bottom-style: solid;
  border-left-style: solid;
}

.noClike {
  background: #C0C0C0;
}

.conventionView {
  min-height: 54rpx;
  font-size: 28rpx;
  font-weight: 400;
  color: #353535;
  line-height: 54rpx;
}

.box.padding {
  padding: 20rpx;
}

.img {
  width: 200rpx;
  height: 200rpx;
  border-radius: 10rpx;
}

.green {
  color: #41E0AC;
}

.blue {
  color: #256BF5;
}

.red {
  color: #FF2D2D;
}

.whImg {
  width: 200rpx;
  height: 200rpx;
  border-radius: 20rpx;
  margin-bottom: 20rpx;
}

.describe {
  min-width: 432rpx;
  min-height: 33rpx;
  font-size: 24rpx;
  font-weight: 400;
  color: #7E7E7E;
  line-height: 33rpx;
  margin-top: 20rpx;
}

/* 时间 */
.nowp {
  height: 90rpx;
  font-size: 34rpx;
  font-weight: 400;
  color: #353535;
  line-height: 90rpx;
}
.timeView{
  height: 90rpx;
}

.act_right {
  display: flex;
  justify-content: flex-end;
}

.timeView picker {
  flex: 1;
  margin-left: 40rpx;
}
.sel_text{
  flex:1;
  text-align: right;
  margin-right: 10rpx;
}
.convention_button{
  margin-bottom: 20rpx;
}