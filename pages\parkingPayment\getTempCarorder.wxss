@import 'index.wxss';
@import '../supplementaryPayment/index.wxss';

.nullTip {
  font-size: 30rpx;
  font-weight: 400;
  color: #a3a3a3;
}

.header {
  margin: 10rpx 0;
  width: 100%;
  height: 80rpx;
  background-color: #ffffff;
  display: flex;
  align-items: center;
}

.header .order-type {
  margin-left: 40rpx;
  width: 50%;
  height: 48rpx;
  font-size: 34rpx;
  font-weight: bold;
  color: #7e7e7e;
  line-height: 48rpx;
  text-align: center;
}

.header .order-type::after {
  content: '';
  display: block;
  margin-left: 40%;
  width: 60rpx;
  height: 8rpx;
  background: transparent;
  border-radius: 4rpx 4rpx 4rpx 4rpx;
}

.header .order-type.active {
  color: #000000;
}

.header .order-type.active::after {
  background: #41e0ac;
}

.header .invoice-state {
  margin-left: 34rpx;
  margin-right: 8rpx;
  height: 40rpx;
  font-size: 28rpx;
  color: #7e7e7e;
}

.header .invoice-state.active {
  color: #41e0ac;
}
.listBox {
  height: calc(100% - 275rpx);
}
.left {
  margin: auto 40rpx auto 45rpx;
  width: 32rpx;
  height: 100%;
  display: flex;
  align-items: center;
}

.left image {
  width: 32rpx;
  height: 32rpx;
}

.footer {
  width: 100%;
  height: 180rpx;
  background-color: #ffffff;
  box-sizing: border-box;
  font-size: 26rpx;
  font-weight: 400;
}

.footer .totalBox {
  flex: 1;
  font-size: 26rpx;
  font-weight: 400;
  color: #7e7e7e;
  margin: auto;
}

.footer .text {
  min-height: 40rpx;
  width: 100%;
  line-height: 40rpx;
}

.footer .checkbox {
  margin: auto 40rpx;
  width: 100rpx;
  height: 37rpx;
  font-size: 26rpx;
  color: #353535;
  display: flex;
  align-items: center;
}

.footer .checkbox image {
  margin-top: 2rpx;
  margin-right: 10rpx;
  width: 32rpx;
  height: 32rpx;
}

.footer .btn {
  width: 156rpx;
  height: 64rpx;
  background: linear-gradient(90deg, #458bff 0%, #08d7ae 100%);
  box-shadow: 0rpx 3rpx 10rpx 1rpx rgba(37, 107, 245, 0.2);
  border-radius: 40rpx;
  text-align: center;
  font-size: 30rpx;
  color: #ffffff;
  line-height: 64rpx;
  margin: auto 30rpx;
}
.box_convention_button {
  padding: 20rpx 0 0 0;
}
.cu-tag.badge {
  right: 58rpx;
  top: auto;
}
.cwxqzt {
  margin-top: 10rpx;
}
.btn-disabled {
  opacity: 0.6;
}
