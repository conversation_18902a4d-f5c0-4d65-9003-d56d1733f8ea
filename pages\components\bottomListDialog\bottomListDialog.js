Component({
  properties: {
    list: {
      type: Array,
      default: []
    },
    isShow: {
      type: Boolean,
      default: false
    },
    rangekey: {
      type: String,
      default: ""
    },
    value: {
      type: String,
      default: ""
    },
    disablekey: {
      type: String,
      default: ""
    },
    disableText: {
      type: String,
      default: ""
    }
  },
  data: {},
  methods: {
    //点击显示底部弹出
    changeRange: function () {
      this.showModal();
      console.log('我是弹窗打开----');
    },
    //底部弹出框
    showModal: function () {
      // 背景遮罩层
      var animation = wx.createAnimation({
        duration: 50,
        timingFunction: "linear",
        delay: 0
      })
      //this.animation = animation
      animation.translateY(50).step()
      this.setData({
        animationData: animation.export(),
        isShow: true
      })
      setTimeout(function () {
        animation.translateY(0).step()
        this.setData({
          animationData: animation.export()
        })
      }.bind(this), 50)
    },

    //点击背景面任意一处时，弹出框隐藏
    hideModal: function (e) {
      //弹出框消失动画
      var animation = wx.createAnimation({
        duration: 10,
        timingFunction: "linear",
        delay: 0
      })
      //this.animation = animation
      animation.translateY(10).step()
      this.setData({
        animationData: animation.export(),
      })
      setTimeout(function () {
        animation.translateY(0).step()
        this.setData({
          animationData: animation.export(),
          isShow: false
        })
      }.bind(this), 10)
    },

    // 选择选项-----弹出框选择添加类型
    getValueTap(e) {
      console.log(e);
      var info = e.currentTarget.dataset.info;
      this.triggerEvent("selectItem", info);
      this.hideModal();
    }
  },
  // 生命周期
  lifetimes: {
    ready: function () {

    },
  }
})