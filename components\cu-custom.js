var app = getApp()
Component({
  /**
   * 组件的一些选项
   */
  options: {
    multipleSlots: true,
  },
  /**
   * 组件的对外属性
   */
  properties: {
    contentTitle: {
      type: String,
      default: '',
    },
    bgColor: {
      type: String,
      default: '',
    },
    backgroundColor: {
      type: String,
      default: '',
    },
    isCustom: {
      type: Boolean,
      default: false,
    },
    isBack: {
      type: Boolean,
      default: false,
    },
    isCustomBack: {
      type: Boolean,
      default: false,
    },
    bgImage: {
      type: String,
      default: '',
    },
  },
  /**
   * 组件的初始数据
   */
  data: {
    StatusBar: app.globalData.StatusBar,
    CustomBar: app.globalData.CustomBar,
    Custom: app.globalData.Custom,
  },
  /**
   * 组件的方法列表
   */
  methods: {
    BackPage() {
      if (this.data.isCustomBack) {
        this.triggerEvent('back')
      } else {
        wx.navigateBack({
          delta: 1,
          success: () => {
            this.triggerEvent('back')
          },
          fail() {
            wx.reLaunch({
              url: '/pages/index/index',
            })
          },
        })
      }
    },
    toHome() {
      wx.reLaunch({
        url: '/pages/index/index',
      })
    },
  },
})
