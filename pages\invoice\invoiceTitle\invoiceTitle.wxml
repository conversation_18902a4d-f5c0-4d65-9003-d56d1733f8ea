<view class="saveOutView">
  <cu-custom bgColor="white-bg" contentTitle="开具发票" isBack="true"></cu-custom>
  <!-- 表单内容 -->
  <scroll-view class="scrollViewSa" scroll-y="true">
    <form catchsubmit="formSubmit">
      <view class="section-title">发票详情</view>
      <view class="section-content">
        <view class="form-row">
          <view class="form-label">抬头类型</view>
          <view class="form-value">
            <view class="checkbox" bindtap="selectedTitleType" data-type="1">
              <image src="/image/{{invoiceTitleType == '1' ? 'yes-icon-select' : 'yes-icon'}}.png" />企业单位
            </view>
            <view class="checkbox" bindtap="selectedTitleType" data-type="2">
              <image src="/image/{{invoiceTitleType == '2' ? 'yes-icon-select' : 'yes-icon'}}.png" />个人/非企业单位
            </view>
          </view>
        </view>
        <view class="form-row">
          <view class="form-label required">{{invoiceTitleType == '1'?'公司名称':'抬头名称'}}</view>
          <view class="form-value">
            <input id="invoiceTitle" name="invoiceTitle" class="input" type="text" placeholder="请填写{{invoiceTitleType == '1'?'公司名称':'抬头名称'}}"  maxlength='50'/>
          </view>
        </view>
        <view class="form-row" wx:if="{{invoiceTitleType == '1'}}">
          <view class="form-label required">公司税号</view>
          <view class="form-value">
            <input id="taxpayerId" name="taxpayerId" class="input" type="text" placeholder="请填写公司税号" maxlength='20'/>
          </view>
        </view>
        <view class="form-row" wx:if="{{invoiceTitleType == '1'}}">
          <view class="form-label">注册地址</view>
          <view class="form-value">
            <input name="taxpayerAddr" class="input" type="text" placeholder="请填写公司注册地址"  maxlength='50'/>
          </view>
        </view>
        <view class="form-row" wx:if="{{invoiceTitleType == '1'}}">
          <view class="form-label">开户银行</view>
          <view class="form-value">
            <input name="taxpayerBank" class="input" type="text" placeholder="请填写公司开户银行"  maxlength='40'/>
          </view>
        </view>
        <view class="form-row" wx:if="{{invoiceTitleType == '1'}}">
          <view class="form-label">银行账号</view>
          <view class="form-value">
            <input name="taxpayerAccount" class="input" type="text" placeholder="请填写银行账号"  maxlength='30'/>
          </view>
        </view>
        <view class="form-row">
          <view class="form-label">备注</view>
          <view class="form-value">
            <input name="remark" class="input" type="text" placeholder="请输入备注内容" />
          </view>
        </view>
      </view>
      <view class="section-content">
        <view class="form-row">
          <view class="form-label">总金额</view>
          <view class="form-value">
            <view class="amount">￥{{amount}}</view>
            <view class="next" bindtap="gotoDetail">共{{count}}张，查看详情</view>
          </view>
        </view>
      </view>
      <view class="section-title">接收方式</view>
      <view class="section-content">
        <view class="form-row">
          <view class="form-label required">电子邮箱</view>
          <view class="form-value">
            <input id="email" name="email" class="input" placeholder="请输入电子邮箱"  maxlength='50'/>
          </view>
        </view>
        <view class='rowView xy'>
          <image class='yes_icon' wx:if="{{!allowXY}}" src="../../../image/yes-icon.png" bindtap='allow'></image>
          <image class='yes_icon' wx:if="{{allowXY}}" src="../../../image/yes-icon-select.png" bindtap='allow'></image>
          <text class='text'>我同意桂林盛才人力科技有限公司使用我所提交的信息用于开具发票服务。<text bindtap="viewAgreement" id='serviceUrl' style="color:rgba(37, 107, 245, 1)">《服务协议》</text>及<text bindtap="viewAgreement" id='privacyUrl' style="color:rgba(37, 107, 245, 1)">《软件注册及隐私政策协议》</text></text>
        </view>
        <button class="convention_button" formType="submit">提交</button>
      </view>
      <!-- 预览模式 -->
      <view class="preview-box" hidden="{{!isShowPreviewBox}}" bindtap="closePreviewBox">
        <view class="container" catchtap="placeholderFun">
          <view class="header">
            开具电子发票
            <image src="/image/close.png" bindtap="closePreviewBox" class="close" />
          </view>
          <view class="content">
            <view class="section-content">
              <view class="form-row">
                <view class="form-label required">{{invoiceTitleType == '1'?'公司名称':'抬头名称'}}</view>
                <view class="form-value">
                  <text class="text">{{param.invoiceTitle}}</text>
                </view>
              </view>
              <view class="form-row" hidden="{{invoiceTitleType=='2'}}">
                <view class="form-label required">公司税号</view>
                <view class="form-value">
                  <text class="text">{{param.taxpayerId}}</text>
                </view>
              </view>
              <view class="form-row">
                <view class="form-label required">电子邮箱</view>
                <view class="form-value">
                  <text class="text">{{param.email}}</text>
                </view>
              </view>
              <view class="form-row">
                <view class="form-label">发票类型</view>
                <view class="form-value">
                  <text class="text">电子发票</text>
                </view>
              </view>
            </view>
            <view class="tip">请确认电子邮箱号无误，电子发票将在系统开具后发送至该邮箱，请注意查收</view>
            <view class="form-control">
              <view class="btn" bindtap="onSubmit">确认提交</view>
            </view>
          </view>
        </view>
      </view>
    </form>
  </scroll-view>

</view>