// pages/parkingSpaceInquiry/info/index.js
var util = require('../../../utils/util')
var api = require('../../../config/api.js')
const app = getApp()
Page({
  data: {
    rules: [], //包月规则
    isShowRules: false, //展示包月规则弹窗
    goPageType: '', //跳转页面的类型
    showPlateNoBox: false, //显示车牌选择
    selectPlate: 0,
    plateNos: [], //车牌列表
    goPageRoute: '', //保存弹出登录弹窗之前用户需要跳转的页面
    id: 1,
    listDivHeight: 0,
    latitude: app.globalData.latitude,
    longitude: app.globalData.longitude,
    imgurl: api.imgUrl + '/ccxqmn.png',
    info: {},
  },
  onLoad: function (options) {
    this.setData({
      id: options.id,
      latitude: options.lat,
      longitude: options.long,
      resourceType: options.resourceType,
      listDivHeight: app.globalData.windowHeight,
    })
  },
  onShow() {
    var data = this.data
    this.initData(data.id, data.latitude, data.longitude, data.resourceType)
  },
  initData: function (id, latitude, longitude, type) {
    var that = this
    console.log('onShow-到达详情页ID：' + id)
    util.showLoading('正在加载…')
    util
      .request(
        api.getParkingInfo,
        {
          resourceType: type,
          latitude: latitude,
          longitude: longitude,
          id: id,
        },
        'GET'
      )
      .then(function (res) {
        var infos = res.result
        if (res.code == '0') {
          that.setData({
            info: infos,
          })
          wx.hideLoading()
        } else {
          wx.hideLoading()
          util.showToast(res.message)
        }
      })
  },
  goPage: function (e) {
    var that = this
    var dataset = e.currentTarget.dataset
    var type = dataset.type
    that.setData({
      goPageType: type,
    })
    if (app.globalData.userInfo.loginStatus != 'LOGGED_IN') {
      //未登录用户
      wx.navigateTo({
        url: '/pages/common/loginTip/loginTip?tiptype=0',
      })
    } else {
      util.showLoading('正在加载…')
      that.jumpProcess()
    }
  },
  jumpProcess: function () {
    var that = this
    var infos = that.data.info
    var parkId = infos.parkId
    var url = ''
    var type = that.data.goPageType
    if (type == 'buy') {
      //购买包月
      url = '/pages/monthlyPackage/add/pay/index?parkId=' + parkId
    } else if (type === 'vehicleReservation') {
      url = '/pages/vehicleReservation/add/index'
    }
    util.request(api.getUserStatus, '', 'GET').then(function (res) {
      var info = res.result
      if (res.code == '0') {
        wx.hideLoading()
        if (info.userStatus != 'REGISTERED_BOUND') {
          //未绑定车牌
          wx.showModal({
            title: '提示',
            content: '请添加车牌号',
            confirmText: '去添加',
            success(res) {
              if (res.confirm) {
                wx.navigateTo({
                  url: '/pages/my/myCar/index',
                })
              } else if (res.cancel) {
                console.log('用户点击取消')
              }
            },
          })
        } else {
          util.showLoading('正在加载…')
          if (type == 'buy') {
            //包月流程已更改1.18
            util
              .request(
                api.checkMonth,
                {
                  parkId: parkId,
                },
                'GET'
              )
              .then(function (res) {
                //检测是否有车牌可以办理包月
                var info = res.result
                if (res.code == '0') {
                  wx.hideLoading()
                  if (info.showPopup == 0) {
                    //是否显示弹窗,0表示不显示，直接展示规则列表，1表示显示
                    that.showMonthlySubscriptionRules(info.rules, parkId)
                  } else {
                    if (info.showOtherVehicleBagBtn == 1 && info.showRenewBtn == 1) {
                      //是否显示【其他车辆办理】按钮,0表示不显示，1表示显示//是否显示【去续费】按钮,,0表示不显示，1表示显示
                      wx.showModal({
                        title: '包月提醒',
                        content: info.message,
                        confirmText: '其他车辆',
                        cancelText: '去续费',
                        confirmColor: '#000000',
                        showCancel: true,
                        success(res) {
                          if (res.confirm) {
                            //显示包月规则列表
                            that.showMonthlySubscriptionRules(info.rules, parkId)
                          } else if (res.cancel) {
                            //去续费：跳转到我的月卡列表
                            that.toMyMonthlyList()
                          }
                        },
                      })
                    } else if (info.showOtherVehicleBagBtn == 0 && info.showRenewBtn == 1) {
                      wx.showModal({
                        title: '包月提醒',
                        content: info.message,
                        confirmText: '去续费',
                        showCancel: false,
                        confirmColor: '#000000',
                        success(res) {
                          if (res.confirm) {
                            //去续费：跳转到我的月卡列表
                            that.toMyMonthlyList()
                          }
                        },
                      })
                    } else if (info.showOtherVehicleBagBtn == 1 && info.showRenewBtn == 0) {
                      wx.showModal({
                        title: '包月提醒',
                        content: info.message,
                        confirmText: '其他车辆',
                        showCancel: false,
                        confirmColor: '#000000',
                        success(res) {
                          if (res.confirm) {
                            that.showMonthlySubscriptionRules(info.rules, parkId)
                          }
                        },
                      })
                    }
                  }
                } else {
                  wx.hideLoading()
                  util.showToast(res.message)
                }
              })
          }
          // else if (dataset.type = 'vehicleReservation') { //预约的功能已经去掉屏蔽了，不可用
          //   that.toPageL(url);
          // }
        }
      } else {
        wx.hideLoading()
        util.showToast(res.message)
      }
    })
  },
  openMapApp: function (e) {
    let that = this
    var info = that.data.info
    var latitude = info.parkLatitude
    var longitude = info.parkLongitude
    var name = info.parkName
    var address = info.parkingAddress
    util.openMapApp(latitude, longitude, app.globalData.mapScale, name, address)
  },
  goNextPage: function () {
    this.toPageL(this.data.goPageRoute)
  },
  toPageL: function (url) {
    var info = this.data.info
    wx.navigateTo({
      url: url,
      success: res => {
        res.eventChannel.emit('setData', {
          data: info,
        })
      },
    })
  },
  selectItem: function (e) {
    var that = this
    var info = that.data.info
    var detail = e.detail
    that.toPageL(
      '/pages/monthlyPackage/add/pay/index?parkId=' + info.parkId + '&plateNo=' + detail.plateNo + '&isRenewal=false'
    )
  },
  refresh() {
    this.jumpProcess()
  },
  goPageMethod() {
    //登陆后跳转到这个方法
    wx.navigateBack()
    this.jumpProcess()
  },
  showMonthlySubscriptionRules(rules, parkId) {
    if (rules.length > 1) {
      this.setData({
        isShowRules: true,
        rules: rules,
      })
    } else {
      //只有一个规则直接跳转包月页面
      this.goMonthlyPage(parkId, rules[0])
    }
  },
  toMyMonthlyList: function () {
    //跳转去我的月卡
    wx.navigateTo({
      url: '/pages/monthlyPackage/index',
    })
  },
  selectRule: function (e) {
    var detail = e.detail
    this.goMonthlyPage(this.data.info.parkId, this.data.rules[detail])
  },
  goMonthlyPage(parkId, info) {
    wx.navigateTo({
      url: '/pages/monthlyPackage/add/payNew/index?parkId=' + parkId,
      success: res => {
        res.eventChannel.emit('setData', {
          data: info,
          parkId: parkId,
          isRenewal: false,
        })
      },
    })
  },
})
