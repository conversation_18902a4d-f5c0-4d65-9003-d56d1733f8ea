page {
  height: 100%;
  width: 100%;
}

.full-page {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
}

.backgroundView {
  flex: 1;
  height: 0;
  width: 100%;
  background-size: 100%;
  background-repeat: no-repeat;
  background-position-y: 40rpx;
}

.topTitle {
  width: 80%;
  min-height: 67rpx;
  font-size: 48rpx;

  font-weight: 400;
  color: #ffffff;
  line-height: 67rpx;
  margin-top: 79rpx;
  margin-left: 87rpx;
}

.topTitle-fu {
  width: 80%;
  min-height: 40rpx;
  font-size: 26rpx;

  font-weight: 400;
  color: #ffffff;
  line-height: 40rpx;
  margin-top: 18rpx;
  margin-left: 87rpx;
}

.bottomTitle {
  width: 80%;
  min-height: 50rpx;
  font-size: 48rpx;

  font-weight: 400;
  color: #da5937;
  margin-top: 180rpx;
  margin-left: 87rpx;
}

.bottomDetails-container {
  flex: 1;
  height: 0;
  margin-top: 31rpx;
  margin-left: 87rpx;
  margin-bottom: 20rpx;
  width: 80%;
  overflow: hidden;
}

.bottomDetails {
  height: 100%;
  font-size: 24rpx;
  font-weight: 400;
  color: #606266;
  line-height: 32rpx;
  box-sizing: border-box;
}

.bottomDetails-item {
  margin-bottom: 14rpx;
}

.bottomDetails-item:last-of-type {
  margin-bottom: 0;
}

.add {
  width: calc(100% - 40rpx) !important;
  height: 96rpx;
  background: #4768f3;
  border-radius: 10rpx 10rpx 10rpx 10rpx;
  position: absolute;
  bottom: 45rpx;
  left: 20rpx;
  font-size: 36rpx;
  font-weight: 400;
  color: #ffffff;
  line-height: 60rpx;
}

.text-bold {
  font-weight: bold;
}

.footer-container {
  padding-bottom: calc(constant(safe-area-inset-bottom) + 20rpx);
  padding-bottom: calc(env(safe-area-inset-bottom) + 20rpx);
}
