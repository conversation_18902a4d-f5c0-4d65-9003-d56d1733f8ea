const app = getApp()
var util = require('../../../../../utils/util');
var api = require('../../../../../config/api.js');
Page({
  data: {
    pageHeight: 1000,
  },
  onLoad(options) {
    let height = app.globalData.windowHeight - app.globalData.CustomBar + 48; // 48是tabbar的高度
    console.log('windowHeight', app.globalData.windowHeight)
    console.log('CustomBar', app.globalData.CustomBar)
    this.setData({
      pageHeight: height
    })
  },
  goNext(){
    wx.navigateTo({
      url: '/pages/my/set/logoff/logoffConfirm/verification/index',
    })
  },
  cancel(){
    wx.navigateBack({
      delta: 3
    })
  }
})