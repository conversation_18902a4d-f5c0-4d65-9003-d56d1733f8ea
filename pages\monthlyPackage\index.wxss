@import '../common/common.wxss';
@import '../components/common.wxss';

page {
  background: #f6f7fb;
}

.backgroundView {
  background-size: 100%;
  background-repeat: no-repeat;
  background-position-y: 40rpx;
}

.topTitle {
  width: 80%;
  height: 67rpx;
  font-size: 48rpx;
  font-weight: 400;
  color: #ffffff;
  line-height: 67rpx;
  padding-left: 84rpx;
  padding-top: 79rpx;
}

.topTitle-fu {
  width: calc(100% - 454rpx);
  height: 40rpx;
  font-size: 28rpx;
  font-weight: 400;
  color: #ffffff;
  line-height: 40rpx;
  padding-left: 150rpx;
  padding-top: 18rpx;
}

.add {
  line-height: 64rpx;
  padding: 0 10rpx !important;
  width: 200rpx !important;
  height: 64rpx;
  background: linear-gradient(90deg, #458bff 0%, #08d7ae 100%);
  border-radius: 100rpx 100rpx 100rpx 100rpx;
  font-size: 34rpx;
  font-weight: 400;
  color: #ffffff;
}

.carListInfoBox {
  width: calc(100% - 80rpx);
  min-height: 186rpx;
  background: #ffffff;
  border-bottom: 1rpx solid #f6f6f6;
  padding: 30rpx 41rpx;
}

.carListInfoBox-r {
  width: calc(100% - 80rpx);
  min-height: 186rpx;
}

.carListInfoBox text {
  margin-bottom: 10rpx;
}

.carListInfo-one {
  width: 100%;
  min-height: 50rpx;
  font-size: 34rpx;
  font-weight: bold;
  color: #353535;
  line-height: 50rpx;
}

.carListInfo-two {
  width: 100%;
  height: 40rpx;
  font-size: 28rpx;
  font-weight: 400;
  color: #353535;
  line-height: 40rpx;
}

/* 月卡状态,包期状态 1-生效中，2-取消中(正在退款)，3-已退款已取消，4-已过期，5-申请审核中 ， 6- 审核通过，待支付，7-审核被拒绝；当支付后办理未成功，处于退款true */
.stateBox {
  background: rgba(126, 126, 126, 0.2);
  width: 100rpx;
  max-width: 200rpx;
  height: 40rpx;
  line-height: 40rpx;
  text-align: center;
  font-size: 26rpx;
  font-weight: 400;
  color: #7e7e7e;
  border-radius: 5rpx 5rpx 5rpx 5rpx;
  margin-right: 10rpx;
}

.stateBox.w {
  width: 150rpx;
}

.stateBox.a1 {
  color: #24bb8a;
  background: #d9f9ee;
}

.stateBox.a2 {
  color: #da5937;
  background: #f8ded7;
}

.nullTip {
  width: 100%;
  height: 50rpx;
  font-size: 36rpx;
  font-weight: 400;
  color: #888888;
  text-align: center;
  margin: 102rpx 0;
}

.circle-i-orange {
  width: 190rpx;
  height: 40rpx;
  margin-top: 37rpx;
  margin-left: 520rpx;
}

.btn {
  height: 56rpx;
  background: rgba(37, 107, 245, 0.1);
  border: 1rpx solid rgba(37, 107, 245, 0.2);
  font-size: 26rpx;
  color: #256bf5;
  line-height: 56rpx;
  margin-left: 0;
  width: 100rpx;
  box-shadow: 0rpx 10rpx 0rpx 1rpx rgba(228, 234, 248, 0.16);
  border-radius: 100rpx 100rpx 100rpx 100rpx;
  text-align: center;
}

.btn.f {
  margin-left: auto;
}

.btn.w {
  width: 156rpx;
  margin-right: 10rpx;
  background: linear-gradient(90deg, #ff5745 0%, #ffae00 100%);
  border: none;
  color: #ffffff;
}

.custom-footer {
  width: 100%;
}

.custom-btn {
  width: 100% !important;
  color: #4768f3;
  padding: 20rpx 0;
}

.custom-content {
  font-size: 28rpx;
  font-weight: 400;
  color: #606266;
  line-height: 32rpx;
}

.bottomDetails-item {
  margin-bottom: 14rpx;
}

.bottomDetails-item:last-of-type {
  margin-bottom: 0;
}

.text-bold {
  font-weight: bold;
}
