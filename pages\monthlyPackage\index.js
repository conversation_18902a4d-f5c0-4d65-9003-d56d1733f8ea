// pages/monthlyPackage/add/index.js
var api = require('../../config/api.js')
var util = require('../../utils/util')
Page({
  data: {
    clickInfo: {}, //点击的信息
    rules: [], //包月规则
    isShowRules: false, //展示包月规则弹窗
    showModal: false,
    imgUrl: api.imgUrl,
    cardNum: 0,
    list: [],
    currentPage: 1,
    pageSize: 20,
    isShowSlotModal: false,
  },
  onShow() {
    util.showLoading('正在加载…')
    this.setData({
      list: [],
      currentPage: 1,
      total: 0,
    })
    this.getList()
  },
  getList: function () {
    var that = this
    var data = that.data
    var currentPage = data.currentPage
    var pageSize = data.pageSize
    util
      .request(
        api.payMonthlyRecordList,
        {
          effectiveStatus: 'ALL',
          currentPage: currentPage,
          pageSize: pageSize,
        },
        'GET'
      )
      .then(function (res) {
        var result = res.result
        if (res.code == '0') {
          var total = result.total
          var list = result.records
          if (total == 0) {
            //没有数据
            that.setData({
              list: [],
              cardNum: total,
            })
          } else {
            if (total <= pageSize || list.length == 0) {
              //没有更多数据
              that.setData({
                list: that.data.list.concat(list),
                showNullTip: false,
                showNullMoreTip: true,
                cardNum: total,
              })
            } else {
              that.setData({
                list: that.data.list.concat(list),
                showNullTip: false,
                showNullMoreTip: false,
                cardNum: total,
              })
            }
          }
          //加载下一页
          that.setData({
            currentPage: currentPage + 1,
          })
          wx.hideLoading()
        } else {
          wx.hideLoading()
          util.showToast(res.message)
        }
      })
  },
  addCard(e) {
    wx.navigateTo({
      url: '/pages/monthlyPackage/add/find/index?isFind=true',
    })
  },
  goXq(e) {
    var dataset = e.currentTarget.dataset
    var bagMyself = dataset.b
    var certCode = dataset.c
    wx.navigateTo({
      url: '/pages/monthlyPackage/details/index?bagMyself=' + bagMyself + '&&certCode=' + certCode,
    })
  },
  goAddPage(e) {
    var dataset = e.currentTarget.dataset
    var info = dataset.info
    // wx.navigateTo({
    //   url: '/pages/monthlyPackage/add/pay/index?parkId=' + info.parkId + "&plateNo=" + info.plateNo + "&isRenewal=" + dataset.isr + "&certCode=" + info.certCode + "&isRenewal=true",
    //   success: (res) => { //isRenewal=true点击立即续费、再次办理的时候不能切换车牌
    //     res.eventChannel.emit('setData', {
    //       data: info
    //     });
    //   }
    // })修改包月流程1.18
    util.showLoading('正在加载…')
    var that = this
    util.request(api.getMonthRules, { certCode: info.certCode }, 'GET').then(function (res) {
      var rules = res.result
      if (res.code == '0') {
        that.showMonthlySubscriptionRules(rules, info)
        wx.hideLoading()
      } else {
        wx.hideLoading()
        util.showToast(res.message)
      }
    })
  },
  showMonthlySubscriptionRules(rules, info) {
    if (rules.length > 1) {
      this.setData({
        isShowRules: true,
        rules: rules,
        clickInfo: info,
      })
    } else {
      //只有一个规则直接跳转包月页面
      this.goMonthlyPage(rules[0], info)
    }
  },
  goMonthlyPage(rules, info) {
    var that = this

    wx.navigateTo({
      url: '/pages/monthlyPackage/add/payNew/index',
      success: res => {
        res.eventChannel.emit('setData', {
          data: rules,
          certCode: info.certCode,
          parkId: info.parkId,
          isRenewal: true, //表示是续费
        })
      },
    })
  },
  showApplication(e) {
    this.setData({
      isShowSlotModal: true,
    })
  },
  closeModal(e) {
    this.setData({
      isShowSlotModal: false,
    })
  },
  selectRule: function (e) {
    var detail = e.detail

    this.goMonthlyPage(this.data.rules[detail], this.data.clickInfo)
  },
})
