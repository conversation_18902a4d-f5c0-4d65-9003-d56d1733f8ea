@import '../components/recordDetail.wxss';
@import '../common/common.wxss';
page{
  background: #F6F7FB;
}
.saveOutView{
  display: flex;
  flex-direction: column;
  height: calc(100vh - 40rpx) ;
  overflow: hidden;
}

.titleTab{
  width: 100%;
  display: flex;
  flex-direction: row;
  justify-content: center;
}

.titleTab .tab{
  margin: 28rpx 30rpx;
  width: 102rpx;
  height: 48rpx;
  font-size: 34rpx;
  font-weight: bold;
  color: #7E7E7E;
  line-height: 48rpx;
}
.titleTab .tab::after{
  content: '';
  display: block;
  margin-left: 21rpx;
  width: 60rpx;
  height: 8rpx;
  background: transparent;
  border-radius: 4rpx 4rpx 4rpx 4rpx;

}
.titleTab .tab.select{
  color: #000000;
}
.titleTab .tab.select::after{
  background: #41E0AC;
}