var api = require('../../../../config/api.js')
var util = require('../../../../utils/util')
var app = getApp()
Page({
  data: {
    parkId: '', //选择的停车场ID
    rules: [], //包月规则
    isShowRules: false, //展示包月规则弹窗
    isFind: false, //从我的月卡页面进入的true，从服务页面进入的是false
    latitude: app.globalData.latitude,
    longitude: app.globalData.longitude,
    CustomBar: app.globalData.CustomBar,
    keyword: '', //搜索关键字
    onSearch: false,
    currentPage: 1,
    showNullMoreTip: false,
    list: [],
    showNullTip: false,
    xmorqwtjrxm: '', //搜索的关键字
    onRefresh: false,
    pageSize: 20, //分页大小
    inputShowed: false,
    inputVal: '',
  },
  onLoad: function (options) {
    this.setData({
      isFind: JSON.parse(options.isFind),
    })
  },
  onShow: function () {
    var that = this
    wx.getLocation({
      //获取当前位置
      type: 'gcj02',
      success(res) {
        console.log(res)
        var latitude = res.latitude
        var longitude = res.longitude
        that.setData({
          latitude: latitude,
          longitude: longitude,
        })
        that.getList() //10.31增加需要默认搜索
      },
      fail(res) {
        that.getList()
      },
    })
  },
  /**
   * 进入搜索
   */
  keywordfocus: function (e) {
    this.setData({
      onSearch: true,
      currentPage: 1,
    })
  },
  /**
   * 退出搜索
   */
  offSearch: function () {
    this.setData({
      keyword: '',
      onSearch: false,
      currentPage: 1,
      list: [],
      showNullTip: false,
      showNullMoreTip: false,
    })
    this.getList() //需要恢复到刚进页面时的状态，即初始状态
  },
  /**
   * 获取搜索关键字
   */
  getKeyword(e) {
    let that = this
    let keyword = e.detail.value
    console.log(keyword)
    that.setData({
      keyword: keyword,
      currentPage: 1,
      pageSize: 20,
    })
  },
  /**
   * 手机键盘确定搜索键
   */
  bindconfirm: function () {
    this.setData({
      currentPage: 1,
      showNullMoreTip: false,
      list: [],
      showNullTip: false,
    })
    this.getList()
  },
  getList: function () {
    var that = this
    var keyword = that.data.keyword
    keyword = keyword.replace(/\s*/g, '')
    util.showLoading('正在加载…')
    var currentPage = that.data.currentPage
    util
      .request(
        api.searchMonthParkingPoi,
        {
          latitude: that.data.latitude,
          longitude: that.data.longitude,
          keyword: keyword,
          currentPage: that.data.currentPage,
        },
        'GET'
      )
      .then(function (res) {
        if (res.code == '0') {
          wx.hideLoading()
          if (res.result.total == 0) {
            if (currentPage == 1) {
              //没有数据
              that.setData({
                list: [],
                showNullTip: true,
                showNullMoreTip: false,
              })
            } else {
              //没有更多数据
              that.setData({
                showNullMoreTip: true,
                showNullTip: false,
              })
            }
          } else {
            var records = res.result.records
            that.setData({
              list: that.data.list.concat(records),
              showNullTip: false,
              showNullMoreTip: false,
            })
            if (res.result.pages == currentPage) {
              //没有数据
              that.setData({
                showNullMoreTip: true,
              })
            }
          }
          //加载下一页
          that.setData({
            currentPage: currentPage + 1,
          })
        } else {
          wx.hideLoading()
          util.showToast(res.message)
        }
      })
  },
  openMapApp: function (e) {
    var dataset = e.currentTarget.dataset
    var latitude = dataset.lat
    var longitude = dataset.long
    var name = dataset.name
    var address = dataset.address
    util.openMapApp(latitude, longitude, app.globalData.mapScale, name, address)
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {
    this.setData({
      pageSize: 20,
      currentPage: 1,
      keyword: '',
      list: [],
    })
    this.getList()
    wx.stopPullDownRefresh()
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {
    if (this.data.noMoreData) {
      util.showToast('没有更多数据了')
    } else {
      this.getList()
    }
  },
  goxq: function (e) {
    var id = e.currentTarget.id
    this.setData({
      parkId: id,
    })
    // console.log('包月车场准备跳转到详情页ID：' + id)
    // wx.navigateTo({
    //   url: "/pages/parkingSpaceInquiry/info/index?id=" + id + "&lat=" + this.data.latitude + "&long=" + this.data.longitude + "&resourceType=PARKING_LOT"
    // })

    //1.18修改包月流程：先检查是否可以包月。在去查包月规则。选择规则后再跳转到支付。
    if (app.globalData.userInfo.loginStatus != 'LOGGED_IN') {
      //未登录用户
      wx.navigateTo({
        url: '/pages/common/loginTip/loginTip?tiptype=0',
      })
    } else {
      util.showLoading('正在加载…')
      this.jumpProcess()
    }
  },
  jumpProcess() {
    var that = this
    var id = that.data.parkId
    util.request(api.getUserStatus, '', 'GET').then(function (res) {
      var info = res.result
      if (res.code == '0') {
        wx.hideLoading()
        if (info.userStatus != 'REGISTERED_BOUND') {
          //未绑定车牌
          wx.showModal({
            title: '提示',
            content: '请添加车牌号',
            confirmText: '去添加',
            success(res) {
              if (res.confirm) {
                wx.navigateTo({
                  url: '/pages/my/myCar/index',
                })
              } else if (res.cancel) {
                console.log('用户点击取消')
              }
            },
          })
        } else {
          util
            .request(
              api.checkMonth,
              {
                parkId: id,
              },
              'GET'
            )
            .then(function (res) {
              //检测是否有车牌可以办理包月
              var info = res.result
              if (res.code == '0') {
                wx.hideLoading()
                if (info.showPopup == 0) {
                  //是否显示弹窗,0表示不显示，直接展示规则列表，1表示显示
                  that.showMonthlySubscriptionRules(info.rules, id)
                } else {
                  if (info.showOtherVehicleBagBtn == 1 && info.showRenewBtn == 1) {
                    //是否显示【其他车辆办理】按钮,0表示不显示，1表示显示//是否显示【去续费】按钮,,0表示不显示，1表示显示
                    wx.showModal({
                      title: '包月提醒',
                      content: info.message,
                      confirmText: '其他车辆',
                      cancelText: '去续费',
                      confirmColor: '#000000',
                      showCancel: true,
                      success(res) {
                        if (res.confirm) {
                          //显示包月规则列表
                          that.showMonthlySubscriptionRules(info.rules, id)
                        } else if (res.cancel) {
                          //去续费：跳转到我的月卡列表
                          that.toMyMonthlyList()
                        }
                      },
                    })
                  } else if (info.showOtherVehicleBagBtn == 0 && info.showRenewBtn == 1) {
                    wx.showModal({
                      title: '包月提醒',
                      content: info.message,
                      confirmText: '去续费',
                      showCancel: false,
                      confirmColor: '#000000',
                      success(res) {
                        if (res.confirm) {
                          //去续费：跳转到我的月卡列表
                          that.toMyMonthlyList()
                        }
                      },
                    })
                  } else if (info.showOtherVehicleBagBtn == 1 && info.showRenewBtn == 0) {
                    wx.showModal({
                      title: '包月提醒',
                      content: info.message,
                      confirmText: '其他车辆',
                      showCancel: false,
                      confirmColor: '#000000',
                      success(res) {
                        if (res.confirm) {
                          that.showMonthlySubscriptionRules(info.rules, id)
                        }
                      },
                    })
                  }
                }
              } else {
                wx.hideLoading()
                util.showToast(res.message)
              }
            })
        }
      }
    })
  },
  showMonthlySubscriptionRules(rules, parkId) {
    if (rules.length > 1) {
      this.setData({
        isShowRules: true,
        rules: rules,
      })
    } else {
      //只有一个规则直接跳转包月页面
      this.goMonthlyPage(parkId, rules[0])
    }
  },
  toMyMonthlyList: function () {
    //跳转去我的月卡
    wx.navigateTo({
      url: '/pages/monthlyPackage/index',
    })
  },
  selectRule: function (e) {
    var detail = e.detail

    this.goMonthlyPage(this.data.parkId, this.data.rules[detail])
  },
  goMonthlyPage(parkId, info) {
    wx.navigateTo({
      url: '/pages/monthlyPackage/add/payNew/index?parkId=' + parkId,
      success: res => {
        res.eventChannel.emit('setData', {
          data: info,
          parkId: parkId,
          isRenewal: false,
        })
      },
    })
  },
  goPageMethod() {
    //登陆后跳转到这个方法
    wx.navigateBack()
    this.jumpProcess()
  },
})
