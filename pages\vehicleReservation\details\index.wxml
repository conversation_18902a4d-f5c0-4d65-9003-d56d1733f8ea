<view class="saveOutView">
  <cu-custom bgColor="white-bg" contentTitle="预约订单" isBack="true"></cu-custom>
  <view class="infoBox">
    <view class="infoBox-b rowView">
      <view class="infoBox-b-l">预约车场名称:</view>
      <view class="infoBox-b-r">{{info.parkName}}</view>
    </view>
    <view class="infoBox-b rowView ">
      <view class="infoBox-b-l">预约车牌号码:</view>
      <view class="infoBox-b-r">{{info.plateNo}}</view>
    </view>
    <view class="line"></view>
    <view class="infoBox-b rowView">
      <view class="infoBox-b-l">预约状态:</view>
      <view class="infoBox-b-r">{{info.reservationStateDesc}}</view>
    </view>
    <view class="infoBox-b rowView">
      <view class="infoBox-b-l">预约时间: </view>
      <view class="infoBox-b-r">{{info.reservationTime}}</view>
    </view>
    <view class="infoBox-b rowView" wx:if="{{info.reservationState == 'CANCELED'}}">
      <view class="infoBox-b-l">取消时间: </view>
      <view class="infoBox-b-r">{{info.cancelTime}}</view>
    </view>
    <view class="line"></view>
    <view class="infoBox-b rowView">
      <view class="infoBox-b-l">预约金额: </view>
      <view class="infoBox-b-r">¥{{info.depositAmount}}</view>
    </view>
    <view class="infoBox-b rowView">
      <view class="infoBox-b-l">支付方式: </view>
      <view class="infoBox-b-r">{{info.payTypeDesc}}</view>
    </view>
    <view class="infoBox-b rowView">
      <view class="infoBox-b-l">入场截止时间: </view>
      <view class="infoBox-b-r">{{info.limitInTime}}</view>
    </view>
    <view class="infoBox-b rowView">
      <view class="infoBox-b-l">退订截止时间: </view>
      <view class="infoBox-b-r">{{info.limitCancelTime}}</view>
    </view>
    <view class="infoBox-b rowView" wx:if="{{info.reservationState == 'EXPIRED'||info.reservationState == 'CANCELED'}}">
      <view class="infoBox-b-l">退回金额： </view>
      <view class="infoBox-b-r">¥{{info.refundableAmount}}</view>
    </view>
  </view>
</view>