var util = require('../../../utils/util');
var api = require('../../../config/api.js');
Page({
  data: {
    id: '',
    info: {},
  },
  onLoad(options) {
    this.setData({
      id: options.id
    })
  },
  onShow() {
    this.getList();
  },
  getList: function () {
    util.showLoading('正在加载…')
    var that = this;
    var data = that.data;
    util.request(api.getRechargeDetailN + data.id, {}, 'GET').then(function (res) {
      var result = res.result;
      if (res.code == '0') {
        that.setData({
          info: result
        })
        wx.hideLoading();
      } else {
        wx.hideLoading();
        util.showToast(res.message)
      }
    });
  },
  goBack() {
    wx.navigateBack({
      delta: 2
    })
  },
})