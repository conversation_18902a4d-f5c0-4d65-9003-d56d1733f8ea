@import "../../common/common.wxss";

page {
  background: #F6F7FB;
}

.yhjBox {
  min-height: 150rpx;
  width: calc(100%-40rpx);
  margin: 20rpx;
}

.yhjBox-l {
  width: 210rpx;
  min-height: 150rpx;
  background: linear-gradient(131deg, #458BFF 0%, #08D7AE 100%);
  box-shadow: 0rpx 3rpx 10rpx 1rpx rgba(37, 107, 245, 0.2);
  opacity: 1;
  border-radius: 20rpx 0 0 20rpx;
  text-align: center;
   justify-content: center;/*纵向居中 */
}

.yhjBox-lqm {
  font-size: 34rpx;
  font-weight: bold;
  color: #F6F7FB;
  line-height: 150rpx;
}

.yhjBox-r {
  flex: 1;
  min-height: 150rpx;
  background: #FFFFFF;
  box-shadow: 0rpx 10rpx 0rpx 1rpx rgba(228, 234, 248, 0.16);
  opacity: 1;
  border-radius: 0 20rpx 20rpx 0;
  justify-content: center;/*纵向居中 */
}

.yhjBox-l-t {
  width: 100%;
  min-height: 84rpx;
  font-size: 60rpx;
  font-weight: bold;
  color: #F6F7FB;
  line-height: 84rpx;
  text-align: center;
}

.yhjBox-l-b {
  width: 100%;
  min-height: 37rpx;
  font-size: 26rpx;
  font-weight: 400;
  color: #F6F7FB;
  line-height: 37rpx;
  text-align: center;
}

.yhjBox-r-t {
  flex: 1;
  min-height: 90rpx;
  font-size: 26rpx;
  font-weight: 400;
  color: #353535;
  line-height: 45rpx;
  margin: 0 24rpx;
  border-bottom: 1rpx solid #DFDFDF;
  padding-top: 10rpx;
}

.yhjBox-r-b {
  width: 372rpx;
  height: 50rpx;
  font-size: 22rpx;
  font-weight: 400;
  color: #7E7E7E;
  line-height: 50rpx;
  padding-left: 24rpx;
}

.unit {
  height: 84rpx;
  font-size: 26rpx;
  line-height: 37rpx;
}
.min{
  font-size: 38rpx;
}