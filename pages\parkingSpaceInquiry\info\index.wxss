/* pages/parkingSpaceInquiry/info/index.wxss */
@import '../../common/common.wxss';

page {
  background: #F6F7FB;
}

.ccxq {
  display: flex;
  flex-direction: column;
}

.listDivHeight {
  width: 100vh;
  height: 100vh;
}

.infoDiv {
  background: #FFFFFF;
  display: flex;
  flex-direction: column;
  width: calc(100% - 40rpx);
  min-height: 160rpx;
  box-shadow: 0rpx 0rpx 20rpx 1rpx rgba(228, 234, 248, 0.16);
  border-radius: 20rpx 20rpx 20rpx 20rpx;
  opacity: 1;
  margin: 20rpx;
}

.cwxqzt {
  width: 100%;
  height: 240rpx;
}

.search-name {
  width: calc(100% - 50rpx);
  min-height: 48rpx;
  font-size: 34rpx;
  font-weight: bold;
  color: #353535;
  line-height: 48rpx;
  margin: 32rpx 20rpx 0 30rpx;
}

.nameinfo {
  width: calc(100% - 50rpx);
  min-height: 40rpx;
  margin: 10rpx 20rpx 10rpx 30rpx;
  display: flex;
  flex-direction: row;
}

.search-name-xq {
  width: calc(100% - 150rpx);
  font-size: 28rpx;
  font-weight: 400;
  color: #7E7E7E;
  line-height: 40rpx;
  flex: 1;
}

.search-dw {
  min-width: 100rpx;
  height: 40rpx;
  font-size: 28rpx;
  font-weight: 400;
  color: #7E7E7E;
  line-height: 40rpx;
  text-align: right;
}

.nameinfo image {
  width: 32rpx;
  height: 32rpx;
  background: #FFFFFF;
  border-radius: 0rpx 0rpx 0rpx 0rpx;
  opacity: 1;
  border: 1rpx solid #707070;
}


.search-dc-num cover-view {
  height: 100%;
  line-height: 54rpx;
}

.search-yw {
  width: calc(100% - 50rpx);
  height: 40rpx;
  color: #4768F3;
  line-height: 40rpx;
  margin: 10rpx 20rpx 10rpx 30rpx;
  font-size: 28rpx;
  font-weight: 400;
}

.search-dc-tip {
  width: calc(100% - 50rpx);
  min-height: 33rpx;
  font-size: 26rpx;
  font-weight: bold;
  color: #353535;
  line-height: 33rpx;
  margin: 10rpx 20rpx 10rpx 30rpx;
}

.yhj {
  width: calc(100% - 92rpx);
  height: 220rpx;
  background: #FFFFFF;
  border-radius: 20rpx 20rpx 20rpx 20rpx;
  opacity: 1;
  margin: 0 20rpx 20rpx 20rpx;
  padding-left: 30rpx;
  padding-right: 20rpx;
}

.info-bt {
  width: 100%;
  height: 45rpx;
  font-size: 32rpx;
  font-weight: bold;
  color: #353535;
  line-height: 54rpx;
  display: flex;
  flex-direction: row;
  margin: 25rpx 0;
}

.info-bt-text {
  width: calc(100% - 137rpx);
  height: 40rpx;
  font-size: 28rpx;
  font-weight: bold;
  color: #000000;
  line-height: 40rpx;
}

.info-btn {
  font-weight: 400;
  color: #888888;
  line-height: 33rpx;
  margin: auto;
  width: 88rpx;
  height: 30rpx;
  font-size: 22rpx;
  font-weight: 400;
  color: #7E7E7E;
  line-height: 30rpx;
}


.yhj-info-d {
  width: 100%;
  height: calc(100% - 91rpx);
  background: #FFFFFF;
}

.yhj-info-d-icon {
  width: 96rpx;
  height: 96rpx;
  border-radius: 10rpx 10rpx 10rpx 10rpx;
  opacity: 1;
}

.yhj-info-d-c {
  flex: 1;
  height: 100%;
  display: flex;
  flex-direction: column;
  margin: 0 40rpx;
}

.yhj-info-d-type {
  width: 100%;
  height: 48rpx;
  font-size: 34rpx;
  font-weight: bold;
  color: #353535;
  line-height: 48rpx;
}

.yhj-info-d-yxq {
  width: 100%;
  height: 40rpx;
  font-size: 28rpx;
  font-weight: 400;
  color: #7E7E7E;
  line-height: 40rpx;
  margin: 10rpx 0;
}

.yhj-info-d-dhBtn {
  border-radius: 52rpx 52rpx 52rpx 52rpx !important;
  line-height: 25rpx !important;
  margin: 20rpx 10rpx ;
  background: linear-gradient(90deg, #FF5745 0%, #FFAE00 100%);
  opacity: 1;
  width: 156rpx !important;
  height: 56rpx !important;
  font-size: 26rpx;
  font-weight: 400;
  color: #FFFFFF;
}

.tcsfbz {
  min-height: 200rpx;
  width: calc(100% - 104rpx);
  background: #FFFFFF;
  opacity: 1;
  margin: 0rpx 20rpx 20rpx 20rpx;
  border-radius: 20rpx 20rpx 20rpx 20rpx;
  padding: 0 32rpx;
}

.tcsfbz-text {
  width: 100%;
  min-height: 45rpx;
  font-size: 28rpx;
  font-weight: 400;
  color: #353535;
  margin-bottom: 10rpx;
}
.hh{
  overflow-wrap:break-word;
}
.tcsfbz-tip {
  width: 100%;
  height: 70rpx;
  font-size: 22rpx;
  font-weight: 400;
  color: #7E7E7E;
  line-height: 32rpx;
  margin-bottom: 20rpx;
}

.greenTitle {
  width: 224rpx;
  height: 40rpx;
  font-size: 28rpx;
  font-weight: bold;
  color: #41E0AC;
  line-height: 54rpx;
  margin-bottom: 20rpx;
}