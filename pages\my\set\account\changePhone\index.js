// pages/my/set/account/changePhone/index.js
var util = require('../../../../../utils/util')
var validator = require('../../../../../utils/validator')
var api = require('../../../../../config/api.js')
var app = getApp()

Page({
  /**
   * 页面的初始数据
   */
  data: {
    phone: app.globalData.userInfo.phone,
    showNextStep: false,
    inputValue: '',
    allowClicking: false,
    showVerification: false,
    showSuccess: false,
    isClick: true,
  },
  onShow: function () {
    let tel1 = validator.maskPhone(app.globalData.userInfo.phone)
    this.setData({
      phone: tel1,
    })
  },
  bindKeyInput: function (e) {
    var phone = e.detail.value
    var flag = false
    if (phone.length == 11) {
      flag = true
    }
    this.setData({
      inputValue: phone,
      allowClicking: flag,
    })
  },
  showNextStep: function () {
    this.setData({
      showNextStep: !this.data.showNextStep,
    })
  },
  nextStep: function () {
    var that = this
    var data = this.data
    if (data.allowClicking) {
      if (!validator.validatePhone(data.inputValue)) {
        util.showToast('请输入正确的手机号')
      } else {
        that.setData({
          showVerification: true,
        })
      }
    }
  },
  modifyPhone: function (e) {
    //修改手机号
    var that = this
    util
      .request(
        api.modifiPhone,
        {
          'verificationId': e.detail.verificationId,
          'code': e.detail.code,
        },
        'PUT'
      )
      .then(function (res) {
        if (res.code == '0') {
          wx.hideLoading()
          util.showToast('验证完成，3秒后跳转')
          app.globalData.userInfo.phone = that.data.inputValue
          try {
            wx.setStorageSync('token', res.result.token)
          } catch (e) {
            console.log(e)
          }
          var i = setTimeout(function () {
            that.setData({
              showNextStep: false,
              showVerification: false,
              showSuccess: true,
              isClick: true,
            })
            clearInterval(i)
          }, 3000)
        } else {
          wx.hideLoading()
          util.showToast('验证失败，请重试')
          that.setData({
            isClick: true,
          })
        }
      })
  },
  goBack: function () {
    wx.navigateBack({
      delta: 1,
    })
  },
  back: function () {
    this.setData({
      phone: app.globalData.userInfo.phone,
      showNextStep: true,
      inputValue: '',
      allowClicking: false,
      showVerification: false,
      showSuccess: false,
    })
  },
})
