const util = require('../../utils/util')
const api = require('../../config/api')
const app = getApp()

function isNull(value) {
  return value === null || value === undefined || value === '' || (Array.isArray(value) && value.length === 0)
}

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // title: '“以旧换新、卖旧买新”项目调查问卷表',
    desc: '本问卷旨在收集群众对于我市促进房产市场“卖旧买新、以旧换新”活动的需求和期望，以便更好地制定相关政策，优化服务，满足广大市民的改善型住房需求。您的意见和建议将作为我们工作改进的重要依据，请您如实填写，感谢您的支持与配合！',
    desc2: '填写调查问卷的客户，将会获得畅行桂林10元停车代金券，领取后三天内可用。',
    list: [
      {
        field: 'owner',
        required: true,
        error: false,
        type: 'input',
        title: '业主信息',
        value: '',
      },
      {
        field: 'contactNumber',
        required: true,
        disabled: true,
        error: false,
        type: 'input',
        title: '联系电话',
        value: '',
      },
      {
        field: 'willParticipate',
        required: true,
        error: false,
        type: 'radio',
        title: '对此项目是否会参与',
        value: '',
        options: [
          { label: '是', value: '是' },
          { label: '否', value: '否' },
        ],
      },
      {
        field: 'oldLocation',
        required: true,
        error: false,
        type: 'radio',
        title: '旧房位置',
        value: '',
        options: [
          { label: '七星区', value: '七星区' },
          { label: '象山区', value: '象山区' },
          { label: '叠彩区', value: '叠彩区' },
          { label: '秀峰区', value: '秀峰区' },
          { label: '临桂区', value: '临桂区' },
        ],
      },
      {
        field: 'oldArea',
        required: true,
        error: false,
        type: 'radio',
        title: '旧房面积',
        value: '',
        options: [
          { label: '30㎡及以下', value: '30及以下' },
          { label: '40㎡', value: '40' },
          { label: '50㎡', value: '50' },
          { label: '60㎡', value: '60' },
          { label: '70㎡及以上', value: '70及以上' },
        ],
      },
      {
        field: 'oldLayout',
        required: true,
        error: false,
        type: 'radio',
        title: '旧房户型',
        value: '',
        options: [
          { label: '一房', value: '一房' },
          { label: '两房', value: '两房' },
          { label: '三房', value: '三房' },
          { label: '四房（及以上）', value: '四房及以上' },
        ],
      },
      {
        field: 'oldFloor',
        required: true,
        error: false,
        type: 'radio',
        title: '旧房楼层',
        value: '',
        options: [
          { label: '1楼', value: '1' },
          { label: '2楼', value: '2' },
          { label: '3楼', value: '3' },
          { label: '4楼', value: '4' },
          { label: '5楼', value: '5' },
          { label: '6楼（及以上）', value: '6及以上' },
        ],
      },
      {
        field: 'oldPsychologicalPrice',
        required: true,
        error: false,
        type: 'radio',
        title: '旧房心理价位',
        value: '',
        options: [
          { label: '20万', value: '20' },
          { label: '30万', value: '30' },
          { label: '40万', value: '10' },
          { label: '50万', value: '50' },
          { label: '60万及以上', value: '60及以上' },
        ],
      },
      {
        field: 'oldAge',
        required: true,
        error: false,
        type: 'radio',
        title: '旧房房龄',
        value: '',
        options: [
          { label: '0-5年', value: '0-5' },
          { label: '5-10年', value: '5-10' },
          { label: '10-15年', value: '10-15' },
          { label: '15年及以上', value: '15及以上' },
        ],
      },
      {
        field: 'oldLoanStatus',
        required: true,
        error: false,
        type: 'radio',
        title: '旧房贷款情况',
        value: '',
        options: [
          { label: '是', value: '是' },
          { label: '否', value: '否' },
        ],
      },
      {
        field: 'newOldLocation',
        required: true,
        error: false,
        type: 'radio',
        title: '拟换新房位置',
        value: '',
        options: [
          { label: '七星区', value: '七星区' },
          { label: '象山区', value: '象山区' },
          { label: '叠彩区', value: '叠彩区' },
          { label: '秀峰区', value: '秀峰区' },
          { label: '临桂区', value: '临桂区' },
        ],
      },
      {
        field: 'newOldArea',
        required: true,
        error: false,
        type: 'radio',
        title: '拟换新房面积',
        value: '',
        options: [
          { label: '30㎡及以下', value: '30及以下' },
          { label: '40㎡', value: '40' },
          { label: '50㎡', value: '50' },
          { label: '60㎡', value: '60' },
          { label: '70㎡及以上', value: '70及以上' },
        ],
      },
      {
        field: 'newOldLayout',
        required: true,
        error: false,
        type: 'radio',
        title: '拟换新房户型',
        value: '',
        options: [
          { label: '一房', value: '一房' },
          { label: '两房', value: '两房' },
          { label: '三房', value: '三房' },
          { label: '四房（及以上）', value: '四房及以上' },
        ],
      },
      {
        field: 'newOldFloor',
        required: true,
        error: false,
        type: 'radio',
        title: '拟换新房楼层',
        value: '',
        options: [
          { label: '1楼', value: '1' },
          { label: '2楼', value: '2' },
          { label: '3楼', value: '3' },
          { label: '4楼', value: '4' },
          { label: '5楼', value: '5' },
          { label: '6楼（及以上）', value: '6及以上' },
        ],
      },
      {
        field: 'newPriceRange',
        required: true,
        error: false,
        type: 'radio',
        title: '拟换新房价格区间',
        value: '',
        options: [
          { label: '40万及以下', value: '40及以下' },
          { label: '50万', value: '50' },
          { label: '60万', value: '60' },
          { label: '70万', value: '70' },
          { label: '80万及以上', value: '80及以上' },
        ],
      },
      {
        field: 'needParkingSpace',
        required: true,
        error: false,
        type: 'radio',
        title: '车位需求',
        value: '',
        options: [
          { label: '是', value: '是' },
          { label: '否', value: '否' },
        ],
      },
      {
        field: 'newWillTakeLoan',
        required: true,
        error: false,
        type: 'radio',
        title: '是否贷款',
        value: '',
        options: [
          { label: '是', value: '是' },
          { label: '否', value: '否' },
        ],
      },
      {
        field: 'oldAdditionalNotes',
        required: true,
        error: false,
        type: 'checkbox',
        max: 5,
        min: 5,
        errorTip: '至少勾选五项',
        title: '侧重以下哪项条件（勾选五项）',
        value: '',
        options: [
          { label: '交易税费', value: '交易税费' },
          { label: '学区', value: '学区' },
          { label: '周边配套', value: '周边配套' },
          { label: '交通', value: '交通' },
          { label: '治安', value: '治安' },
          { label: '小区环境', value: '小区环境' },
        ],
      },
      {
        field: 'newSuggestions',
        required: false,
        error: false,
        type: 'textarea',
        title: '其他补充说明',
        value: '',
      },
    ],
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {},

  onShow() {
    if (app.globalData?.userInfo?.loginStatus != 'LOGGED_IN') {
      wx.redirectTo({ url: '/pages/common/loginTip/loginTip?route=/pages/question/question' })
      wx.hideLoading()
      return
    }
    this.checkAdd()
    this.setData({
      'list[1].value': app.globalData?.userInfo?.phone,
    })
  },

  onBack() {
    wx.reLaunch({ url: '/pages/index/index' })
  },

  bindKeyInput(e) {
    const { index } = e.currentTarget.dataset
    this.setData({
      [`list[${index}].value`]: e.detail.value,
    })
  },
  radioChange(e) {
    const { index } = e.currentTarget.dataset
    this.setData({
      [`list[${index}].value`]: e.detail.value,
    })
    const item = this.data.list[index]
    if (item.field === 'willParticipate') {
      if (item.value === '否') {
        this.storeList = this.data.list.splice(index + 1)
        this.setData({
          list: this.data.list,
        })
      } else {
        if (this.storeList) {
          this.setData({
            list: this.data.list.concat(this.storeList),
          })
          this.storeList = null
        }
      }
    }
  },
  checkboxChange(e) {
    const { index } = e.currentTarget.dataset
    const item = this.data.list[index]
    const value = e.detail.value
    if (item.max) {
      if (value.length >= item.max) {
        item.options.forEach(option => {
          if (!value.includes(option.value)) {
            option.disabled = true
          }
        })
      } else {
        item.options.forEach(option => {
          option.disabled = false
        })
      }
    }
    item.value = value
    this.setData({
      [`list[${index}]`]: this.data.list[index],
    })
  },

  checkAdd() {
    util
      .request(api.getSurveyHavaAdd, { driverId: app.globalData?.userInfo?.driverId }, 'get')
      .then(function (res) {
        if (res.code == 200) {
          if (res.result) {
            wx.redirectTo({ url: '/pages/question-success/question-success?isAdd=true' })
          }
        } else {
          util.showToast(res.message)
        }
      })
      .catch(err => {
        util.showToast('出错了' + JSON.stringify(err))
      })
  },

  onSave() {
    let hasNull = false
    this.data.list.forEach(item => {
      if (item.required) {
        if (isNull(item.value)) {
          item.error = true
          hasNull = true
        } else if (item.min && item.value.length < item.min) {
          item.error = true
          hasNull = true
        } else {
          item.error = false
        }
      } else {
        item.error = false
      }
    })
    this.setData({
      list: this.data.list,
    })

    if (!hasNull) {
      util.showLoading('正在提交...')
      let params = {}
      this.data.list.map(item => {
        if (item.type === 'checkbox') {
          params[item.field] = item.value.join(',')
        } else {
          params[item.field] = item.value
        }
      })
      params.driverId = app.globalData?.userInfo?.driverId

      util
        .request(api.postSurveyAdd, params, 'post')
        .then(function (res) {
          wx.hideLoading()
          if (res.code == 200) {
            wx.redirectTo({ url: '/pages/question-success/question-success' })
          } else {
            util.showToast(res.message)
          }
        })
        .catch(err => {
          wx.hideLoading()
          util.showToast('出错了' + JSON.stringify(err))
        })
    } else {
      util.showToast('有未填写项，请检查')
    }
  },
})
