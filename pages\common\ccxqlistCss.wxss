.listDiv-d {
  width: calc(100% - 100rpx);
  background: #ffffff;
  box-shadow: 0rpx 10rpx 20rpx 0rpx rgba(228, 234, 248, 0.5);
  border-radius: 20rpx;
  margin: 0 50rpx 30rpx 50rpx;
  flex-direction: row;
  display: flex;
  padding: 10rpx 0;
}

.listDiv-d-left {
  width: calc(100% - 140rpx);
  flex-direction: column;
  display: flex;
  margin: auto;
  border-right: 2rpx solid #f4f8ff;
}

.listDiv-search-name {
  max-width: calc(100% - 20rpx);
  min-height: 56rpx;
  font-size: 30rpx;
  color: #000000;
  line-height: 56rpx;
  margin: 0 21rpx;
  font-weight: bold;
}

.listDiv-search-name-xq {
  width: calc(100% - 41rpx);
  min-height: 40rpx;
  font-size: 26rpx;
  font-weight: 400;
  color: #c0c0c0;
  line-height: 40rpx;
  margin-left: 21rpx;
  margin-right: 20rpx;
  margin-bottom: 10rpx;
}

.listDiv-search-dc {
  width: 100%;
  min-height: 32rpx;
  font-size: 24rpx;
  font-weight: 500;
  line-height: 32rpx;
  margin-bottom: 19rpx;
}

.listDiv-search-dc-num {
  margin-left: 21rpx;
  min-width: 192rpx;
  height: 100%;
  color: #353535;
  flex-direction: row;
  display: flex;
  flex-wrap: wrap;
  row-gap: 8rpx;
}

.listDiv-search-yw {
  color: #256bf5;
  margin-right: 10rpx;
}

.listDiv-search-dc-num view {
  height: 100%;
}

.listDiv-search-dc-zw {
  padding-right: 25rpx;
}

.listDiv-search-dc-tip {
  min-width: 156rpx;
  height: 100%;
  color: #353535;
  margin-right: 10rpx;
}

.listDiv-search-dc-yhj {
  width: 99rpx;
  height: 34rpx !important;
  line-height: 34rpx;
  font-size: 24rpx;
  font-weight: 500;
  border-radius: 8rpx;
  text-align: center;
  background: #ffe7d5;
  width: 119rpx;
  color: #da5937;
  font-size: 20rpx;
  margin-right: 10rpx;
}

.listDiv-search-dc-by {
  width: 99rpx;
  height: 38rpx !important;
  line-height: 38rpx;
  font-size: 24rpx;
  font-weight: 500;
  color: #38c798;
  background: #d9f9ee;
  border-radius: 8rpx;
  text-align: center;
  margin-top: 10rpx;
}

.listDiv-d-right {
  width: 140rpx;
  flex-direction: column;
  display: flex;
  margin: auto;
}

.listDiv-d-right image {
  width: 38rpx;
  height: 43rpx;
  margin: 20rpx auto 0 auto;
}

.listDiv-d-dhBtn {
  flex-direction: column;
  display: flex;
}

.listDiv-d-dhBtn text {
  width: 100%;
  height: 37rpx;
  font-size: 26rpx;
  font-weight: 400;
  color: #256bf5;
  line-height: 37rpx;
  text-align: center;
}

.listDiv-search-dw {
  width: 100%;
  font-weight: 400;
  line-height: 28rpx;
  text-align: center;
  min-height: 28rpx;
  font-size: 20rpx;
  min-height: 20rpx;
  font-size: 18rpx;
  color: #353535;
  margin-bottom: 10rpx;
}

.njgtip {
  text-align: center;
  margin: auto;
}

.state {
  width: 100rpx;
  height: 40rpx;
  background: #d9f9ee;
  border-radius: 5rpx 5rpx 5rpx 5rpx;
  opacity: 1;
  text-align: center;
  font-size: 26rpx;
  font-weight: 400;
  color: #24bb8a;
  line-height: 40rpx;
  margin: 0 21rpx 10rpx 21rpx;
}

.grey {
  background: rgba(126, 126, 126, 0.2);
  color: #7e7e7e;
}

.blue {
  background: #d3e1fd;
  color: #256bf5;
}

.holiday-free-tag {
  display: inline-block;
  padding: 0 18rpx;
  height: 34rpx;
  line-height: 34rpx;
  background: linear-gradient(90deg, #ffb347 0%, #ffcc80 100%);
  color: #fff;
  font-size: 20rpx;
  font-weight: 500;
  border-radius: 8rpx;
  box-shadow: 0 2rpx 8rpx rgba(255, 179, 71, 0.15);
}
