<view class="saveOutView">
  <cu-custom bgColor="white-bg" contentTitle="提现" isBack="true"></cu-custom>
  <view wx:if="{{showSuccess}}" class="vBox" >
    <view class="successStatus-icon">
      <image src="../../../image/suc.png" class="successStatus-icon-img"></image>
    </view>
    <view class="successStatus-text">提现申请成功</view>
    <view class="successStatus-text-two" style="margin-bottom: 60rpx;"> 客服申请成功后退回原支付账户</view>
    <button class="convention_button " bindtap="backPage"> 返回</button>
  </view>
  <view wx:else>
  <view class="box" style="height: 240rpx;padding-top: 30rpx;">
    <view class="line rowView withinView" style="margin-bottom: 18rpx;">
      ￥<input placeholder="请输入提现金额"  type="digit" bindinput="check" class="box-input"/>
    </view>
    <view class="withinView tip" wx:if='{{!showTip}}'>
      可提现金额{{withdrawableBalance}}元，本次提现金额不高于10000.00元，不低于1.00元
    </view>
    <view class="withinView tip red" wx:else>{{tipText}}</view>
  </view>
  <view class="box ">
    <view class=" rowView withinView" style="margin-bottom: 18rpx;">
      <input placeholder="请输入短信验证码" style="flex: 1;" bindinput="bindKeyInput" class="box-input"/>
      <view class="v" wx:if='{{showSendCodeBtn}}' bindtap="getVerificationCode">获取验证码</view>
      <view class="v" wx:else>{{currentTime}}S后重发</view>
    </view>
  </view>
  <view class="withinView tip" style="color: #606266;">给绑定手机号{{phone}}发送短信</view>
  <button class="convention_button {{!showTip&(code!='')&(amount!='')?'':'noClike'}}" bindtap="{{!showTip&(code!='')&(amount!='')?'applyForWithdrawal':''}}"> 申请提现</button>
</view>

</view>