var util = require('../../../../utils/util');
var api = require('../../../../config/api.js');
var md5 = require('../../../../utils/md5.js');
Page({
  data: {
    payList: [],
    selectPay: '', //balance WeChatPay选择的支付方式
    clear: false, //是否清除余额键盘密码
    show: false, //是否显示余额键盘
    balanceText: '', //当前余额
    deposit: '', //定金
    parkName: '',
    plateNo: '',
    entryTimeDesc: '',
    id: '',
    parkId:'',
    showSuccese: false //显示成功页面
  },
  onLoad(options) {
    this.setData({
      id: options.id,
      deposit: options.deposit,
      parkId: options.parkId,
      parkName: options.parkName,
      plateNo: options.plateNo,
      entryTimeDesc: options.entryTimeDesc
    })
    this.getPayMethod(options.parkId);
  },
  onShow() {
    
  },
  selectPayMode(e) {
    var currentTarget = e.currentTarget;
    this.setData({
      selectPay: currentTarget.id,
    })
  },
  pay() { //需要向后台 获取参数
    util.showLoading('正在加载…')
    var that = this;
    var data = that.data;
    var selectPay = data.selectPay;
    that.setData({
      isClick: false //在点击一次后，点击状态变为关闭，默认为开启
    })
    var clock = setTimeout(() => { //定义一个延时操作setTimeout
        that.setData({
          isClick: true
        })
        clearInterval(clock); 
      }, 3000 //在3秒后，点击状态恢复为默认开启状态
    )
    if (selectPay === "BALANCE_PAY") {
      if (data.balanceText < data.money) {
        wx.hideLoading();
        util.showToast("余额不足，请充值")
        return;
      }
    }
    util.request(api.reservation, {
      "plateNo":data.plateNo,
      "id": data.parkId,
      "payType": selectPay
    }, 'POST').then(function (res) {
      var infos = res.result;
      if (res.code == '0') {
        if (infos.payStatus == 'PAID') { //说明是无需付款可以直接返回成功
          that.setData({
            showSuccese: true
          })
        } else {
            wx.hideLoading();
            that.payWeiPay(infos.timestamp, infos.nonceStr, infos.packageVal, infos.paySign,infos.signType)
        }
      } else {
        util.showToast(res.message);
      }
      wx.hideLoading();
    });
  },
  payWeiPay: function (timeStamp, nonceStr, packageWord, paySign,signType) {
    var that = this;
    wx.requestPayment({ //吊起微信支付
      timeStamp: timeStamp,
      nonceStr: nonceStr,
      package: packageWord,
      signType: signType,
      paySign: paySign,
      success(res) {
        wx.hideLoading();
        console.log('支付成功');
      },
      fail(res) {
        wx.hideLoading();
        console.log('支付失败');
      },
      'complete': function (res) {
        console.log('支付完成');
        if (res.errMsg == 'requestPayment:ok') {
          that.setData({
            showSuccese: true
          })
        } else {
          wx.showModal({
            title: '提示',
            content: '支付失败'
          });
        }
        return;
      }
    })
  },
  getPayMethod(parkId) { //获取支付方式
    util.showLoading('正在加载…')
    var that = this;
    util.request(api.getPayMethodByParkId , {
      parkId:parkId,
      payBusiness:'ARREARS'
    }, 'GET').then(function (res) {
      var list = res.result;
      if (res.code == '0') {
        console.log("获取支付方式成功")
        wx.hideLoading();
        that.setData({
          payList: list
        });
        if (list.length > 0) {
          var payType = list[0]['payType'];
          console.log("获取支付方式成功---默认支付类型" + payType)
          that.setData({
            selectPay: payType
          });
          if (payType === "BALANCE_PAY") {
            that.setData({
              balanceText: list[0]['iconUrl'] //用来对比余额
            });
          }
        }
      } else {
        console.log("获取支付方式失败" + res.message)
        wx.hideLoading()
      }
    })
  },
  goPage(e) {
    var type = e.currentTarget.dataset.type;
    if (type == 'index') {
      wx.switchTab({
        url: '/pages/index/index',
      })
    } else {
      wx.navigateTo({
        url: '/pages/vehicleReservation/index',
      })
    }
  }
})