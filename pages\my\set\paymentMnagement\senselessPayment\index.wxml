<view class="saveOutView">
  <cu-custom bgColor="white-bg" contentTitle="无感支付管理" isBack="true"></cu-custom>
  <view class="text">开启后，可通过扣除余额进行无感支付停车费</view>
  <view class="text" style="margin-top: 0;">未添加车牌无法显示配置信息，请先添加车牌</view>
  <scroll-view scroll-y="true" style="height: 1rpx;flex: 1;">
    <block wx:for="{{carList}}" wx:key="index" scroll-y="true">
      <view class="box">
        <view class="row ">
          <view style="flex: 1;">{{item.plateNo}}</view>
          <switch checked="{{item.amountEtc==0?false:true}}" bindchange="switchChange" color="#41E0AC" data-plate="{{item.plateNo}}" />
        </view>
      </view>
    </block>
  </scroll-view>
</view>