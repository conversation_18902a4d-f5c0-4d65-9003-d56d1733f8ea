<view class="saveOutView">
  <cu-custom bgColor="white-bg" contentTitle="明细" isBack="true"></cu-custom>
  <view wx:if="{{ list.length == 0 }}">
    <image src="../../../image/qfjf-null.png" class="nullImg"></image>
    <view class="nullTip">暂无明细记录</view>
  </view>
  <scroll-view scroll-y="true" class="scrollViewSa" bindscrolltolower="bindReachBottom" wx:else>
    <block wx:for="{{list}}" wx:key="index" scroll-y="true" bindscrolltolower="bindReachBottom">
      <view class="title">{{item.date}}</view>
      <view class="box" wx:for="{{item.balanceBargains}}" wx:for-item="items" wx:key="childindex">
        <view class="rowView one">
          <view class="autoM">{{items.desc}}</view>
          <view class="{{items.type==0?'reduce':'add'}}">{{items.type==0?'-':'+'}}￥{{items.money}}</view>
        </view>
        <view class="rowView two">
          <view style="width: 260rpx;" class="autoM">{{items.time}}</view>
          <view>{{items.moneyAfter}}</view>
        </view>
      </view>
    </block>
  </scroll-view>
  <view class="viewPlaceholder_private"> </view>
</view>