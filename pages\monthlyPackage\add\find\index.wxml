<cu-custom bgColor="white-bg" contentTitle="{{isFind?'查找停车场':'车场包月'}}" isBack="true"></cu-custom>

<view class="searchBar">
  <image class="Icon-search" src="../../../../image/merry-search.png"></image>
  <input type="text" placeholder="输入需要包月的车场名称" confirm-type="search" value="{{keyword}}" bindfocus="keywordfocus" bindinput="getKeyword" bindconfirm='bindconfirm'></input>
  <image class="cuIcon-delete" wx:if="{{onSearch}}" bindtap="offSearch" src="../../../../image/input-close.png"></image>
</view>
<block wx:for="{{list}}" wx:key="index">
  <view class="listDiv-d" bindtap="goxq" id="{{item.id}}">
    <view class="listDiv-d-left">
            <view class="rowView">
              <view class="listDiv-search-name ">{{item.name}}</view>
              <view class="listDiv-search-dc-by" wx:if="{{item.monthly}}">可包月</view>
            </view>
            <view class="listDiv-search-name-xq">{{item.address}}</view>
            <view class="listDiv-search-dc">
              <view class="listDiv-search-yw margin10" wx:if="{{item.dataSource=='HIK'}}">余位 {{item.surplus}}/</view>
              <view class="listDiv-search-dc-zw margin10" wx:if="{{item.dataSource=='HIK'}}">{{item.total}}</view>
              <view class="listDiv-search-dc-tip margin10" wx:if="{{item.freeTimeDesc!=''}}">{{item.freeTimeDesc}}</view>
              <view class="listDiv-search-dc-yhj margin10" catchtap="goxq" wx:if="{{item.discount}}">优惠券可用</view>
            </view>
          </view>
    <view class="listDiv-d-right">
      <view class="listDiv-d-dhBtn" catchtap="openMapApp" data-lat="{{item.latitude}}" data-long="{{item.longitude}}" data-name="{{item.name}}" data-address="{{item.address}}">
        <image src="../../../../image/dh.png"></image>
        <text>导航</text>
      </view>
      <view class="listDiv-search-dw">{{item.distance}}km</view>
    </view>
  </view>
</block>
<view wx:if="{{showNullTip}}" class="njgtip">无符合查询条件数据</view>
<view wx:if="{{showNullMoreTip}}" class="njgtip">没有更多信息了</view>
<monthlySubscriptionRules isShow="{{isShowRules}}" rules="{{rules}}" bind:selectRule="selectRule"></monthlySubscriptionRules>