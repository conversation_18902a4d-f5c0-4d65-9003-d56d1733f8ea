<view class="saveOutView">
  <cu-custom bgColor="white-bg" contentTitle="{{info.resourceType=='CHARGE'?'充电桩详情':'车场详情'}}" isBack="true"></cu-custom>
  <scroll-view scroll-y="true" style="flex: 1;height: 1rpx;padding-bottom: 20rpx;">
    <view class="cwxqzt">
      <image class="cwxqzt" mode="scaleToFill" src="{{(info.parkPicUrl==''||info.parkPicUrl==null)?imgurl:info.parkPicUrl}}" lazy-load='true'></image>
    </view>
    <view class="infoDiv" id="{{id}}" data-type="{{info.type}}">
      <view class="search-name flex-wrp">{{info.parkName}}</view>
      <view class="nameinfo">
        <view class="search-name-xq ">{{info.parkingAddress}}</view>
        <view class="search-dw ">{{info.distance}}km</view>
      </view>
      <view class="rowView">
        <view class="search-yw " wx:if="{{info.dataSource=='HIK'}}">余位 {{info.leftParkingSpaceNum}}<text style="color: #7E7E7E;">/{{info.totalParkingSpaceNum}}</text></view>
        <view class="search-yw " text style="color: #353535;" wx:else hidden="{{info.resourceType=='CHARGE'}}">停车位{{info.totalParkingSpaceNum}}</view>
        <view class="search-yw " wx:if="{{info.dataSource=='HIK'&&info.resourceType!='CHARGE'}}">残疾车位 <text style="color: #353535;">{{info.disabledParkingSpaceNum==0?"无":info.disabledParkingSpaceNum}}</text></view>
        <view class="search-yw " wx:if="{{info.dataSource=='HIK'&&info.resourceType!='CHARGE'}}">大巴车位 <text style="color: #353535;">{{info.busParkingSpaceNum==0?"无":info.busParkingSpaceNum}}</text></view>
      </view>

      <!-- <view class="search-dc-tip">{{info.payRuleDesc}}</view> -->
    </view>
    <view class="yhj columnView" wx:if="{{info.discount}}">
      <view class="info-bt">
        <view class="icon-sjx-left-tilte"></view>
        <view class="info-bt-text">优惠劵</view>
        <view class="info-btn">查看已领</view>
        <view class="icon-jt-left" style="  border-width: 2rpx 2rpx 0 0;  width: 13rpx;height: 13rpx;"></view>
      </view>
      <view class="yhj-info-d rowView">
        <image class="yhj-info-d-icon" mode="scaleToFill" src="../../../image/yhj.png"></image>
        <view class="yhj-info-d-c">
          <view class="yhj-info-d-type">免费领取停车券</view>
          <view class="yhj-info-d-yxq">限时发放 快去领取吧~</view>
        </view>
        <button class="yhj-info-d-dhBtn" type="primary" style="padding: 8px 14px!important;">领取</button>
      </view>
    </view>
    <view class="yhj columnView" wx:if="{{info.monthly}}">
      <view class="info-bt">
        <view class="icon-sjx-left-tilte"></view>
        <view class="info-bt-text">包月套餐</view>
        <view class="info-btn"></view>
      </view>
      <view class="yhj-info-d rowView">
        <image class="yhj-info-d-icon" mode="scaleToFill" src="../../../image/yhj_2.png"></image>
        <view class="yhj-info-d-c">
          <view class="yhj-info-d-type">包月停车优惠便捷</view>
          <view class="yhj-info-d-yxq">更多包月优惠即将发布</view>
        </view>
        <button class="yhj-info-d-dhBtn" type="primary" style="padding: 8px 14px!important;" bindtap="goPage" data-type="buy">去购买</button>
      </view>
    </view>
     <view class="tcsfbz columnView" hidden="{{(info.dataSource!='HIK'&&info.resourceType=='CHARGE')}}">
     <!--需求：社会面的充电站不显示收费标准，自营充电桩才显示-->
      <view class="info-bt">
        <view class="icon-sjx-left-tilte"></view>
        <view class="info-bt-text" style="width: calc(100% - 10px);">收费标准</view>
      </view>
      <view class="tcsfbz-text"><text>{{info.payRuleDesc==''||info.payRuleDesc==null? '暂无': info.payRuleDesc}}</text></view>
      <!-- 需求：11.6 运营填规则的时候，按了回车键 所以需要显示换行-->
      <view class="greenTitle" wx:if="{{info.resourceType!='CHARGE'&&info.newEnergyPayRuleDesc!=''}}">新能源车收费标准</view>
      <view class="tcsfbz-text" wx:if="{{info.resourceType!='CHARGE'&&info.newEnergyPayRuleDesc!=''}}">{{ info.newEnergyPayRuleDesc}}</view>
      <view class="tcsfbz-tip" hidden="{{(info.dataSource=='HIK')}}">温馨提示:车场临停收费标准以车场公示为准，如有疑问请联系车场物业管理方。</view>
      <!-- 需求：10.31路测泊位不要展示这个提示，只有第三方车场（社会面）详情页面展示 -->
    </view>
    <!-- <view class="box_conventionTwo_button" wx:if="{{info.reservation}}">
      <view class="conventionTwo_button-left" bindtap="openMapApp">导航</view>
      <view class="conventionTwo_button-right" bindtap="goPage" data-type="vehicleReservation">预约车位</view>
    </view> -->
    <button class="convention_button" formType="submit" bindtap="openMapApp" >导航</button>
  </scroll-view>
</view>
<loginDialog showModal="{{showLoginDialog}}" bind:refresh="goNextPage"></loginDialog>
<bottomListDialog id='dialog' catchtouchmove="preventTouchMove" bind:selectItem="selectItem" isShow="{{showPlateNoBox}}" list="{{plateNos}}" rangekey="plateNo" disablekey="bagable" disableText="已包月,需办理请前往续费"></bottomListDialog>
<monthlySubscriptionRules isShow="{{isShowRules}}" rules="{{rules}}" bind:selectRule="selectRule"></monthlySubscriptionRules>