<view class="box columnView {{couponState=='1'?'':'grey'}}" style="{{couponState!='1'?bgurl:''}}">
  <view class="box-t rowView">
    <view class="columnView" style="flex: 1;">
      <view class="box-t-l-t">{{info.typeName}}</view>
      <view class="box-t-l-b">{{info.validDate}}</view>
    </view>
    <view class="box-t-r">{{info.couponValueText}}</view>
  </view>
  <view class="rowView" bindtap="openOrClose" style="height: auto;">
    <view class="box-b-l {{show?'show':'close'}}" >{{info.desc}}</view>
    <view class="udbtn {{show?'down':'up'}}">
    </view>
  </view>
</view>