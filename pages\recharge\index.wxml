<view class="saveOutView">
  <cu-custom bgColor="white-bg" contentTitle="充值" isBack="true"></cu-custom>
  <scroll-view class="scrollViewSa" scroll-y="true">
    <view class="info columnView">
      <view class="info-one" style="background-image: url('{{imgUrl}}pay-j.png');">
        <view class="info-oneR-tilte-down">
          <view class="info-oneR-text-up"><text style="font-size:24rpx; ">¥</text>{{balance}}</view>
          <view class="info-oneR-text-down">包含赠送金额{{giftBalance}}元</view>
        </view>
      </view>
      <view class="rowView">
        <view class="info-oneR-tip">钱包充值用于便捷离场</view>
        <view class="sqtk" bindtap="showRefundTip">提现</view>
        <view class="sqtk" bindtap="goDetail">明细</view>
      </view>
    </view>
    <view class="backWihte columnView">
      <view class="info-bt">
        <view class="icon-sjx-left-tilte"></view>
        <view class="info-bt-text">充值额度</view>
      </view>
      <view class="infoListBox recharge">
        <block wx:for="{{list}}" wx:key="index">
          <view bindtap="selectPackage" data-money="{{item.price}}" id="{{item.pkgId}}" data-index="{{index}}" class="infoBox {{selectIndex == index ? 'choose':''}}">
            <view class="infoBox-ysb" wx:if="{{item.discountDesc!=''}}">{{item.discountDesc}}</view>
            <view class="infoBox-text" style="{{item.discountDesc!=''?'': 'margin-top: 35rpx'}}">¥<text style="font-size: 36rpx;">{{item.price}}</text></view>
            <view class="infoBox-text">{{item.pkgDesc}}</view>
          </view>
        </block>
      </view>
    </view>
    <view class="backWihte rowView other {{selectIndex==null?'selected':''}}">
      其他额度：<input placeholder="请输入需充值额度" style="flex: 1;" bindinput="bindKeyInput" bindfocus="bindfocus" class="other-input" value="{{amount}}" type="number" />
    </view>
    <view class="other-tip">温馨提示：自定义充值额度最低1元，最高500元</view>
    <view class="backWihte columnView">
      <view class="info-bt">
        <view class="icon-sjx-left-tilte"></view>
        <view class="info-bt-text">支付方式</view>
      </view>
      <view class="paymentListBox">
        <view id="WeChatPay" class="infoBox {{selectPay == 'WeChatPay' ? 'choosePay':''}}" style="display: flex; flex-direction: column;" bindtap="selectPay">
          <image class="img" src="../../image/wx-pay.png" style="width: 48rpx;height: 48rpx;position: static;margin: 14rpx auto 0;"></image>
          <view class="infoBox-text" style="font-size: 26rpx;">微信支付</view>
        </view>
      </view>
    </view>
    <view class="backWihte rowView" style="line-height: 90rpx;">
      <view class="infoBox-pay" style="margin-left: 52rpx;">应付金额:</view>
      <view class="infoBox-pay" style="color: #FF2D2D;">¥{{money}}</view>
    </view>
    <button class="convention_button" bindtap="pay">支付</button>
  </scroll-view>
</view>