@import '/pages/common/common.wxss';
@import '/pages/common/ccxqlistCss.wxss';
@import '/pages/components/common.wxss';

page {
  background: #f6f9ff;
}

.outermostLayer {
  display: flex;
  flex-direction: column;
  height: 100hv;
}

.float-box {
  position: relative;
  top: -60rpx;
}

.center-two {
  width: calc(100% - 100rpx);
  min-height: 250rpx;
  background: #ffffff;
  box-shadow: 0rpx 10rpx 20rpx 0rpx rgba(228, 234, 248, 0.5);
  border-radius: 20rpx;
  margin: 0 50rpx 30rpx 50rpx;
  flex-direction: column;
  display: flex;
}

.center-two-up {
  display: flex;
  flex-direction: row;
}

.center-two-up-left {
  min-width: 220rpx;
  margin: 43rpx 30rpx 42rpx 27rpx;
  height: 34rpx;
  font-size: 36rpx;
  font-weight: 800;
  line-height: 34rpx;
  color: #384b64;
}

.center-two-up-left.wdl {
  font-weight: bold;
  font-size: 26rpx;
  margin: 10rpx 20rpx;
  line-height: 56rpx;
  height: 56rpx;
}

.haveCar {
  background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABsAAAAPCAYAAAAVk7TYAAAAAXNSR0IArs4c6QAAAlxJREFUOE+tk01oE1EUhc+dJumP2IAiirqw6Ma6kmAyWYgRiaYZC9ISihSNpENdSBeCXYkYETcFRV0UF5O2KGQh7SaSSa0IATdJKgoKikhRKYUGF2oxqdZkrkw0JTNJk/rzlnfuOd89972hzs6Arb3DHmGgh5hvpNTIJfyn4zwecpMmDBHhM9HKNRIleQpAz6o/405K3XkOCGv/whQlWfeMAmj+7ZPRYUsANhqMmaMt+blgMpks/A1Q9A0E0UQKAEuFPk8uf6gXRFEC2SqNGRzL5op975MT3/4EKEqhIYZwkwDBFGCE9ILbP+DTiKYIaDMZP17K2U68So5+XQ9QlOSLAK4CKPmWD0O7no6PXVgtOiX5IDEeEMFuNOZ0DstdL+PRT/WALr88QoRhUw8zcDkdV/QBjBO4pKCDYE0A2GJcAV4UUTg2q04sVgPDgss/P0pEZ41poBHjfEpVbpfrhrh6UZTkvQAeAdhhSvj2O8P7XI18KNc9Ho9lecOeuwScNA1RYMZgWlXGK+tVMP2j86jcIVhLwN2GaZnnixp7n06PvdnlOdOyrc1yH4Ru08NagUb96YQyad5CTZjetN8b2m6zCTME7DOJsloBAWriK0R02LS6vMbcO6tGpmvd75qwUsIjpzcLzbYECAcavUZmfGFCdyauPFmrty5MF7m6+tuJWmMgHKoD/IhC0Zd6OP6s3lANYbrY7Q60apvskwT4a5gtAPCm4srrRunXBdNNHI5Bq3Wrdg+EvgrTOe0HvJkZ5V0jUNV/1lgQFkRp4RYznwI4BgsNp2NKtrHuV8dP56bJCAf8E1EAAAAASUVORK5CYII=');
  background-repeat: no-repeat;
  background-size: 27rpx 15rpx;
  background-position: right;
}

.center-two-up-right {
  flex: 1;
  width: 0;
  padding-right: 32rpx;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.center-two-up-right-text {
  height: 56rpx;
  line-height: 56rpx;
  font-size: 22rpx;
  font-weight: bold;
  color: #fe536d;
}

.btn-zhifu {
  width: 170rpx;
  height: 58rpx;
}
.center-right-bottom {
  display: flex;
}

.center-two-down {
  line-height: 98rpx;
  font-size: 26rpx;

  font-weight: 500;
  color: #7e7e7e;
  width: 650rpx;
  height: 98rpx;
  background: #ffffff;
  border-radius: 20rpx;
  margin: 40rpx 0 20rpx 50rpx;
}

.list-tool {
  margin: 0 50rpx 20rpx 50rpx;
  width: calc(100% - 100rpx);
  display: flex;
  flex-direction: row;
  font-size: 22rpx;
  font-weight: bold;
  color: #353535;
  text-align: center;
}

.list-tool .selected {
  width: 131rpx;
  height: 38rpx;
  background: #e5f0ff;
  border-radius: 10rpx;
  color: #085ae5;
}

.list-tool-scroll {
  height: 80rpx;
  width: 100%;
  white-space: nowrap;
}

.list-tool-select {
  width: 131rpx;
  height: 38rpx;
  background: #ffffff;
  border-radius: 10rpx;
  line-height: 38rpx;
  margin-right: 16rpx;
  margin-top: 5rpx;
}

.list-tool image {
  height: 54rpx;
  width: 155rpx;
  position: absolute;
  right: 50rpx;
}

.center-one-v-img {
  margin-top: 0;
}

.center-one-v-text {
  margin-top: 0;
  font-size: 22rpx;
  font-weight: 500;
  color: #384b64;
}

.swiper {
  width: 100%;
  margin: 0;
  height: 550rpx;
}

.wx-swiper-dots .wx-swiper-dot {
  width: 14rpx;
  height: 14rpx;
}
.wx-swiper-dots.wx-swiper-dots-horizontal {
  bottom: 70rpx;
}

.center-one {
  flex-wrap: wrap;
  display: flex;
  margin-top: 46rpx;
  width: calc(100% - 32rpx);
  margin: 0 16rpx;
  min-height: 272rpx;
  border-radius: 20rpx;
}

.center-one-v {
  flex-direction: column;
  display: flex;
  align-items: center;
  flex-basis: auto;
  margin: 0 33rpx 25rpx 33rpx;
  width: 90rpx;
  height: 110rpx;
}

.listDiv-search-dc {
  flex-wrap: wrap;
  display: flex;
  padding-left: 20rpx;
}

.margin10 {
  margin-top: 10rpx;
}

.convention_bottom_btn_medium {
  width: 200rpx;
  margin: 10rpx 0 20rpx 0;
}

/* 临时车牌开始 */
.con-query {
  width: 100%;
  border-radius: 8px;
}

.pages_header {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.pages_header_top {
  width: 33.3%;
  height: 60rpx;
  border-left: 5px solid green;
  border-right: 5px solid green;
}

.pages_header_btm {
  width: 70%;
  background: green;
  height: 120rpx;
  line-height: 120rpx;
  text-align: center;
  color: white;
  border-radius: 10rpx;
  font-weight: normal;
  font-size: 16pt;
}

.tips {
  text-align: center;
  margin: 60rpx 0;
  font-size: 12pt;
  color: #888888;
}

.plate-input-text {
  text-align: center;
  line-height: 90rpx;
  color: #f39900;
}

.plate-input-flag {
  float: right;
  margin-right: 8%;
  font-size: 14px;
}

.plate-input-flag .new-energy {
  color: #14c414;
}

.plate-input-body {
  height: 90rpx;
  width: calc(100% - 18rpx);
  margin: 20rpx 0 35rpx 0;
  margin-left: 9rpx;
}

.plate-input-content {
  display: flex;
  flex-direction: row;
  height: 90rpx;
}

.plate-nums-foc {
  width: 70rpx;
  height: 90rpx;
  display: flex;
  flex: 1;
  margin: 0 5rpx 0 5rpx;
  align-items: center;
  justify-content: center;
  text-align: center;
  box-sizing: border-box;
  background: #ffffff;
  opacity: 1;
  border-radius: 5rpx 5rpx 5rpx 5rpx;
}

.plate-nums-foc .plate-num-text {
  border: 2rpx solid #4768f3;
}

.plate-nums-first {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.plate-num-text {
  font-size: 40rpx;
  font-weight: 300;
  border-radius: 5rpx 5rpx 5rpx 5rpx;
  opacity: 1;
  border: 1rpx solid #7e7e7e;
  line-height: 80rpx;
  width: 70rpx;
  height: 80rpx;
  background: #ffffff;
}

.new-plate-input-content {
  display: flex;
  flex-direction: row;
  height: 100rpx;
}

.kb_top {
  align-content: relative;
  width: 100%;
  height: 74rpx;
  background: #fff;
  border-top: solid #ebebeb 2rpx;
  border-bottom: 15rpx solid #f4f4f4;
}

.keyboard {
  z-index: 9999;
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  height: auto;
  background: #f4f4f4;
  display: flex;
  flex-wrap: wrap;
  border-bottom: 15rpx solid #f4f4f4;
}

.td {
  flex-grow: 1;
  text-align: center;
  font-size: 34rpx;
  height: 86rpx;
  line-height: 80rpx;
  background: #fff;
  margin: 10rpx 5rpx;
  color: #333;
  border-radius: 2rpx;
  box-shadow: 0rpx 2rpx 0rpx #a9a9a9;
}

.td_nor {
  flex: 1 1 6%;
}

.td_num {
  flex: 1 1 8%;
}

.td_spec {
  flex: 1 1 12%;
}

.board_bg {
  box-shadow: 0 0 0 #e5e5e5;
  background: #e5e5e5;
}

.del-first {
  position: absolute;
  bottom: 10rpx;
  right: 10rpx;
  width: 100rpx;
  height: 86rpx;
  background-color: #fff;
  box-shadow: 0rpx 2rpx 0rpx #a9a9a9;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 10rpx;
}

.del-hover {
  position: absolute;
  bottom: 10rpx;
  right: 10rpx;
  width: 137rpx;
  height: 86rpx;
  background-color: #e5e5e5;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 10rpx;
  box-shadow: 0 0 0 #e5e5e5;
}

.del-img {
  display: block;
  width: 46rpx;
  height: 38rpx;
}

.yes_icon {
  width: 40rpx;
  height: 40rpx;
  margin: 0 10rpx 0 10rpx;
}

.dx {
  flex-direction: row;
  display: flex;
  text-align: center;
  height: 38rpx;
  font-size: 28rpx;
  font-weight: 400;
  color: #7e7e7e;
  width: 150rpx;
  margin: 0 20rpx 14rpx auto;
}

.dx > .text {
  width: 100rpx;
}

.noAllowClick {
  background: #c0c0c0;
}

.convention_button {
  height: 62rpx;
  line-height: 30rpx;
}

/* 临时车牌结束 */

.complete {
  position: absolute;
  right: 0;
  display: block;
  height: 74rpx;
  padding: 0 34rpx;
  color: #6883f5;
  line-height: 74rpx;
  font-size: 30rpx;
}

/* 弹窗广告 */
.ad-bg {
  width: 100%;
  height: 100%;
  position: fixed;
  box-sizing: border-box;
  top: 0;
  left: 0;
  z-index: 990;
  background-color: #000;
  opacity: 0.7;
}
.ad {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 999;
  text-align: center;
  color: #fff;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.ad-img {
  width: 100%;
}
.glicon-guanbi {
  font-size: 48rpx;
  margin-top: 80rpx;
}

.qf-listDiv-right-three {
  font-size: 28rpx;
  font-weight: 400;
  color: #ffffff;
  text-align: center;
  width: 170rpx;
  height: 48rpx;
  line-height: 48rpx;
  background: linear-gradient(90deg, #ff5745 0%, #ffae00 100%);
  border-radius: 52rpx;
  opacity: 1;
}
.btn-pay {
  background: #458bff;
}

.ai-float-btn {
  position: fixed;
  right: -20rpx;
  top: 50%;
  transform: translateY(-50%);
  z-index: 9998;
  width: 96rpx;
  height: 96rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 16rpx rgba(37, 107, 245, 0.18);
  border-radius: 50%;
  background: #fff;
}
.ai-float-logo {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
}
