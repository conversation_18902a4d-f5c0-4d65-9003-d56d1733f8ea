page {
  background-color: #F6F7FB;
}

.container {
  width: 100%;
}

.container .header {
  width: 100%;
  height: 100rpx;
  background: linear-gradient(218deg, #5A7EFF 0%, #478BFF 100%);
  box-shadow: inset 0rpx 3rpx 15rpx 1rpx #98ACFF;

  font-size: 36rpx;
  color: #FFFFFF;
  line-height: 100rpx;
  text-align: center;
}

.container .content {
  padding: 0 50rpx;
  width: 100%;
  box-sizing: border-box;
}

.container .content .begin {
  margin-top: 30rpx;
  margin-bottom: 20rpx;
  width: 100%;
  font-size: 30rpx;
  color: #000000;
  line-height: 48rpx;
}

.container .content .item {
  margin-top: 30rpx;
  width: 100%;
  display: flex;
  flex-direction: row;
  display: flex;
  align-items: center;
}

.container .content .item .desc {
  flex: 1;
  font-size: 26rpx;
  color: #606266;
  line-height: 36rpx;
}

.container .content .item .status {
  margin-left: 170rpx;
  width: 137rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
}

.container .content .item .status .icon {
  width: 40rpx;
  height: 40rpx;
}

.container .content .item .status .text {
  margin-left: 10rpx;
  font-size: 26rpx;
  color: #606266;
  line-height: 36rpx;
}

.bottom-control {
  height: 253rpx;
}

.bottom-control .explain {
  padding: 0 101rpx;
  width: 100%;
  box-sizing: border-box;
  font-size: 26rpx;
  color: #606266;
  line-height: 36rpx;
  text-align: center;
  margin-bottom: 40rpx;
}

.bottom-control .explain .light {
  color: #256BF5;
}

.bottom-control .btn {
  margin: 40rpx 20rpx 0 20rpx;
  width: calc(100% - 40rpx);
  height: 80rpx;
  background: linear-gradient(90deg, #458BFF 0%, #08D7AE 100%);
  box-shadow: 0rpx 3rpx 10rpx 1rpx rgba(37, 107, 245, 0.2);
  border-radius: 40rpx;
  text-align: center;
  font-size: 36rpx;
  color: #FFFFFF;
  line-height: 80rpx;
}

.noClick {
  background: #C0C0C0;
}