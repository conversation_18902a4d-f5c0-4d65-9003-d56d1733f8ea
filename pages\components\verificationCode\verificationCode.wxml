<!--pages/common/verificationCode/index.wxml-->
<view wx:if="{{showVerification}}" class="vBox">
  <view class="tipV">请输入短信验证码</view>
  <view class="tipVT">已发送验证码至{{phone}}</view>
  <input class="inputV" auto-focus placeholder="请输入验证码" type="number" bindinput="bindKeyInput" maxlength="6" />
  <view class="tipVTT" wx:if="{{showSendCodeBtn}}">{{currentTime}}s重新发送</view>
  <view class="tipVTT" wx:else><text wx:if="{{phoneIsTrue}}">5分钟内有效</text><text bindtap="getVerificationCode" style="color: #256BF5; padding: 18rpx 20rpx;">重新发送</text></view>
</view>
<button class="convention_button" bindtap="sure" wx:if="{{showVerification}}">确定</button>
<view wx:if="{{showSuccess}}" class="vBox" style="width: calc(100% - 100rpx);">
  <view class="successStatus-icon">
    <image src="../../../image/suc.png" class="successStatus-icon-img"></image>
  </view>
  <view class="successStatus-text" wx:if="{{type== 'UPDATE_PHONE'}}">更换手机号成功，下次请使用</view>
  <view class="successStatus-text" wx:else>如您在畅行桂林其他平台有登录，请退出登录以确保账号</view>
  <view class="successStatus-text" wx:if="{{type== 'UPDATE_PHONE'}}" style="margin-bottom: 99rpx;"> {{phone}}登录</view>
  <view class="successStatus-text" style="margin-bottom: 99rpx;" wx:else> 注销成功;如无请忽略!</view>
</view>
<button class="convention_button " bindtap="BackPage" wx:if="{{showSuccess}}">我知道了</button>