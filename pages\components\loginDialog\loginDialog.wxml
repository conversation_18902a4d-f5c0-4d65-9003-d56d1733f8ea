<!--pages/login/index.wxml-->
<!-- 第一个版本。修改过流程，暂时不用了。 -->
<view class="model" catchtouchmove='preventTouchMove' wx:if='{{showModal}}'></view>
<view class="modalDlg" catchtouchmove='preventTouchMove' wx:if='{{showModal}}'>
  <view class='windowRow'>
    <text class='modalDlg-title'>提示</text>
    <image class='back' bindtap='back' src="../../../image/close.png"></image>
  </view>
  <text class='modalDlg-title-t'>新用户注册享优惠</text>
  <image src="../../../image/phone-icon.png" style="height:249rpx;width:322rpx;"></image>
  <view class='modalDlg-xyView'>
    <image class='yes_icon' wx:if="{{!allowXY}}" src="../../../image/yes-icon.png" bindtap='allow'></image>
    <image class='yes_icon' wx:if="{{allowXY}}" src="../../../image/yes-icon-select.png" bindtap='allow'></image>
    <text class='modalDlg-xyView-text'>登录代表您已同意</text>
    <text class='modalDlg-xyView-text xy' id='serviceUrl' bindtap="viewAgreement">《服务协议》</text>
    <text class='modalDlg-xyView-text'>和</text>
    <text class='modalDlg-xyView-text xy' id='privacyUrl' bindtap="viewAgreement">《软件注册及隐私政策协议》</text>
  </view>
  <view wx:if="{{!allowXY}}" style="width:94%" class='wishbnt' bindtap='sqbd'>注册</view>
  <button wx:if="{{allowXY}}" style="width:94%" class='wishbnt' open-type="getPhoneNumber" bindgetphonenumber="getPhoneNumber" >注册</button>
</view>
<twoBtnDialog showModal="{{showGetNumber}}" title="提示" leftBtnText="取消" rightBtnText="去添加" tipText="请添加车牌号" bind:rightGo="getRightGo" bind:leftGo="getLeftGo"></twoBtnDialog>