<view class="saveOutView">
  <cu-custom bgColor="white-bg" contentTitle="优惠券" isBack="true"></cu-custom>
  <view class="rowView headToolbar">
    <view class="{{couponState=='1'?'selectType':''}} headToolbar-v" bindtap="selectCoupon" id='1'>未使用<view class="bg"></view>
    </view>
    <view class="{{couponState=='2'?'selectType':''}} headToolbar-v" bindtap="selectCoupon" id='2'>已使用<view class="bg"></view>
    </view>
    <view class="{{couponState=='3'?'selectType':''}} headToolbar-v" bindtap="selectCoupon" id='3'>已过期<view class="bg"></view>
    </view>
  </view>
  <view wx:if="{{ list.length == 0 }}">
    <image src="../../../image/qfjf-null.png" class="nullImg"></image>
    <view class="nullTip">无{{couponStateEnumeration[couponState]}}优惠券</view>
  </view>
  <scroll-view scroll-y="true" class="scrollViewSa"  wx:else>
    <block wx:for="{{list}}" wx:key="index" scroll-y="true">
      <couponBox info="{{item}}" bind:openOrClose="open" couponState="{{couponState}}" bgurl="{{bgurl}}">使用item自定义组件</couponBox>
    </block>
  </scroll-view>
</view>