page {
  background: #F6F7FB;
}

.info-bt {
  width: calc(100% - 16rpx);
  height: 45rpx;
  font-size: 32rpx;
  
  font-weight: bold;
  color: #353535;
  line-height: 54rpx;
  display: flex;
  flex-direction: row;
  margin: 19rpx 0;
  padding: 0 4rpx;
}

.info-bt-text {
  width: calc(100% - 50rpx);
  height: 45rpx;
  font-size: 32rpx;
  
  font-weight: bold;
  color: #353535;
  line-height: 45rpx;
}

.info-one {
  width: calc(100% - 40rpx);
  min-height: 220rpx;
  padding: 0 20rpx;
  background-color: #ffffff;
}

.img {
  position: absolute;
  width: calc(100% - 40rpx);
  height: 220rpx
}


.infoListBox {
  /* 实现自动换行 */
  width: calc(100% - 20rpx);
  min-height: 220rpx;
  display: flex;
  flex-wrap: wrap;
  flex-direction: row;
  box-sizing: content-box;
}

.infoBox {
  margin: 0 0 19rpx 19rpx;
  text-align: center;
  color: #353535;
  min-width: 208rpx;
  height: 120rpx;
  background: #FFFFFF;
  border-radius: 10rpx 10rpx 10rpx 10rpx;
  opacity: 1;
  border: 1rpx solid #DFDFDF;
}

.infoBox.choose {
  color: #F1F8FF;
  background: #256BF5;
}

.infoBox-ysb {
  border-radius: 0rpx 5rpx 0rpx 5rpx;
  opacity: 1;
  color: #FFFFFF;
  font-size: 16rpx;
  
  font-weight: 400;
  line-height: 27rpx;
  position: relative;
  top: -10rpx;
  right: -135rpx;
  width: 75rpx;
  height: 27rpx;
  background: linear-gradient(90deg, #FF5745 0%, #FFAE00 100%);
  opacity: 1;
}

.paymentListBox {
  /* 实现自动换行 */
  width: calc(100% - 40rpx);
  min-height: 140rpx;
  display: flex;
  flex-direction: row;
  padding: 0 20rpx 0 20rpx;
}

.infoBox-text {
  min-width: 112rpx;
  height: 40rpx;
  font-size: 24rpx;
  
  font-weight: 400;
  line-height: 40rpx;
}

.infoBox-pay {
  min-width: 160rpx;
  height: 45rpx;
  font-size: 32rpx;
  
  font-weight: bold;
  color: #353535;
  line-height: 45rpx;
}

.infoBox-btn {
  position: absolute;
  bottom: 45rpx;
  left: 20rpx;
  font-size: 34rpx;
  font-weight: 400;
  color: #FFFFFF;
  line-height: 50rpx;
  width: 710rpx !important;
  height: 80rpx;
  background: linear-gradient(90deg, #458BFF 0%, #08D7AE 100%);
  box-shadow: 0rpx 3rpx 10rpx 1rpx rgba(37, 107, 245, 0.2);
  opacity: 1;
  border-radius: 40rpx 40rpx 40rpx 40rpx;
}

.infoBox.choosePay {
  border: 2rpx solid #4768F3;
}

.backWihte {
  background-color: #fff;
  width: 710rpx;
  min-height:96rpx;
  background: #FFFFFF;
  border-radius: 20rpx 20rpx 20rpx 20rpx;
  opacity: 1;
  margin-left: 20rpx;
  margin-bottom: 20rpx;
}

.infoBox-money {
  flex-direction: row;
  display: flex;
  padding: 23rpx 0;
  margin-top: 20rpx;
  background-color: #fff;
  margin:0 20rpx ;
  border-radius: 20rpx 20rpx 20rpx 20rpx;
}