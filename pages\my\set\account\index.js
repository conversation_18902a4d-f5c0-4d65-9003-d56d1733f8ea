// pages/my/set/account/index.js
var app = getApp();
Page({

  /**
   * 页面的初始数据
   */
  data: {
    phone:''
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {

  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    let reg=/(\d{3})\d{4}(\d{4})/;
    let tel1 = app.globalData.userInfo.phone.replace(reg, "$1****$2")
    this.setData({
      phone: tel1
    })
  },
  gotoPage(e){
    let url = e.currentTarget.dataset.url;
    console.log(url)
    if(!url){
      return;
    }
    wx.navigateTo({
      url: url,
    })
  }
})