// 获取应用实例
var util = require('../../../../utils/util')
var api = require('../../../../config/api.js')
import { PLATE_COLOR } from '../../../../utils/dict'

Page({
  data: {
    index_type: 0, //车辆类型的索引
    index_colour: 0, //车辆颜色的索引
    vehicleType: [],
    vehicleColour: [],
    defaultVehicle: false,
    isKeyboard: false,
    isNumberKB: true,
    tapNum: false,
    showNewPower: true, //是否显示‘新’
    // keyboardNumber: "1234567890ABCDEFGHJKLMNPQRSTUVWXYZ港澳学",
    keyboardNumber: '1234567890QWERTYUP港澳ASDFGHJKL学ZXCVBNM',
    keyboard1: '渝川京沪粤津冀晋蒙辽吉黑苏浙皖闽赣鲁豫鄂湘桂琼贵云藏陕甘青宁新',
    inputPlates: {
      index0: '桂',
      index1: '',
      index2: '',
      index3: '',
      index4: '',
      index5: '',
      index6: '',
      index7: '',
    },
    inputOnFocusIndex: '',
    carNum: '',
  },
  onShow() {
    wx.hideHomeButton()
    this.getVehicleColour()
    this.getVehicleType()
  },
  getVehicleColour() {
    var that = this
    util.showLoading('正在加载…')
    util.request(api.getVehicleColour, {}, 'GET').then(function (res) {
      if (res.code == '0') {
        const list = res.result.map(item => {
          item.title = PLATE_COLOR[item.vehicleColourType] ? PLATE_COLOR[item.vehicleColourType] : item.title
          return item
        })
        list.unshift({
          title: '无',
          vehicleColourType: '',
        })
        that.setData({
          vehicleColour: list,
        })
        wx.hideLoading()
      } else {
        wx.hideLoading()
        util.showToast(res.message)
      }
    })
  },
  bindPickerChange_colour: function (e) {
    this.setData({
      index_colour: e.detail.value,
    })
  },
  getVehicleType() {
    var that = this
    util.showLoading('正在加载…')
    util.request(api.getVehicleType, {}, 'GET').then(function (res) {
      if (res.code == '0') {
        that.setData({
          vehicleType: res.result,
        })
        wx.hideLoading()
      } else {
        wx.hideLoading()
        util.showToast(res.message)
      }
    })
  },
  bindPickerChange_type: function (e) {
    this.setData({
      index_type: e.detail.value,
    })
  },
  isVehicleNumber(vehicleNumber) {
    var xxreg =
      /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}(([0-9]{5}[DABCEFGHJK]$)|([DABCEFGHJK][A-HJ-NP-Z0-9][0-9]{4}$))/ // 2021年新能源车牌不止有DF
    var creg =
      /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}[A-HJ-NP-Z0-9]{4}[A-HJ-NP-Z0-9挂学警港澳]{1}$/
    if (vehicleNumber.length == 7) {
      return creg.test(vehicleNumber)
    } else if (vehicleNumber.length == 8) {
      return xxreg.test(vehicleNumber)
    } else {
      return false
    }
  },
  addCar() {
    var that = this
    var data = that.data
    var carNum = data.carNum
    // var vehicleType = data.vehicleType[data.index_type]['vehicleType'];12.5去掉车辆类型
    if (!that.isVehicleNumber(carNum)) {
      util.showToast('输入的车牌号不正确！')
      return
    }
    var vehicleColourType = data.vehicleColour[data.index_colour]['vehicleColourType']
    if (!vehicleColourType) {
      util.showToast('请选择车牌颜色！')
      return
    }
    util.showLoading('正在加载…')
    //校验车牌号
    util
      .request(
        api.addCar,
        {
          vehicleColourType: vehicleColourType,
          vehicleType: '', //vehicleType,12.5去掉车辆类型
          plateNo: carNum,
          defaultVehicle: data.defaultVehicle,
        },
        'POST'
      )
      .then(function (res) {
        if (res.code == '0') {
          wx.hideLoading()
          util.showToast('添加成功！')
          var clock = setTimeout(function () {
            wx.navigateBack({
              delta: 1,
            })
            clearInterval(clock)
          }, 500)
        } else {
          wx.hideLoading()
          util.showToast(res.message)
        }
      })
  },
  inputClick: function (t) {
    //点击车牌输入框
    var that = this
    var id = t.target.dataset.id
    if (id == 0) {
      that.setData({
        inputOnFocusIndex: id,
        isNumberKB: true,
        isKeyboard: true,
        tapNum: false,
      })
    } else if (id == 1) {
      that.setData({
        inputOnFocusIndex: id,
        isNumberKB: false,
        isKeyboard: true,
        tapNum: false,
      })
    } else if (id == 7) {
      that.setData({
        inputOnFocusIndex: id,
        isNumberKB: false,
        isKeyboard: true,
        tapNum: true,
        showNewPower: false,
      })
    } else if (id > 1) {
      that.setData({
        inputOnFocusIndex: id,
        isNumberKB: false,
        isKeyboard: true,
        tapNum: true,
      })
    }
  },
  tapKeyboard: function (t) {
    //键盘点击事件
    t.target.dataset.index
    var a = t.target.dataset.val
    switch (parseInt(this.data.inputOnFocusIndex)) {
      case 0:
        this.setData({
          'inputPlates.index0': a,
          inputOnFocusIndex: '1',
        })
        break
      case 1:
        this.setData({
          'inputPlates.index1': a,
          inputOnFocusIndex: '2',
        })
        break
      case 2:
        this.setData({
          'inputPlates.index2': a,
          inputOnFocusIndex: '3',
        })
        break
      case 3:
        this.setData({
          'inputPlates.index3': a,
          inputOnFocusIndex: '4',
        })
        break
      case 4:
        this.setData({
          'inputPlates.index4': a,
          inputOnFocusIndex: '5',
        })
        break
      case 5:
        this.setData({
          'inputPlates.index5': a,
          inputOnFocusIndex: '6',
        })
        break
      case 6:
        this.setData({
          'inputPlates.index6': a,
          inputOnFocusIndex: '7',
        })
        break
      case 7:
        this.setData({
          'inputPlates.index7': a,
          inputOnFocusIndex: '7',
          showNewPower: false,
        })
    }
    var n =
      this.data.inputPlates.index0 +
      this.data.inputPlates.index1 +
      this.data.inputPlates.index2 +
      this.data.inputPlates.index3 +
      this.data.inputPlates.index4 +
      this.data.inputPlates.index5 +
      this.data.inputPlates.index6 +
      this.data.inputPlates.index7
    console.log('车牌号:', n)
    this.data.carNum = n
    this.checkedKeyboard()
  },
  //点击键盘上的完成按钮
  tapSpecBtna: function (t) {
    var that = this
    that.setData({
      isKeyboard: false,
      inputOnFocusIndex: '7',
    })
  },
  //键盘删除按钮点击事件
  tapSpecBtn: function (t) {
    var data = this.data
    console.log(t)
    console.log(t.target.dataset.index)
    switch (parseInt(data.inputOnFocusIndex)) {
      case 0:
        this.setData({
          'inputPlates.index0': '',
          inputOnFocusIndex: '0',
        })
        break
      case 1:
        this.setData({
          'inputPlates.index1': '',
          inputOnFocusIndex: '0',
        })
        break
      case 2:
        this.setData({
          'inputPlates.index2': '',
          inputOnFocusIndex: '1',
        })
        break
      case 3:
        this.setData({
          'inputPlates.index3': '',
          inputOnFocusIndex: '2',
        })
        break
      case 4:
        this.setData({
          'inputPlates.index4': '',
          inputOnFocusIndex: '3',
        })
        break
      case 5:
        this.setData({
          'inputPlates.index5': '',
          inputOnFocusIndex: '4',
        })
        break
      case 6:
        this.setData({
          'inputPlates.index6': '',
          inputOnFocusIndex: '5',
        })
        break
      case 7:
        this.setData({
          'inputPlates.index7': '',
          inputOnFocusIndex: '6',
        })
    }
    this.checkedKeyboard()
    var inputPlates = data.inputPlates
    var n =
      inputPlates.index0 +
      inputPlates.index1 +
      inputPlates.index2 +
      inputPlates.index3 +
      inputPlates.index4 +
      inputPlates.index5 +
      inputPlates.index6 +
      inputPlates.index7
    this.setData({
      'carNum': n,
    })
  },
  checkedKeyboard: function () {
    //键盘切换
    var t = this
    var inputOnFocusIndex = this.data.inputOnFocusIndex
    if (inputOnFocusIndex == 0) {
      t.setData({
        tapNum: false, //是否可以点击
        isNumberKB: true, //省和
      })
    }
    if (inputOnFocusIndex == 1) {
      t.setData({
        tapNum: false,
        isNumberKB: false,
      })
    }
    if (inputOnFocusIndex > 1) {
      t.setData({
        tapNum: true,
        isNumberKB: false,
      })
    }
  },
  switchChange: function (e) {
    this.setData({
      defaultVehicle: e.detail.value,
    })
  },
})
