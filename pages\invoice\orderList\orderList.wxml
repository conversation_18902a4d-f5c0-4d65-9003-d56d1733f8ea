<view class="saveOutView">
  <cu-custom bgColor="white-bg" contentTitle="开具发票" isBack="true"></cu-custom>
  <view class="header">
    <block wx:for="{{orderTypes}}" wx:key="id">
      <view class="order-type {{currentOrderType == item.id ? 'active' : ''}}" bindtap="changeOrderType" data-type="{{item.id}}">{{item.name}}</view>
    </block>
  </view>
  <view class="header">
    <view class="invoice-state active" data-state='able'>可开票订单</view>
  </view>
  <scroll-view scroll-y="true" class="scrollViewSa" bindscrolltolower="bindReachBottom">
    <block wx:for="{{list}}" wx:key="id">
      <view class="order" bindtap="changeCheckState" data-info="{{item}}" data-checkstate="{{item.checkstate}}" data-index="{{index}}">
        <view class="left" hidden="{{mode == 'other'}}">
          <image src="/image/{{item.checkstate ? 'yes-icon-select' : 'yes-icon'}}.png" />
        </view>
        <view class="middle columnView">
          <view class="name">{{item.parkName}}</view>
          <view class="address">{{item.plateNo}}</view>
          <view class="extra">
            <text>{{item.payTime}}</text>
            <text>{{item.discountMoneyText}}</text>
          </view>
        </view>
        <view class="right">
          <text>￥{{item.invoiceAmount}}</text>
          <!-- <text class="red" hidden="{{mode != 'other'}}">优惠订单</text> -->
        </view>
      </view>
    </block>
  </scroll-view>
  <view class="footer" hidden="{{currentInvoiceState == 'other'}}">
    <view class="total"><text>{{selectedList.length}}</text>个订单，共<text>{{invoiceAmout}}</text>元 <text wx:if="{{currentOrderType=='MONTHLY_PACKAGE'}}">此账单开具发票后，该次包期将不可取消</text>
    </view>
    <view class="control">
      <!-- <view class="checkbox" bindtap="batchSelected" data-type="month">
      <image src="/image/{{batchType == 'month' ? 'yes-icon-select' : 'yes-icon'}}.png" />本页全选
    </view> -->
      <view class="checkbox" bindtap="batchSelected" data-type="all">
        <image src="/image/{{selectedList.length == list.length ? 'yes-icon-select' : 'yes-icon'}}.png" />全部全选
      </view>
      <view style="flex: 1; display: flex; justify-content: flex-end;">
        <view class="btn" bindtap="gotoInvoiceTitle">下一步</view>
      </view>
    </view>
  </view>
</view>