// pages/vehicleReservation/component/reservation-record.js
Component({
  properties: {
    record: {}
  },
  data: {
  },
  lifetimes:{
attached(e){
  console.log('aaaa',this.data.record)
}
  },
  methods: {
    cancel: function(){
      this.triggerEvent('cancelReservation',this.data.record)
    },
    add:function(){
      this.triggerEvent('addReservation',this.data.record)
    },
    goXq:function(){
      this.triggerEvent('reservationInfo',this.data.record)
    }
  }
})
