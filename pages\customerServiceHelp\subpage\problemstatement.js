var util = require('../../../utils/util');
var api = require('../../../config/api.js');
var dateTimePicker = require('../../../utils/dateTimePicker.js');
Page({
  data: {
    show: {
      title: '',
      content: ''
    },
    type: '',
    sourceType: ['camera', 'album'],
    //问题咨询
    questionType: [], //问题咨询类型
    questionImgUrl: '', //问题咨询的base64
    showQImg: true, //是否显示问题图片
    selectQuestionType: 0, //选择问题自选类型
    allowClicking: false, //允许点击提交
    flag: false, //显示问题订单
    flagDetails: false, //显示问题订单详情
    questionValue: '',
    //反馈记录
    imgUrl: '',
    //反馈详情
    feedback: {},
    feedbackStateClass: {
      1: 'blue',
      2: 'green'
    },
    //反馈详情--停车订单申诉详情
    parkingAppealOrderDetails:{},
    parkingAppealOrdeClass: {//1-申诉中，2-申诉成功，3-申诉被拒
      1: 'blue',
      2: 'green',
      3: 'red',
    }
  },
  onLoad(options) {
    var type = options.type;
    this.setData({
      type: type
    })
    if (type == 'feedback') { //反馈记录详情
      this.getFeedbackInfo(options.id);
    } else if (type == 'ProblemConsultation') {
      this.getCtyp();
    }else if(type=="parkingAppealOrderDetails"){//反馈记录详情--停车订单详情
      this.getParkingAppealOrderDetails(options.id);
    }
     else {
     
    }
  },
  getCtyp: function (id) {
    var that = this;
    util.request(api.getCType, {}, 'GET').then(function (res) {
      if (res.code == '0') {
        wx.hideLoading();
        that.setData({
          questionType: res.result,
        });
      } else {
        wx.hideLoading();
        util.showToast(res.message)
      }
    });
  },
  getAtyp: function (id) {
    var that = this;
    util.request(api.getAType, {}, 'GET').then(function (res) {
      if (res.code == '0') {
        wx.hideLoading();
        that.setData({
          appealType: res.result,
        });
      } else {
        wx.hideLoading();
        util.showToast(res.message)
      }
    });
  },
  getParkingAppealOrderDetails: function (id) {
    var that = this;
    util.request(api.getParkingAppealOrderDetails + id, {}, 'GET').then(function (res) {
      if (res.code == '0') {
        wx.hideLoading();
        that.setData({
          parkingAppealOrderDetails: res.result,
        });
      } else {
        wx.hideLoading();
        util.showToast(res.message)
      }
    });
  },
  getFeedbackInfo: function (id) {
    var that = this;
    util.request(api.getFeedbackDetail + id, {}, 'GET').then(function (res) {
      if (res.code == '0') {
        wx.hideLoading();
        that.setData({
          feedback: res.result,
        });
      } else {
        wx.hideLoading();
        util.showToast(res.message)
      }
    });
  },
  selectQuestionType: function (e) {
    var id = e.currentTarget.id;
    this.setData({
      selectQuestionType: id,
      questionValue: ''
    })
  },
  selectOrder: function () {
    wx.navigateTo({
      url: '/pages/customerServiceHelp/subpage/order/index?type=' + this.data.selectQuestionType,
    })
  },
  bindFormSubmit: function (e) {
    var that = this;
    var data = this.data;
    if (data.allowClicking) {
      //接口还没有
      console.log(e.detail.value.textarea)
    }
  },
  questionInput: function (e) {
    var flag = false;
    var value = e.detail.value;
    if (value.length > 0) {
      flag = true;
    }
    this.setData({
      questionValue: value,
      allowClicking: flag
    })
  },
  appealInput: function (e) {
    var flag = false;
    var value = e.detail.value;
    if (value.length > 0) {
      flag = true;
    }
    this.setData({
      appealRemark: value,
      allowClicking: flag
    })
  },
  chooseAppealType: function (e) {
    var id = e.currentTarget.id;
    var remark = e.currentTarget.dataset.remark;
    var flag = e.currentTarget.dataset.flag;
    this.setData({
      selectAppealType: id,
      describe: remark
    })
  },
  showActionSheet: function () {
    const that = this
    wx.showActionSheet({
      itemList: ['拍照', '相册'],
      itemColor: '',
      //成功时回调
      success: function (res) {
        if (!res.cancel) {
          /*
           res.tapIndex返回用户点击的按钮序号，从上到下的顺序，从0开始
           比如用户点击本例中的拍照就返回0，相册就返回1
           我们res.tapIndex的值传给chooseImage()
          */
          that.chooseImage(res.tapIndex)
        }
      },
      //失败时回调
      fail: function (res) {
        console.log('获取图片调用失败')
      },
      complete: function (res) {},
    })
  },
  chooseImage(tapIndex) {
    const checkeddata = true
    const that = this
    wx.chooseImage({
      //count表示一次可以选择多少照片
      count: 1,
      //sizeType所选的图片的尺寸，original原图，compressed压缩图
      sizeType: ['original'],
      //如果sourceType为camera则调用摄像头，为album时调用相册
      sourceType: [that.data.sourceType[tapIndex]],
      success(res) {
        // tempFilePath可以作为img标签的src属性显示图片
        const tempFilePaths = res.tempFilePaths
        for (var x = 0; x < tempFilePaths.length; x++) {
          wx.getFileSystemManager().readFile({
            filePath: tempFilePaths[x], //选择图片返回的相对路径
            encoding: "base64", //这个是很重要的
            success: res => { //成功的回调//返回base64格式
              console.log('data:image/png;base64,' + res.data)
              that.upImg(res.data)
            }
          })
        }
        that.setData({
          showQImg: false,
          showImgUrl: tempFilePaths
        })
      }
    })
  },
  upImg: function (img) {
    var fileBase = 'data:image/png;base64,' + img;
    var that = this;
    util.showLoading('上传中')
    var fileType = 4;
    var type = that.data.type;
    if (type == "ProblemConsultation") {
      fileType = 3
    }
    util.request(api.uploadPicture, {
      fileBase: fileBase,
      fileType: fileType
    }, 'POST').then(function (res) {
      if (res.code == '0') {
        wx.hideLoading();
        if (type == "ProblemConsultation") {
          that.setData({
            questionImgUrl: res.result
          })
        } else {
          that.setData({
            appealImgUrl: res.result
          })
        }
      } else {
        wx.hideLoading();
        util.showToast(res.message)
      }
    })
  },
  addProblemConsultation: function () {
    var that = this;
    util.showLoading("正在加载...")
    util.request(api.addProblemConsultation, {
      content: that.data.questionValue,
      type: that.data.selectQuestionType,
      picUrls: [that.data.questionImgUrl]
    }, 'POST').then(function (res) {
      if (res.code == '0') {
        wx.hideLoading();
        wx.showModal({
          title: '提示',
          content: '提交成功！',
          showCancel: false,
          success(res) {
            if (res.confirm) {
              wx.navigateBack({
                delta: 1
              })
            }
          }
        })

      } else {
        wx.hideLoading();
        util.showToast(res.message)
      }
    });
  }
})