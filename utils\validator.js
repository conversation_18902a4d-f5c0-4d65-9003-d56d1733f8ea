/**
 * 手机号验证工具类
 * 包含手机号相关的验证函数和正则表达式
 */

/**
 * 手机号脱敏显示
 * @param {string} phone 手机号
 * @returns {string} 脱敏后的手机号
 */
function maskPhone(phone) {
  if (!phone || typeof phone !== 'string') {
    return ''
  }
  const reg = /(\d{3})\d{4}(\d{4})/
  return phone.replace(reg, '$1****$2')
}

/**
 * 验证中国大陆手机号
 * 支持所有三大运营商的号段，包括最新的号段
 * @param {string} phone 手机号
 * @returns {boolean} 是否为有效手机号
 */
function validatePhone(phone) {
  if (!phone || typeof phone !== 'string') {
    return false
  }

  // 中国大陆手机号正则表达式（更新至2024年）
  // 移动：134-139、147、148、150-152、157-159、172、178、182-184、187-188、195、197-198
  // 联通：130-132、145、146、155-156、166、167、171、175-176、185-186、196
  // 电信：133、149、153、173-174、177、180-181、189、191、193-194、199
  // 虚拟运营商：170
  const phoneRegex = /^1(3[0-9]|4[01456789]|5[0-35-9]|6[2567]|7[0-8]|8[0-9]|9[0-35-9])\d{8}$/

  return phoneRegex.test(phone)
}

module.exports = {
  maskPhone,
  validatePhone,
}
