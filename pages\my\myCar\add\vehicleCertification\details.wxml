<view class='saveOutView'>
  <cu-custom bgColor="white-bg" contentTitle="车辆认证" isBack="true"></cu-custom>
  <scroll-view class="scrollViewSa" scroll-y="true">
    <view class="typeBox t{{info.reviewState}}">{{info.reviewState=='2'?'认证失败':'认证审核中…'}}</view>
    <view class="reasonBox" wx:if="{{info.reviewState=='2'}}">{{info.reviewStateContent}}</view>
    <view class="carBox">
      <text style="margin: 0 40rpx 0 30rpx;">认证车牌</text><text>{{carNum}}</text>
    </view>
    <view class="carBox columnView">
      <view class="form-row">
        <view class="form-label ">车主姓名</view>
        <view class="form-value">
          {{info.name}}
        </view>
      </view>
      <view class="form-row">
        <view class="form-label ">手机号码</view>
        <view class="form-value">
          {{info.phone}}
        </view>
      </view>
      <view class="form-row" style="border: none;">
        <view class="form-label ">身份证号</view>
        <view class="form-value">
          {{info.identification}}
        </view>
      </view>
    </view>
    <view class="carBox p">
      <view class="title">行驶证与车辆照片</view>
      <view class="wordBox">
        <view bindtap='showActionSheet' id='frontFile' style="background: url({{info.frontFile}}) no-repeat center;background-size: 100%;" class="imgBox">
        </view>
        <view class="titleBox">行驶证正面</view>
      </view>
      <view class="wordBox">
        <view bindtap='showActionSheet' id='contraryFile' style="background: url({{info.contraryFile}}) no-repeat center;background-size: 100%;" class="imgBox">
        </view>
        <view class="titleBox">行驶证副面</view>
      </view>
      <view class="wordBox">
        <view bindtap='showActionSheet' id='vehicleFile' style="background: url({{info.vehicleFile}}) no-repeat center;background-size: 100%;" class="imgBox">
        </view>
        <view class="titleBox">车头照片</view>
      </view>
    </view>
    <view class="box_convention_button">
      <button class="convention_button " wx:if="{{info.reviewState=='2'}}" bindtap="goAddPage">修改信息 </button>
    </view>
  </scroll-view>
</view>