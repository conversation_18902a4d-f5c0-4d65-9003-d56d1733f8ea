<!--pages/parkingSpaceInquiry/index.wxml-->

<map
  id="myMap"
  class="map"
  latitude="{{latitude}}"
  longitude="{{longitude}}"
  joinCluster="true"
  scale="{{scale}}"
  bindmarkertap="markertap"
  bindcallouttap="markertap"
  bindupdated="isMapLoaded"
  show-location="true"
  style="width: {{swidth}}px; height: {{sheight}}px;"
  bindtap="modifyLocation"
  bindregionchange="getNeedShowMarkersInMap"
  markers="{{markers}}"
>
  <cover-view slot="callout">
    <block wx:for="{{markers}}" wx:key="this">
      <cover-view
        class="customCallout rowView"
        marker-id="{{item.id}}"
        wx:if="{{item.type=='PARKING_LOT'||item.type=='PARKING_SPACE'||(item.dataSource=='HIK'&&item.type == 'CHARGE')}}"
      >
        <cover-image class="icon" wx:if="{{item.bubbleIcon}}" src="{{item.bubbleIcon}}"></cover-image>
        <cover-view class="content" wx:if="{{item.dataSource=='HIK'&&item.type == 'CHARGE'}}"
          ><!--智慧（自营）充电桩显示 快充余位/总数 普通的充点电桩显示水滴-->
          快充 {{item.surplus}}/{{item.total}}
        </cover-view>
        <cover-view class="content" wx:else
          ><!--智慧车场（场库车场及泊位车场）地图显示余位气泡位显示：余位/总数 社会面信息显示水滴标识-->
          余位 {{item.surplus}}/{{item.total}}
        </cover-view>
      </cover-view>
    </block>
  </cover-view>
  <view bindtap="goBack" class="zhtc-back" style="top:{{jnTop}}rpx;">
    <image src="../../image/zhtc-back.png" style="width: 35rpx; height: 35rpx"></image>
  </view>
  <view
    class="view-search s-view-search"
    style="top:calc(70rpx + {{jnTop}}rpx) "
    wx:if="{{is_selected  == 'PARKING_LOT' }}"
  >
    <view class="view-search-d flex-wrp" bindtap="goSearchPage">
      <image class="Icon-search" src="../../image/merry-search.png"></image>
      <view class="view-search-text flex-wrp">{{keyword}}</view>
    </view>
    <view class="list-tool">
      <block wx:for="{{parkingLotFilters}}" wx:key="index" scroll-y="true">
        <view
          id="{{item.value}}"
          class="list-tool-select {{parkingLotFilterValue == item.value ? 'selected' : ''}} "
          bindtap="selectFilterItem"
          >{{item.label}}</view
        >
      </block>
    </view>
  </view>
  <view class="view-search" style="top:calc(70rpx + {{jnTop}}rpx) " wx:else>
    <view class="view-search-d flex-wrp" bindtap="goSearchPage">
      <image class="Icon-search" src="../../image/merry-search.png"></image>
      <view class="view-search-text flex-wrp">{{keyword}}</view>
    </view>
  </view>
  <view
    id="leftToolBox"
    class="view-search-left columnView"
    style="top:calc({{is_selected  == 'PARKING_LOT' ? '225rpx':'175rpx' }} + {{jnTop}}rpx) "
    wx:if="{{!isV}}"
  >
    <image
      id="PARKING_LOT"
      class="view-search-select view-search-left-btn"
      bindtap="selectItem"
      src="{{is_selected  == 'PARKING_LOT' ? btnIcon.s.PARKING_LOT : btnIcon.n.PARKING_LOT}}"
      mode="scaleToFill"
      style="margin-top: 15rpx"
    ></image>
    <image
      id="FILLING_STATION"
      class="view-search-select view-search-left-btn"
      bindtap="selectItem"
      src="{{is_selected  == 'FILLING_STATION' ? btnIcon.s.FILLING_STATION : btnIcon.n.FILLING_STATION}}"
    ></image>
    <image
      id="CHARGE"
      class="view-search-select view-search-left-btn"
      bindtap="selectItem"
      src="{{is_selected  == 'CHARGE' ? btnIcon.s.CHARGE  : btnIcon.n.CHARGE}}"
    ></image>
    <image
      id="TOILET"
      class="view-search-select view-search-left-btn"
      bindtap="selectItem"
      src="{{is_selected  == 'TOILET' ? btnIcon.s.TOILET : btnIcon.n.TOILET}}"
    ></image>
    <image
      id="SCENIC_AREA"
      class="view-search-select view-search-left-btn"
      bindtap="selectItem"
      src="{{is_selected  == 'SCENIC_AREA' ? btnIcon.s.SCENIC_AREA : btnIcon.n.SCENIC_AREA}}"
    ></image>
    <image
      style="width: 42rpx; height: 71rpx; margin: 7rpx 8rpx 15rpx 8rpx"
      src="../../image/button_kefu.png"
      bindtap="goCustomerServiceHelp"
    ></image>
  </view>
  <view
    id="centerBox"
    class="view-search-left-dw"
    style="bottom:15rpx;{{changePosition? 'left: 30rpx;':''}}"
    bindtap="setCenter"
  >
    <image style="width: 44rpx; height: 44rpx; margin: 15rpx" src="../../image/button_dinwei.png"></image>
  </view>
</map>
<view
  class="tool-px-xx"
  wx:if="{{isShowPxSelect}}"
  style="{{listDivHeight  == '501rpx' ? 'bottom:475rpx' : 'bottom:986rpx'}}"
>
  <view class="tool-px-xx-d" id="DISTANCE" bindtap="selectPx" data-text="距离最近">距离最近</view>
  <view class="tool-px-xx-d" id="SURPLUS" bindtap="selectPx" data-text="余位最多">余位最多</view>
  <view class="tool-px-xx-d" id="DISCOUNT" bindtap="selectPx" data-text="优惠车场">优惠车场</view>
</view>
<view class="listDiv columnView" style="height: {{listDivHeight}};">
  <!-- <view style="height: 47rpx;" catchtap="addListHeight">
    <view class="moveBtn"></view>
  </view> -->
  <view class="tool">
    <view class="tool-px" bindtap="openPXTool">
      <view class="tool-px-t">{{orderByText}}</view>
      <view class="icon-sjx-down"></view>
    </view>
    <view class="tool-tip"> <image src="../../image/iMarker-min.png"></image>起点为当前地图所选位置 </view>
    <!-- <view class="tool-text">附近找到{{tcdNum}}个停车点</view>
    <view class="tool-btn" bindtap="addListHeight" data-type="0">{{btntext}}</view> -->
  </view>
  <scroll-view
    scroll-y="true"
    class="{{listDivHeight  == '501rpx' ? 'scroll-view_H' : 'scroll-view_H_Open'}}"
    bindscrolltolower="bindReachBottom"
  >
    <block wx:for="{{pslist}}" wx:key="index" scroll-y="true">
      <view class="listDiv-d" bindtap="goxq" id="{{item.id}}" data-type="{{item.type}}">
        <view class="listDiv-d-left">
          <view class="rowView">
            <view class="listDiv-search-name">{{item.name}}</view>
            <view class="listDiv-search-dc-by" wx:if="{{item.monthly}}">可包月</view>
          </view>
          <view class="listDiv-search-name-xq">{{item.address}}</view>
          <view
            class="listDiv-search-dc"
            wx:if="{{item.dataSource=='HIK'||item.freeTimeDesc!=''||item.monthly||item.discount||item.isHolidayFree}}"
          >
            <view class="listDiv-search-dc-num">
              <view class="listDiv-search-yw" wx:if="{{item.dataSource=='HIK'}}">余位 {{item.surplus}}/</view>
              <view class="listDiv-search-dc-zw" wx:if="{{item.dataSource=='HIK'}}">{{item.total}}</view>
              <view class="listDiv-search-dc-zw" wx:else>停车位{{item.total}}</view>
              <view class="listDiv-search-dc-tip" wx:if="{{item.freeTimeDesc!=''}}"> {{item.freeTimeDesc}} </view>
              <view class="listDiv-search-dc-yhj" catchtap="goxq" wx:if="{{item.discount}}">优惠停车</view>
              <view class="holiday-free-tag" wx:if="{{item.isHolidayFree}}">{{item.holidayDesc}}</view>
            </view>
          </view>
        </view>
        <view class="listDiv-d-right">
          <view
            class="listDiv-d-dhBtn"
            catchtap="openMapApp"
            data-lat="{{item.latitude}}"
            data-long="{{item.longitude}}"
            data-name="{{item.name}}"
            data-address="{{item.address}}"
          >
            <image src="../../image/dh.png"></image>
            <text>导航</text>
          </view>
          <view class="listDiv-search-dw">{{item.distance}}km</view>
        </view>
      </view>
    </block>
    <view wx:if="{{showNullTip}}" class="njgtip">无符合查询条件数据</view>
    <view wx:if="{{showNullMoreTip}}" class="njgtip">没有更多信息了</view>
  </scroll-view>
</view>
