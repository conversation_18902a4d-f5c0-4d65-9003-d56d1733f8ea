/*模态框*/
/*使屏幕变暗  */
.background_screen {
  position: fixed;
  width: 100%;
  top: 0;
  bottom: 0;
  background: #000;
  opacity: 0.6;
  overflow: hidden;
  z-index: 1000;
  color: #fff;
}

/*对话框 */
.attr_box {
  background: #FFFFFF;
  opacity: 1;
  max-height: 1007rpx;
  height: auto;
  width: 100%;
  position: fixed;
  overflow: hidden;
  bottom: 0;
  left: 0;
  z-index: 2000;
  background: #F6F6F6;
  /* background: rgba(66, 66, 66, .6); */
  padding-top: 30rpx;
  padding-bottom: 60rpx;
  box-sizing: border-box;

}


.dialog-box {
  width: 100%;
  height: 100%;
  /* background-color: pink; */
}

.dialog-head {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  height: 60rpx;
  /* background-color: rgb(215, 255, 192); */
}

.dialog-title {
  width: 80%;
  height: 48rpx;
  font-size: 34rpx;
  font-weight: bold;
  color: #353535;
  line-height: 54rpx;
  text-align: center;
}

.close2ImgBox {
  width: 10%;
  height: 100%;
  display: flex;
  align-items: center;
}

.close2Img {
  width: 25rpx;
  height: 25rpx;
}

.dialog-content {
  height: calc(100% - 60rpx);
  box-sizing: border-box;
}

/* 主体内容 */
.box {
  width: calc(100% - 100rpx);
  background: #FFFFFF;
  box-shadow: 0rpx 10rpx 0rpx 1rpx rgba(228, 234, 248, 0.16);
  border-radius: 20rpx 20rpx 20rpx 20rpx;
  opacity: 1;
  margin: 20rpx 20rpx 0 20rpx;
  padding: 30rpx;
}
.left{
  width: calc(100% - 160rpx);
}
.one {
 
  min-height: 40rpx;
  font-size: 30rpx;
  font-weight: bold;
  color: #000000;
  line-height: 40rpx;
  margin-bottom: 10rpx;
}

.two {

  min-height: 37rpx;
  font-size: 26rpx;
  font-weight: 400;
  color: #353535;
  line-height: 37rpx;
  margin-bottom: 10rpx;
}
.three{
  margin-bottom: 10rpx;
}
.three .text {
  min-height: 37rpx;
  font-size: 26rpx;
  font-weight: 400;
  color: #256BF5;
  line-height: 37rpx;
}

.typeBox {
  width: 140rpx;
  height: 38rpx;
  border-radius: 5rpx 5rpx 5rpx 5rpx;
  opacity: 1;
  line-height:  38rpx;
  font-size: 24rpx;
  font-weight: 400;
  text-align: center;
  margin-left: 20rpx;
}

.typeBox.typeBox1 {
  color: #41E0AC;
  background: #D9F9EE;
}

.typeBox.typeBox2 {
  color: #DA5937;
  background: #FFE7D5;
}

.typeBox.typeBox0 {
  color: #256BF5;
  background: #D3E1FD;
}
.right{
  width: 160rpx;
}
.right .text{
  font-size: 26rpx;
  font-weight: 400;
  color: #256BF5;
  line-height: 36rpx;
  margin: auto 0 auto auto;
  text-align: left;
}
.rowView {
  flex-direction: row;
  display: flex;
}

.columnView {
  flex-direction: column;
  display: flex;
}
/* 向右剪头 */
.icon-jt-left {
  width: 15rpx;
  height: 15rpx;
  opacity: 1;
  background-color: transparent;
  /* 模块背景为透明 */
  border-color: #888888;
  border-style: solid;
  border-width: 4rpx 4rpx 0 0;
  transform: rotate(45deg);
  /*箭头方向可以自由切换角度*/
  margin: auto;
}